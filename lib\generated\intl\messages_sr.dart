// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a sr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'sr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("DODAJ PRODAJU"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIJA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale":
            MessageLookupByLibrary.simpleMessage("Prodaja na POS uređaju"),
        "PRICE": MessageLookupByLibrary.simpleMessage("CENA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NAZIV PROIZVODA"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Panel za Prijavu"),
        "QTY": MessageLookupByLibrary.simpleMessage("KOLIČINA"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("UKUPNA VREDNOST"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Titula Korisnika"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("O Aplikaciji"),
        "accountName": MessageLookupByLibrary.simpleMessage("Ime Računa"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Broj Računa"),
        "action": MessageLookupByLibrary.simpleMessage("Akcija"),
        "add": MessageLookupByLibrary.simpleMessage("Dodaj"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Dodaj brend"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Dodaj kategoriju"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Dodaj kupca"),
        "addDescription": MessageLookupByLibrary.simpleMessage("Dodaj opis..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Dodajte dokumente"),
        "addItem": MessageLookupByLibrary.simpleMessage("Dodaj stavku"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Dodaj kategoriju proizvoda"),
        "addNew": MessageLookupByLibrary.simpleMessage("Dodaj Novo"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Dodaj Novog Korisnika"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Dodaj proizvod"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("Uspešno Dodato"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Dodaj dobavljača"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Dodaj jedinicu"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/Ažuriraj listu troškova"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/Ažuriraj listu prihoda"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Dodaj Ulogu Korisnika"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Dodavanje serijskog broja?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresa"),
        "all": MessageLookupByLibrary.simpleMessage("Sve"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Sve osnovne funkcije"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Već imate nalog?"),
        "amount": MessageLookupByLibrary.simpleMessage("Iznos"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Podrška za Android i iOS Aplikacije"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Da li želite kreirati ovu ponudu?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Da li želite da obrišete ovog kupca?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Da li želite da obrišete ovaj proizvod"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Da li želite da obrišete ovu ponudu?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Da li želite da vratite ovu prodaju?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankovni računi"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Podaci o Banci"),
        "bankName": MessageLookupByLibrary.simpleMessage("Ime Banke"),
        "between": MessageLookupByLibrary.simpleMessage("Između"),
        "billTo": MessageLookupByLibrary.simpleMessage("Račun na ime:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Ime Odeljenja"),
        "brand": MessageLookupByLibrary.simpleMessage("Brend"),
        "brandName": MessageLookupByLibrary.simpleMessage("Ime brenda"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Poslovna kategorija"),
        "buy": MessageLookupByLibrary.simpleMessage("Kupi"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kupi Premium Plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Kupi SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Otkaži"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacitet"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Gotovina i banka"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Gotovina u rukama"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorije"),
        "category": MessageLookupByLibrary.simpleMessage("Kategorija"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Naziv kategorije"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Iznos za povrat"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Promenjivi iznos"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Proverite garanciju"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Izaberite plan"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Naplata duga >"),
        "color": MessageLookupByLibrary.simpleMessage("Boja"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Ime kompanije"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Adresa kompanije"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Opis kompanije"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Email adresa kompanije"),
        "companyName": MessageLookupByLibrary.simpleMessage("Ime kompanije"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Broj telefona kompanije"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "URL adrese web sajta kompanije"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Potvrdite lozinku"),
        "continu": MessageLookupByLibrary.simpleMessage("Nastavak"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konvertuj u prodaju"),
        "create": MessageLookupByLibrary.simpleMessage("Kreiraj"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Kreiraj plaćanje"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Kreirao"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreativni centar"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Trenutni plan"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Prilagođavanje Brendiranja Faktura"),
        "customer": MessageLookupByLibrary.simpleMessage("Kupac"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Dugovanja kupca"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Fakture klijenta"),
        "customerList": MessageLookupByLibrary.simpleMessage("Lista kupaca"),
        "customerName": MessageLookupByLibrary.simpleMessage("Ime kupca"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kupac meseca"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tip klijenta"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Kupac: Kupac koji dolazi"),
        "customers": MessageLookupByLibrary.simpleMessage("Kupci"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Dnevna naplata"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Dnevna prodaja"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Dnevne transakcije"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Nadzorna tabla"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum i vreme"),
        "dealer": MessageLookupByLibrary.simpleMessage("Trgovac"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Cena za trgovca"),
        "delete": MessageLookupByLibrary.simpleMessage("Obriši"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Troškovi dostave"),
        "description": MessageLookupByLibrary.simpleMessage("Opis"),
        "details": MessageLookupByLibrary.simpleMessage("Detalji >"),
        "discount": MessageLookupByLibrary.simpleMessage("Popust"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Cena sa popustom"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Preuzmi PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Duguje"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Duguje se iznos"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Dugovani iznos će se prikazati ovde ako postoji"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Inkaso dugovanja"),
        "dueList": MessageLookupByLibrary.simpleMessage("Lista dugovanja"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcije duga"),
        "edit": MessageLookupByLibrary.simpleMessage("Izmeni"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Izmeni/Dodaj serijski broj:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Uredite svoj profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Unesite iznos"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Unesite ime brenda"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv kategorije"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Unesite opis kompanije"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Unesite email adresu kompanije"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Unesite broj telefona kompanije"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Unesite URL adresu web sajta kompanije"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Unesite ime kupca"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Unesite cenu za trgovca"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Unesite cenu sa popustom"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Unesite kategoriju troškova"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Unesite datum troška"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Unesite kategoriju prihoda"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Unesite datum prihoda"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Unesite ime proizvođača"),
        "enterName": MessageLookupByLibrary.simpleMessage("Unesite ime"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Unesite ime"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Unesite napomenu"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Unesite početno stanje"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos plaćanja"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Unesite Šifru"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos za plaćanje"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Unesite cenu"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Unesite kapacitet proizvoda"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Unesite šifru proizvoda"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Unesite boju proizvoda"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Unesite ime proizvoda"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Unesite količinu proizvoda"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Unesite veličinu proizvoda"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Unesite tip proizvoda"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Unesite jedinicu proizvoda"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Unesite težinu proizvoda"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Unesite nabavnu cenu"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Unesite referentni broj"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Unesite prodajnu cenu"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Unesite serijski broj"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Unesite sadržaj poruke"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Unesite količinu zaliha"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Unesite ID Transakcije"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Unesite ime jedinice"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Unesite Ime Uloge Korisnika"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Unesite Titulu Korisnika"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Unesite garanciju"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Unesite veleprodajnu cenu"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Unesite vašu adresu"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Unesite adresu vaše kompanije"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Unesite ime vaše kompanije"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Unesite ime vaše kompanije"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Unesite vašu email adresu"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Unesite vašu lozinku"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Ponovo unesite vašu lozinku"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Unesite vaš broj telefona"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Unesite ime vaše prodavnice"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv kategorije"),
        "expense": MessageLookupByLibrary.simpleMessage("Trošak"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Datum troška"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detalji troškova"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Trošak za"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Lista kategorija troškova"),
        "expenses": MessageLookupByLibrary.simpleMessage("Troškovi"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top pet proizvoda koji se najviše kupuju ovog meseca"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Za Neograničene Upotrebe"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Zaboravili ste lozinku?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Besplatna Rezervna Kopija Podataka"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Besplatno Ažuriranje doživotno"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Besplatan Paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Besplatan Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Počnite"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Vladin ID"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Ukupno"),
        "hold": MessageLookupByLibrary.simpleMessage("Zadržavanje"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Broj zadržavanja"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verifikacija identiteta"),
        "inc": MessageLookupByLibrary.simpleMessage("Prihod"),
        "income": MessageLookupByLibrary.simpleMessage("Prihod"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija prihoda"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Lista kategorija prihoda"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Datum prihoda"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detalji prihoda"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Prihod za"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Lista prihoda"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Povećaj Zalihe"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Brivaza trenutno"),
        "invoice": MessageLookupByLibrary.simpleMessage("Račun"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Račun:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Broj računa..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Broj fakture"),
        "item": MessageLookupByLibrary.simpleMessage("Artikal"),
        "itemName": MessageLookupByLibrary.simpleMessage("Naziv proizvoda"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC verifikacija"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Detalji glavne knjige"),
        "ledger": MessageLookupByLibrary.simpleMessage("Glavna knjiga"),
        "left": MessageLookupByLibrary.simpleMessage("Levo"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Zajmovi"),
        "logOut": MessageLookupByLibrary.simpleMessage("Odjava"),
        "login": MessageLookupByLibrary.simpleMessage("Prijava"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Pozicija loga na fakturi?"),
        "loss": MessageLookupByLibrary.simpleMessage("Gubitak"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Gubitak/Profit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Gubitak(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Niska Zaliha"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Niski zalihi"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Napravite trajan utisak na svoje klijente sa brendiranim fakturama. Naše Neograničeno Ažuriranje pruža jedinstvenu prednost prilagođavanja faktura dodajući profesionalan dodir koji jača identitet vaše marke i podstiče lojalnost korisnika."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Proizvođač"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Panel za prijavu"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Panel za registraciju"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Mobilna Aplikacija\n+\nRadna Površina"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Potvrda o primljenom novcu"),
        "nam": MessageLookupByLibrary.simpleMessage("Ime*"),
        "name": MessageLookupByLibrary.simpleMessage("Naziv"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Ime ili kod ili kategorija"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Novi kupci"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Novi kupci"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Novi prihod"),
        "no": MessageLookupByLibrary.simpleMessage("Ne"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Nema Veze"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih kupaca"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih transakcija duga"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih kategorija troškova"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih kategorija prihoda"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih prihoda"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih faktura"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih proizvoda"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nije pronađena nijedna transakcija kupovine"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih ponuda"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih izveštaja"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih prodajnih transakcija"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih serijskih brojeva"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih dobavljača"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih transakcija"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Korisnik Nije Pronađen"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Nije Pronađena Uloga Korisnika"),
        "note": MessageLookupByLibrary.simpleMessage("Napomena"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Otvoreni čekovi"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Početno stanje"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " ili povucite i otpustite PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Porudžbine"),
        "other": MessageLookupByLibrary.simpleMessage("Ostalo"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Ostali prihodi"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Funkcije Paketa"),
        "paid": MessageLookupByLibrary.simpleMessage("Plaćeno"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Plaćeni iznos"),
        "partyName": MessageLookupByLibrary.simpleMessage("Ime stranke"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tip stranke"),
        "password": MessageLookupByLibrary.simpleMessage("Lozinka"),
        "payCash": MessageLookupByLibrary.simpleMessage("Platite Gotovinom"),
        "payable": MessageLookupByLibrary.simpleMessage("Za plaćanje"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Iznos plaćanja"),
        "payment": MessageLookupByLibrary.simpleMessage("Plaćanje"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Plaćanje unutra"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Plaćanje spolja"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Vrsta plaćanja"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Vrste plaćanja"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Broj telefona"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verifikacija telefona"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Molimo dodajte prodaju"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Molimo dodajte klijenta"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Molimo proverite svoju internet konekciju"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Molimo preuzmite našu mobilnu aplikaciju i pretplatite se na paket kako biste koristili desktop verziju"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Molimo unesite zalihe proizvoda"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Unesite validne podatke"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Molimo izaberite kupca"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Unesite validne podatke"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Panel za Registraciju"),
        "practies": MessageLookupByLibrary.simpleMessage("Vežbaj"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Korisnička Podrška"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Pregled"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Prethodni dug:"),
        "price": MessageLookupByLibrary.simpleMessage("Cena"),
        "print": MessageLookupByLibrary.simpleMessage("Štampaj"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Štampaj fakturu"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Štampaj PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Politika Privatnosti"),
        "product": MessageLookupByLibrary.simpleMessage("Proizvod"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija proizvoda"),
        "productColor": MessageLookupByLibrary.simpleMessage("Boja proizvoda"),
        "productList": MessageLookupByLibrary.simpleMessage("Lista proizvoda"),
        "productName": MessageLookupByLibrary.simpleMessage("Naziv proizvoda"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Veličina proizvoda"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Zalihe Proizvoda"),
        "productType": MessageLookupByLibrary.simpleMessage("Tip proizvoda"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Jedinica proizvoda"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garancija proizvoda"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Težina proizvoda"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapacitet proizvoda"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Izmeni Profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profit(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profit(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Kupovina"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Lista kupovine"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kupi Premium Plan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Cena kupovine"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcija kupovine"),
        "quantity": MessageLookupByLibrary.simpleMessage("Količina*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Ponuda"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Lista ponuda"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Nedavne Prodaje"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Primljeni iznos"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referentni broj"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referentni broj"),
        "registration": MessageLookupByLibrary.simpleMessage("Registracija"),
        "remaining": MessageLookupByLibrary.simpleMessage("Preostalo: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Preostali saldo"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Preostali dug"),
        "reports": MessageLookupByLibrary.simpleMessage("Izveštaji"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Resetujte vašu lozinku"),
        "retailer": MessageLookupByLibrary.simpleMessage("Maloprodavac"),
        "revenue": MessageLookupByLibrary.simpleMessage("Prihod"),
        "right": MessageLookupByLibrary.simpleMessage("Desno"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Iznos Prodaje"),
        "sale": MessageLookupByLibrary.simpleMessage("Prodaja"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Iznos prodaje"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Detalji prodaje"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista prodaje"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Prodajna cena*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Povrat prodaje"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Prodajna transakcija"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Prodajne transakcije (istorija ponuda)"),
        "sales": MessageLookupByLibrary.simpleMessage("Prodaje"),
        "salesList": MessageLookupByLibrary.simpleMessage("Lista Prodaje"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Sačuvaj i objavi"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Sačuvaj i objavi"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Sačuvaj promene"),
        "search": MessageLookupByLibrary.simpleMessage("Pretraga..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Pretraži bilo šta..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Pretraga po računu..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Pretraga po računu ili imenu"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Pretraži po imenu"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Pretraga po imenu ili telefonu..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pretraži serijski broj"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Izaberite stranke"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Izaberite brend proizvoda"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Izaberite serijski broj"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Izaberite varijacije:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Izaberite vreme garancije"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Izaberite vaš jezik"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Pošaljite poruku"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serijski broj"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serijski broj"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Trošak usluge"),
        "setting": MessageLookupByLibrary.simpleMessage("Podešavanje"),
        "share": MessageLookupByLibrary.simpleMessage("Podeli"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Dostava/ostalo"),
        "shopName": MessageLookupByLibrary.simpleMessage("Ime prodavnice"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Početno stanje prodavnice"),
        "show": MessageLookupByLibrary.simpleMessage("Prikaži >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Prikaži logo na fakturi?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Dostava/Usluga"),
        "size": MessageLookupByLibrary.simpleMessage("Veličina"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistika"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Ostanite na čelu tehnoloških inovacija bez dodatnih troškova. Naše Pos Saas POS Neograničeno Ažuriranje obezbeđuje da uvek imate najnovije alate i funkcije na dohvat ruke, čime se garantuje da vaš posao ostane u samom vrhu."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Ostanite na čelu tehnoloških inovacija bez dodatnih troškova. Naše Neograničeno Ažuriranje Pos Sass POS-a obezbeđuje da uvek imate najnovije alate i funkcije na dohvat ruke, čime se garantuje da vaš posao ostane u samom vrhu."),
        "stock": MessageLookupByLibrary.simpleMessage("Zalihe"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventar zaliha"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Izveštaj o zalihama"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Vrednost zaliha"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Vrednost Zaliha"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Međuzbir"),
        "subciption": MessageLookupByLibrary.simpleMessage("Pretplata"),
        "submit": MessageLookupByLibrary.simpleMessage("Potvrdi"),
        "supplier": MessageLookupByLibrary.simpleMessage("Dobavljači"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Dugovanja dobavljača"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Faktura dobavljača"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lista dobavljača"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT Kod"),
        "tSale": MessageLookupByLibrary.simpleMessage("Ukupna Prodaja"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Uzmite fotografiju vozačke dozvole, lične karte ili pasoša"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Uslovi Korišćenja"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Samo ime sve govori. Sa Pos Saas POS Neograničeno, nema ograničenja u korišćenju. Bez obzira da li obrađujete samo nekoliko transakcija ili se suočavate sa navalom klijenata, možete raditi s poverenjem znajući da niste ograničeni količinama."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("Ovaj kupac nema dugovanja"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Ovaj kupac ima prethodno dugovanje"),
        "to": MessageLookupByLibrary.simpleMessage("Do"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Najprodavaniji proizvod"),
        "total": MessageLookupByLibrary.simpleMessage("ukupno"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Ukupan iznos"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Ukupni popust"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Ukupno duga"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Ukupni dugovi"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Ukupni troškovi"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Ukupan prihod"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Ukupno artikala: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Ukupan gubitak"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Ukupno plaćeno"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Ukupno za plaćanje"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Ukupno plaćanja spolja"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Ukupna cena"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Ukupno proizvoda"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Ukupan profit"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Ukupna kupovina"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Ukupan iznos povraćaja"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Ukupni povraćaji"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Ukupna prodaja"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Ukupna prodaja"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Ukupni PDV"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Ukupno plaćanja unutra"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transakcija"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID Transakcije"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Izveštaj o transakcijama"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Pokušaj Ponovo"),
        "type": MessageLookupByLibrary.simpleMessage("Tip"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Naplativo"),
        "unit": MessageLookupByLibrary.simpleMessage("Jedinica"),
        "unitName": MessageLookupByLibrary.simpleMessage("Ime jedinice"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Jedinična cena"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Neograničeno"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Neograničene fakture"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Neograničeno Korišćenje"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Otkrijte puni potencijal Pos Saas POS-a kroz personalizovane sesije obuke koje vodi naš stručni tim. Od osnovnih do naprednih tehnika, mi se pobrinemo da ste dobro upućeni u korišćenje svakog aspekta sistema kako biste optimizovali poslovne procese."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Ažuriraj Sada"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Prvo ažurirajte svoj plan\\nLimit prodaje je premašen."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Unapredi na mobilnoj aplikaciji"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Prenesite sliku"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Otpremi logo na fakturi"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Učitaj Dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Učitaj Datoteku"),
        "userName": MessageLookupByLibrary.simpleMessage("Korisničko Ime"),
        "userRole": MessageLookupByLibrary.simpleMessage("Uloga Korisnika"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Ime Uloge Korisnika"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Titula Korisnika"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("PDV/PDV"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifikujte broj telefona"),
        "view": MessageLookupByLibrary.simpleMessage("Pregled"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Kupac koji dolazi"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garancija"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garancija"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Morate registrovati vaš telefon pre nego što počnete!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Mi razumemo važnost besprekornog poslovanja. Zato je naša podrška dostupna 24 sata dnevno da vam pomogne, bilo da je u pitanju brza upit ili sveobuhvatna zabrinutost. Povežite se s nama u bilo koje vreme, bilo gde putem poziva ili WhatsApp-a kako biste doživeli neprevaziđenu korisničku podršku."),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Veletrgovac"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Veleprodaja"),
        "wight": MessageLookupByLibrary.simpleMessage("Težina"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Da, vratite"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Morate se PONOVO PRIJAVITI na svoj nalog."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Morate da verifikujete identitet pre kupovine poruka"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Vaša lista svih prodaja"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Sve vaše prodaje"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Koristite"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Vaše dugovane prodaje"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Morate da verifikujete identitet pre kupovine poruka"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Vaš Paket"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Vaša uplata je otkazana"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Vaša uplata je uspešna"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Vaše plaćanje je otkazano")
      };
}
