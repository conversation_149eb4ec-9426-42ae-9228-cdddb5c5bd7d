// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("添加销售"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("类别"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("发票"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS销售"),
        "PRICE": MessageLookupByLibrary.simpleMessage("价格"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("产品名称"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas登录面板"),
        "QTY": MessageLookupByLibrary.simpleMessage("数量"),
        "Quantity": MessageLookupByLibrary.simpleMessage("数量*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("状态"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("总值"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("用户标题"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("关于应用"),
        "accountName": MessageLookupByLibrary.simpleMessage("账户名称"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("账号"),
        "action": MessageLookupByLibrary.simpleMessage("操作"),
        "add": MessageLookupByLibrary.simpleMessage("添加"),
        "addBrand": MessageLookupByLibrary.simpleMessage("添加品牌"),
        "addCategory": MessageLookupByLibrary.simpleMessage("添加类别"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("添加客户"),
        "addDescription": MessageLookupByLibrary.simpleMessage("添加描述..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("添加文件"),
        "addItem": MessageLookupByLibrary.simpleMessage("添加物品"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage("添加项目类别"),
        "addNew": MessageLookupByLibrary.simpleMessage("新增"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("添加新用户"),
        "addProduct": MessageLookupByLibrary.simpleMessage("添加产品"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("添加成功"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("添加供应商"),
        "addUnit": MessageLookupByLibrary.simpleMessage("添加单位"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("添加/更新支出列表"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("添加/更新收入列表"),
        "addUserRole": MessageLookupByLibrary.simpleMessage("添加用户角色"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage("添加序列号？"),
        "address": MessageLookupByLibrary.simpleMessage("地址"),
        "all": MessageLookupByLibrary.simpleMessage("全部"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage("所有基本功能"),
        "alreadyHaveAnAccounts": MessageLookupByLibrary.simpleMessage("已有账户？"),
        "amount": MessageLookupByLibrary.simpleMessage("金额"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Android和iOS应用支持"),
        "areYouWantToCreateThisQuation":
            MessageLookupByLibrary.simpleMessage("您是否要创建此报价单？"),
        "areYouWantToDeleteThisCustomer":
            MessageLookupByLibrary.simpleMessage("您确定要删除此客户吗？"),
        "areYouWantToDeleteThisProduct":
            MessageLookupByLibrary.simpleMessage("您是否要删除此产品"),
        "areYouWantToDeleteThisQuotion":
            MessageLookupByLibrary.simpleMessage("您是否要删除此报价？"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("您是否要退还此销售？"),
        "balance": MessageLookupByLibrary.simpleMessage("余额"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("银行账户货币"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("银行账户"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("银行信息"),
        "bankName": MessageLookupByLibrary.simpleMessage("银行名称"),
        "between": MessageLookupByLibrary.simpleMessage("介于"),
        "billTo": MessageLookupByLibrary.simpleMessage("账单寄至："),
        "branchName": MessageLookupByLibrary.simpleMessage("分行名称"),
        "brand": MessageLookupByLibrary.simpleMessage("品牌"),
        "brandName": MessageLookupByLibrary.simpleMessage("品牌名称"),
        "businessCategory": MessageLookupByLibrary.simpleMessage("业务类别"),
        "buy": MessageLookupByLibrary.simpleMessage("购买"),
        "buyPremiumPlan": MessageLookupByLibrary.simpleMessage("购买高级计划"),
        "buySms": MessageLookupByLibrary.simpleMessage("购买短信"),
        "calculator": MessageLookupByLibrary.simpleMessage("计算器："),
        "camera": MessageLookupByLibrary.simpleMessage("相机"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "capacity": MessageLookupByLibrary.simpleMessage("容量"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("现金与银行"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("手头现金"),
        "categories": MessageLookupByLibrary.simpleMessage("类别"),
        "category": MessageLookupByLibrary.simpleMessage("类别"),
        "categoryName": MessageLookupByLibrary.simpleMessage("类别名称"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("改变金额"),
        "changeableAmount": MessageLookupByLibrary.simpleMessage("可变金额"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("检查保修"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("选择一个套餐"),
        "collectDue": MessageLookupByLibrary.simpleMessage("收取应收款 >"),
        "color": MessageLookupByLibrary.simpleMessage("颜色"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("公司名称"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("公司地址"),
        "companyDescription": MessageLookupByLibrary.simpleMessage("公司描述"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage("公司电子邮件地址"),
        "companyName": MessageLookupByLibrary.simpleMessage("公司名称"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage("公司电话号码"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage("公司网站网址"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("确认密码"),
        "continu": MessageLookupByLibrary.simpleMessage("继续"),
        "convertToSale": MessageLookupByLibrary.simpleMessage("转换为销售"),
        "create": MessageLookupByLibrary.simpleMessage("创建"),
        "createPayment": MessageLookupByLibrary.simpleMessage("创建付款"),
        "createdBy": MessageLookupByLibrary.simpleMessage("创建者"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("创意中心"),
        "currency": MessageLookupByLibrary.simpleMessage("货币"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("当前套餐"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("自定义发票品牌"),
        "customer": MessageLookupByLibrary.simpleMessage("顾客"),
        "customerDue": MessageLookupByLibrary.simpleMessage("客户欠款"),
        "customerInvoices": MessageLookupByLibrary.simpleMessage("客户发票"),
        "customerList": MessageLookupByLibrary.simpleMessage("客户列表"),
        "customerName": MessageLookupByLibrary.simpleMessage("客户名称"),
        "customerOfTheMonth": MessageLookupByLibrary.simpleMessage("本月顾客"),
        "customerType": MessageLookupByLibrary.simpleMessage("客户类型"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("顾客：现场客户"),
        "customers": MessageLookupByLibrary.simpleMessage("客户"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("每日收款额"),
        "dailySales": MessageLookupByLibrary.simpleMessage("每日销售额"),
        "dailyTransaction": MessageLookupByLibrary.simpleMessage("每日交易"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("仪表板"),
        "date": MessageLookupByLibrary.simpleMessage("日期"),
        "dateTime": MessageLookupByLibrary.simpleMessage("日期时间"),
        "dealer": MessageLookupByLibrary.simpleMessage("经销商"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("经销商价格"),
        "delete": MessageLookupByLibrary.simpleMessage("删除"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("运费"),
        "description": MessageLookupByLibrary.simpleMessage("描述"),
        "details": MessageLookupByLibrary.simpleMessage("详情 >"),
        "discount": MessageLookupByLibrary.simpleMessage("折扣"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("折扣价"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("下载PDF"),
        "due": MessageLookupByLibrary.simpleMessage("欠款"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("应付金额"),
        "dueAmountWillShowHere":
            MessageLookupByLibrary.simpleMessage("如有未付款金额，将在此处显示"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("应收款项收集"),
        "dueList": MessageLookupByLibrary.simpleMessage("未付款清单"),
        "dueTransaction": MessageLookupByLibrary.simpleMessage("欠款交易"),
        "edit": MessageLookupByLibrary.simpleMessage("编辑"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage("编辑/添加序列号："),
        "editYourProfile": MessageLookupByLibrary.simpleMessage("编辑您的个人资料"),
        "email": MessageLookupByLibrary.simpleMessage("电子邮件"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("输入金额"),
        "enterBrandName": MessageLookupByLibrary.simpleMessage("输入品牌名称"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage("输入类别名称"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("输入公司描述"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("输入公司电子邮件地址"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("输入公司电话号码"),
        "enterCompanyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("输入公司网站网址"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage("输入客户名称"),
        "enterDealePrice": MessageLookupByLibrary.simpleMessage("输入经销商价格"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage("输入折扣价"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage("输入支出类别"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage("输入支出日期"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage("输入收入类别"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage("输入收入日期"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("输入制造商名称"),
        "enterName": MessageLookupByLibrary.simpleMessage("输入名称"),
        "enterNames": MessageLookupByLibrary.simpleMessage("输入姓名"),
        "enterNote": MessageLookupByLibrary.simpleMessage("输入备注"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage("输入期初余额"),
        "enterPaidAmount": MessageLookupByLibrary.simpleMessage("输入已付金额"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("输入密码"),
        "enterPayingAmount": MessageLookupByLibrary.simpleMessage("输入支付金额"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("输入价格"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage("输入产品容量"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage("输入产品代码"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage("输入产品颜色"),
        "enterProductName": MessageLookupByLibrary.simpleMessage("输入产品名称"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage("输入产品数量"),
        "enterProductSize": MessageLookupByLibrary.simpleMessage("输入产品尺寸"),
        "enterProductType": MessageLookupByLibrary.simpleMessage("输入产品类型"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage("输入产品单位"),
        "enterProductWeight": MessageLookupByLibrary.simpleMessage("输入产品重量"),
        "enterPurchasePrice": MessageLookupByLibrary.simpleMessage("输入采购价格"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage("输入参考编号"),
        "enterSalePrice": MessageLookupByLibrary.simpleMessage("输入销售价格"),
        "enterSerialNumber": MessageLookupByLibrary.simpleMessage("输入序列号"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage("输入短信内容"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage("输入库存数量"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage("输入交易ID"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage("输入单位名称"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage("输入用户角色名称"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage("输入用户标题"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("输入保修"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage("输入批发价格"),
        "enterYOurAmount": MessageLookupByLibrary.simpleMessage("输入金额"),
        "enterYourAddress": MessageLookupByLibrary.simpleMessage("输入您的地址"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("输入您的公司地址"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("输入您的公司名称"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("输入您的公司名称"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("输入您的电子邮件地址"),
        "enterYourPassword": MessageLookupByLibrary.simpleMessage("输入您的密码"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("再次输入您的密码"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("输入您的电话号码"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage("输入您的店铺名称"),
        "entercategoryName": MessageLookupByLibrary.simpleMessage("输入类别名称"),
        "expense": MessageLookupByLibrary.simpleMessage("费用"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("支出日期"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("支出详情"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("支出用途"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage("支出类别列表"),
        "expenses": MessageLookupByLibrary.simpleMessage("费用"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage("本月前五畅销产品"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage("供无限使用"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("忘记密码？"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage("免费数据备份"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage("免费终身更新"),
        "freePackage": MessageLookupByLibrary.simpleMessage("免费套餐"),
        "freePlan": MessageLookupByLibrary.simpleMessage("免费计划"),
        "getStarted": MessageLookupByLibrary.simpleMessage("开始使用"),
        "govermentId": MessageLookupByLibrary.simpleMessage("政府身份证"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("总计"),
        "hold": MessageLookupByLibrary.simpleMessage("暂停"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("暂停编号"),
        "identityVerify": MessageLookupByLibrary.simpleMessage("身份验证"),
        "inc": MessageLookupByLibrary.simpleMessage("收入"),
        "income": MessageLookupByLibrary.simpleMessage("收入"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("收入类别"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage("收入类别列表"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("收入日期"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("收入详情"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("收入用途"),
        "incomeList": MessageLookupByLibrary.simpleMessage("收入列表"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("增加库存"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage("即时隐私"),
        "invoice": MessageLookupByLibrary.simpleMessage("发票"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("发票："),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("发票号..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("发票编号"),
        "item": MessageLookupByLibrary.simpleMessage("商品"),
        "itemName": MessageLookupByLibrary.simpleMessage("商品名称"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC 验证"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("分类账明细"),
        "ledger": MessageLookupByLibrary.simpleMessage("分类帐"),
        "left": MessageLookupByLibrary.simpleMessage("左侧"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("贷款账户"),
        "logOut": MessageLookupByLibrary.simpleMessage("登出"),
        "login": MessageLookupByLibrary.simpleMessage("登录"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("徽标在发票中的位置？"),
        "loss": MessageLookupByLibrary.simpleMessage("亏损"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("亏损/利润"),
        "lossminus": MessageLookupByLibrary.simpleMessage("亏损(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("低库存"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("库存不足"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "通过品牌发票给客户留下深刻印象。我们的无限升级提供了定制发票的独特优势，增加了专业触感，巩固了您的品牌形象，培养了客户忠诚度。"),
        "manufacturer": MessageLookupByLibrary.simpleMessage("制造商"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas 登录面板"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas 注册面板"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("移动应用\n+\n桌面"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("收款单"),
        "nam": MessageLookupByLibrary.simpleMessage("名称*"),
        "name": MessageLookupByLibrary.simpleMessage("名称"),
        "nameCodeOrCateogry": MessageLookupByLibrary.simpleMessage("名称、代码或类别"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("新客户"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("新客户"),
        "newIncome": MessageLookupByLibrary.simpleMessage("新增收入"),
        "no": MessageLookupByLibrary.simpleMessage("否"),
        "noConnection": MessageLookupByLibrary.simpleMessage("无连接"),
        "noCustomerFound": MessageLookupByLibrary.simpleMessage("未找到客户"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("未找到应付交易"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("未找到支出类别"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("找不到收入类别"),
        "noIncomeFound": MessageLookupByLibrary.simpleMessage("未找到收入"),
        "noInvoiceFound": MessageLookupByLibrary.simpleMessage("未找到发票"),
        "noProductFound": MessageLookupByLibrary.simpleMessage("未找到产品"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("未找到采购交易"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage("未找到报价"),
        "noReportFound": MessageLookupByLibrary.simpleMessage("未找到报告"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("未找到销售交易"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage("未找到序列号"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage("未找到供应商"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage("未找到交易"),
        "noUserFound": MessageLookupByLibrary.simpleMessage("未找到用户"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage("未找到用户角色"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage("未找到序列号"),
        "note": MessageLookupByLibrary.simpleMessage("备注"),
        "ok": MessageLookupByLibrary.simpleMessage("确定"),
        "openCheques": MessageLookupByLibrary.simpleMessage("待支付支票"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("期初余额"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("或拖放 PNG、JPG 文件"),
        "orders": MessageLookupByLibrary.simpleMessage("订单"),
        "other": MessageLookupByLibrary.simpleMessage("其他"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("其他收入"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("套餐功能"),
        "paid": MessageLookupByLibrary.simpleMessage("已付"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("已付金额"),
        "partyName": MessageLookupByLibrary.simpleMessage("派对名称"),
        "partyType": MessageLookupByLibrary.simpleMessage("派对类型"),
        "password": MessageLookupByLibrary.simpleMessage("密码"),
        "payCash": MessageLookupByLibrary.simpleMessage("现金支付"),
        "payable": MessageLookupByLibrary.simpleMessage("应付金额"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("付款金额"),
        "payment": MessageLookupByLibrary.simpleMessage("付款"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("收款"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("支出"),
        "paymentType": MessageLookupByLibrary.simpleMessage("付款类型"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("付款类型"),
        "phone": MessageLookupByLibrary.simpleMessage("电话"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("电话号码"),
        "phoneVerification": MessageLookupByLibrary.simpleMessage("电话验证"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage("请添加一笔销售"),
        "pleaseAddCustomer": MessageLookupByLibrary.simpleMessage("请添加客户"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage("请检查您的互联网连接"),
        "pleaseDownloadOurMobileApp":
            MessageLookupByLibrary.simpleMessage("请下载我们的移动应用并订阅套餐以使用桌面版本"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("请输入产品库存"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage("请输入有效数据"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("请选择一个顾客"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage("请输入有效数据"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas注册面板"),
        "practies": MessageLookupByLibrary.simpleMessage("实践"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("高级客户支持"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("高级计划"),
        "preview": MessageLookupByLibrary.simpleMessage("预览"),
        "previousDue": MessageLookupByLibrary.simpleMessage("之前欠款："),
        "price": MessageLookupByLibrary.simpleMessage("价格"),
        "print": MessageLookupByLibrary.simpleMessage("打印"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("打印发票"),
        "printPdf": MessageLookupByLibrary.simpleMessage("打印PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "product": MessageLookupByLibrary.simpleMessage("产品"),
        "productCategory": MessageLookupByLibrary.simpleMessage("产品类别"),
        "productCod": MessageLookupByLibrary.simpleMessage("产品代码*"),
        "productColor": MessageLookupByLibrary.simpleMessage("产品颜色"),
        "productList": MessageLookupByLibrary.simpleMessage("产品清单"),
        "productNam": MessageLookupByLibrary.simpleMessage("产品名称*"),
        "productName": MessageLookupByLibrary.simpleMessage("产品名称"),
        "productSize": MessageLookupByLibrary.simpleMessage("产品尺寸"),
        "productStock": MessageLookupByLibrary.simpleMessage("产品库存"),
        "productType": MessageLookupByLibrary.simpleMessage("产品类型"),
        "productUnit": MessageLookupByLibrary.simpleMessage("产品单位"),
        "productWaranty": MessageLookupByLibrary.simpleMessage("产品保修"),
        "productWeight": MessageLookupByLibrary.simpleMessage("产品重量"),
        "productcapacity": MessageLookupByLibrary.simpleMessage("产品容量"),
        "prof": MessageLookupByLibrary.simpleMessage("个人资料"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("编辑个人资料"),
        "profit": MessageLookupByLibrary.simpleMessage("利润"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("利润(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("利润(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("购买"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("采购清单"),
        "purchasePremiumPlan": MessageLookupByLibrary.simpleMessage("购买高级计划"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("采购价格"),
        "purchaseTransaction": MessageLookupByLibrary.simpleMessage("采购交易"),
        "quantity": MessageLookupByLibrary.simpleMessage("数量"),
        "quotation": MessageLookupByLibrary.simpleMessage("报价单"),
        "quotationList": MessageLookupByLibrary.simpleMessage("报价清单"),
        "recentSale": MessageLookupByLibrary.simpleMessage("最近销售"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("已收金额"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("参考编号"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("参考编号"),
        "registration": MessageLookupByLibrary.simpleMessage("注册"),
        "remaining": MessageLookupByLibrary.simpleMessage("剩余: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("剩余余额"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("剩余欠款"),
        "reports": MessageLookupByLibrary.simpleMessage("报告"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage("重置您的密码"),
        "retailer": MessageLookupByLibrary.simpleMessage("零售商"),
        "revenue": MessageLookupByLibrary.simpleMessage("收入"),
        "right": MessageLookupByLibrary.simpleMessage("右侧"),
        "sAmount": MessageLookupByLibrary.simpleMessage("销售金额"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "轻松保护您的业务数据。我们的Pos Saas POS无限升级包括免费数据备份，确保您宝贵的信息得到保护，免受突发事件的影响。专注于真正重要的事情-您的业务增长。"),
        "sale": MessageLookupByLibrary.simpleMessage("销售"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("销售额"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("销售明细"),
        "saleList": MessageLookupByLibrary.simpleMessage("销售清单"),
        "salePrice": MessageLookupByLibrary.simpleMessage("销售价格"),
        "salePrices": MessageLookupByLibrary.simpleMessage("销售价格*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("销售退货"),
        "saleTransaction": MessageLookupByLibrary.simpleMessage("销售交易"),
        "saleTransactionQuatationHistory":
            MessageLookupByLibrary.simpleMessage("销售交易（报价销售历史）"),
        "sales": MessageLookupByLibrary.simpleMessage("销售"),
        "salesList": MessageLookupByLibrary.simpleMessage("销售清单"),
        "saveAndPublish": MessageLookupByLibrary.simpleMessage("保存并发布"),
        "saveAndPublished": MessageLookupByLibrary.simpleMessage("保存并发布"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("保存更改"),
        "search": MessageLookupByLibrary.simpleMessage("搜索..."),
        "searchByInvoice": MessageLookupByLibrary.simpleMessage("按发票搜索..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("按发票或名称搜索"),
        "searchByName": MessageLookupByLibrary.simpleMessage("按名称搜索"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("按名称或电话搜索..."),
        "searchSerialNumber": MessageLookupByLibrary.simpleMessage("搜索序列号"),
        "selectParties": MessageLookupByLibrary.simpleMessage("选择交易方"),
        "selectProductBrand": MessageLookupByLibrary.simpleMessage("选择产品品牌"),
        "selectSerialNumber": MessageLookupByLibrary.simpleMessage("选择序列号"),
        "selectVariations": MessageLookupByLibrary.simpleMessage("选择变体："),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage("选择保修时间"),
        "selectYourLanguage": MessageLookupByLibrary.simpleMessage("选择您的语言"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("发送消息"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("序列号"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("序列号"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("服务费"),
        "setting": MessageLookupByLibrary.simpleMessage("设置"),
        "share": MessageLookupByLibrary.simpleMessage("分享"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("运费/其他"),
        "shopName": MessageLookupByLibrary.simpleMessage("店铺名称"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage("店铺初始余额"),
        "show": MessageLookupByLibrary.simpleMessage("展示 >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage("在发票中显示徽标？"),
        "shpingOrServices": MessageLookupByLibrary.simpleMessage("运输/服务"),
        "size": MessageLookupByLibrary.simpleMessage("尺寸"),
        "statistic": MessageLookupByLibrary.simpleMessage("统计"),
        "status": MessageLookupByLibrary.simpleMessage("状态"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "始终处于技术进步的前沿，无需额外费用。我们的Pos Saas POS无限升级确保您始终拥有最新的工具和功能，保证您的业务保持前沿。"),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "始终处于技术进步的前沿，无需额外费用。我们的Pos Sass POS无限升级确保您始终拥有最新的工具和功能，保证您的业务保持前沿。"),
        "stock": MessageLookupByLibrary.simpleMessage("库存"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("库存清单"),
        "stockReport": MessageLookupByLibrary.simpleMessage("库存报告"),
        "stockValue": MessageLookupByLibrary.simpleMessage("库存价值"),
        "stockValues": MessageLookupByLibrary.simpleMessage("库存价值"),
        "subTotal": MessageLookupByLibrary.simpleMessage("小计"),
        "submit": MessageLookupByLibrary.simpleMessage("提交"),
        "supplier": MessageLookupByLibrary.simpleMessage("供应商"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("供应商欠款"),
        "supplierInvoice": MessageLookupByLibrary.simpleMessage("供应商发票"),
        "supplierList": MessageLookupByLibrary.simpleMessage("供应商列表"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT代码"),
        "tSale": MessageLookupByLibrary.simpleMessage("总销售额"),
        "takeADriveLisense":
            MessageLookupByLibrary.simpleMessage("拍摄驾驶执照、国民身份证或护照照片"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("使用条款"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "名字说的就是一切。使用Pos Saas POS Unlimited，您不受使用限制。无论您是否处理少数交易或迎来大量客户，您都可以自信运营，无需担心受到限制。"),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("该顾客无未付款金额"),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("此客户有以前的欠款"),
        "to": MessageLookupByLibrary.simpleMessage("至"),
        "topSellingProduct": MessageLookupByLibrary.simpleMessage("畅销商品"),
        "total": MessageLookupByLibrary.simpleMessage("总计"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("总金额"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("总折扣"),
        "totalDue": MessageLookupByLibrary.simpleMessage("总应付款"),
        "totalDues": MessageLookupByLibrary.simpleMessage("总欠款"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("总支出"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("总收入"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("总商品数：2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("总亏损"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("总付款"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("应付总额"),
        "totalPaymentOut": MessageLookupByLibrary.simpleMessage("总支出"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("总价"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("总产品"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("总利润"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("总采购额"),
        "totalReturnAmount": MessageLookupByLibrary.simpleMessage("总退货金额"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("总退货次数"),
        "totalSale": MessageLookupByLibrary.simpleMessage("总销售额"),
        "totalSales": MessageLookupByLibrary.simpleMessage("总销售额"),
        "totalVat": MessageLookupByLibrary.simpleMessage("总增值税"),
        "totalpaymentIn": MessageLookupByLibrary.simpleMessage("总收款"),
        "transaction": MessageLookupByLibrary.simpleMessage("交易"),
        "transactionId": MessageLookupByLibrary.simpleMessage("交易ID"),
        "transactionReport": MessageLookupByLibrary.simpleMessage("交易报告"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("重试"),
        "type": MessageLookupByLibrary.simpleMessage("类型"),
        "unPaid": MessageLookupByLibrary.simpleMessage("未付"),
        "unit": MessageLookupByLibrary.simpleMessage("单位"),
        "unitName": MessageLookupByLibrary.simpleMessage("单位名称"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("单价"),
        "unlimited": MessageLookupByLibrary.simpleMessage("无限"),
        "unlimitedInvoice": MessageLookupByLibrary.simpleMessage("无限发票"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage("无限使用"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "通过我们专家团队主导的个性化培训课程，发挥Pos Saas POS的全部潜力。从基础知识到高级技巧，我们确保您精通利用系统的各个方面以优化业务流程。"),
        "updateNow": MessageLookupByLibrary.simpleMessage("立即更新"),
        "updateYourPlanFirst":
            MessageLookupByLibrary.simpleMessage("首先更新您的计划\\n销售限额已超过。"),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage("在移动应用上升级"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("上传图像"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage("上传发票徽标"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("上传文件"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("上传文件"),
        "userName": MessageLookupByLibrary.simpleMessage("用户名"),
        "userRole": MessageLookupByLibrary.simpleMessage("用户角色"),
        "userRoleName": MessageLookupByLibrary.simpleMessage("用户角色名称"),
        "userTitle": MessageLookupByLibrary.simpleMessage("用户标题"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("增值税/GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage("验证电话号码"),
        "view": MessageLookupByLibrary.simpleMessage("查看"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("现场客户"),
        "warranty": MessageLookupByLibrary.simpleMessage("保修"),
        "warrantys": MessageLookupByLibrary.simpleMessage("保修"),
        "weNeedToRegisterYourPhone":
            MessageLookupByLibrary.simpleMessage("在开始之前，我们需要注册您的电话！"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "我们理解无缝运营的重要性。因此，我们提供全天候支持，帮助您解决快速查询或全面关切的问题。随时随地通过电话或WhatsApp与我们联系，体验无与伦比的客户服务。"),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("批发价格"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("批发商"),
        "wholesale": MessageLookupByLibrary.simpleMessage("批发"),
        "wight": MessageLookupByLibrary.simpleMessage("重量"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("是，退还"),
        "youHaveToRelogin":
            MessageLookupByLibrary.simpleMessage("您需要重新登录您的帐户。"),
        "youNeedToIdentityVerifySms":
            MessageLookupByLibrary.simpleMessage("在购买消息之前，您需要进行身份验证"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage("您的全部销售清单"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("您的全部销售额"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("您正在使用"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("您的应收销售额"),
        "yourNeedToIdentityVerify":
            MessageLookupByLibrary.simpleMessage("在购买消息之前，您需要进行身份验证"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("您的套餐"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("您的付款已取消"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("您的付款已成功"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("您的付款已取消")
      };
}
