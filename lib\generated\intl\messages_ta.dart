// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ta locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ta';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("விற்பனை சேர்க்க"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("வகை"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("கட்டண ஆவணம்"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS விற்பனை"),
        "PRICE": MessageLookupByLibrary.simpleMessage("விலை"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("தயாரிப்பு பெயர்"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas உள்நுழைவு பேனல்"),
        "QTY": MessageLookupByLibrary.simpleMessage("தொகை"),
        "Quantity": MessageLookupByLibrary.simpleMessage("அளவு*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("நிலை"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("மொத்த மதிப்பு"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("பயனர் தலை"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("பயன்பாட்டு பற்றி"),
        "accountName": MessageLookupByLibrary.simpleMessage("கணக்கு பெயர்"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("கணக்கு எண்"),
        "action": MessageLookupByLibrary.simpleMessage("செயல்"),
        "add": MessageLookupByLibrary.simpleMessage("சேர்க்க"),
        "addBrand": MessageLookupByLibrary.simpleMessage("பிராண்ட் சேர்க்க"),
        "addCategory": MessageLookupByLibrary.simpleMessage("வகை சேர்க்க"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("கஸ்"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("விளக்கத்தைச் சேர்க்கவும்..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("ஆவணங்களைச் சேர்க்க"),
        "addItem": MessageLookupByLibrary.simpleMessage("பொருள் சேர்க்கவும்"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("பொருள் வகையை சேர்க்கவும்"),
        "addNew": MessageLookupByLibrary.simpleMessage("புதியதை சேர்க்க"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("புதிய பயனரை சேர்க்க"),
        "addProduct":
            MessageLookupByLibrary.simpleMessage("பொருளைச் சேர்க்கவும்"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("வெற்றிகரமாக சேர்த்தது"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("சப்ளையரைச் சேர்க்க"),
        "addUnit": MessageLookupByLibrary.simpleMessage("யூனிட் சேர்க்க"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "செலவு பட்டியலைச் சேர்க்க/புதுப்பிக்கவும்"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "வருவாயை சேர்க்க/புதுப்பிக்கவும்"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("பயனர் பங்கு சேர்க்க"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage(
            "சீரியல் எண்ணைச் சேர்க்கிறீர்களா?"),
        "address": MessageLookupByLibrary.simpleMessage("முகவரி"),
        "all": MessageLookupByLibrary.simpleMessage("அனைத்தும்"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "அனைத்து அடிப்படை அம்சங்களும்"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("ஏற்கனவே ஒரு கணக்கு உள்ளதா?"),
        "amount": MessageLookupByLibrary.simpleMessage("தொகை"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android மற்றும் iOS பயன்பாடு ஆதரவு"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "நீங்கள் இந்த விலையை உருவாக்க விரும்புகிறீர்களா?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "இந்த வாடிக்கையாளரை நீக்க விரும்புகிறீர்களா?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "இந்த பொருளை அழிக்க விரும்புகின்றீர்களா"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "இந்த விலைப்பட்டியலை நீக்க விரும்புகிறீர்களா?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "இந்த விற்பனையைத் திரும்பப் பெற விரும்புகிறீர்களா?"),
        "balance": MessageLookupByLibrary.simpleMessage("இருப்பு"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("வங்கி கணக்கு நாணயம்"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("வங்கி கணக்குகள்"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("வங்கி தகவரம்"),
        "bankName": MessageLookupByLibrary.simpleMessage("வங்கி பெயர்"),
        "between": MessageLookupByLibrary.simpleMessage("இடையில்"),
        "billTo": MessageLookupByLibrary.simpleMessage("கட்டணம் செலுத்துபவர்:"),
        "branchName": MessageLookupByLibrary.simpleMessage("கிளை பெயர்"),
        "brand": MessageLookupByLibrary.simpleMessage("பிராண்ட்"),
        "brandName": MessageLookupByLibrary.simpleMessage("பிராண்ட் பெயர்"),
        "businessCategory": MessageLookupByLibrary.simpleMessage("வணிக வகை"),
        "buy": MessageLookupByLibrary.simpleMessage("வாங்க"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("பிரீமியம் திட்டம் வாங்க"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMS வாங்க"),
        "calculator": MessageLookupByLibrary.simpleMessage("கணினி:"),
        "camera": MessageLookupByLibrary.simpleMessage("கேமரா"),
        "cancel": MessageLookupByLibrary.simpleMessage("இல cancellation"),
        "capacity": MessageLookupByLibrary.simpleMessage("கொள்ளளவு"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("பணம் மற்றும் வங்கி"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("கைக்கொடுத்த பணம்"),
        "categories": MessageLookupByLibrary.simpleMessage("வகைகள்"),
        "category": MessageLookupByLibrary.simpleMessage("வகை"),
        "categoryName": MessageLookupByLibrary.simpleMessage("வகை பெயர்"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("மாற்றம் தொகை"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("மாற்றக்கூடிய தொகை"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("வாரண்டி சரிபார்க்கவும்"),
        "choseAplan": MessageLookupByLibrary.simpleMessage(
            "ஒரு திட்டத்தைத் தேர்ந்தெடுக்கவும்"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("கடன் வசூலிக்கவும் >"),
        "color": MessageLookupByLibrary.simpleMessage("நிறம்"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("நிறுவனப் பெயர்"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("நிறுவன முகவரி"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("நிறுவன விளக்கம்"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("நிறுவன மின்னஞ்சல் முகவரி"),
        "companyName": MessageLookupByLibrary.simpleMessage("நிறுவனப் பெயர்"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("நிறுவன தொலைபேசி எண்"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("நிறுவன வலைத்தள URL"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage(
            "கடவுச்சொல்லை உறுதிப்படுத்தவும்"),
        "continu": MessageLookupByLibrary.simpleMessage("தொடரவும்"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("விற்பனையாக மாற்றுக"),
        "create": MessageLookupByLibrary.simpleMessage("உருவாக்க"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("கட்டணம் உருவாக்க"),
        "createdBy": MessageLookupByLibrary.simpleMessage("உருவாக்கியவர்"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("க creativehub"),
        "currency": MessageLookupByLibrary.simpleMessage("நாணயம்"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("தற்போதைய திட்டம்"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "தனிப்பயன் விலகப்பட்ட விலைச்சீட்டு பிராண்டிங்"),
        "customer": MessageLookupByLibrary.simpleMessage("கஸ்டமர்"),
        "customerDue": MessageLookupByLibrary.simpleMessage("கஸ்டமர் தங்கியது"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("கஸ்டமர் இன்வாய்சஸ்"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("கஸ்டமர் பட்டியல்"),
        "customerName": MessageLookupByLibrary.simpleMessage("கஸ்டமர் பெயர்"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("மாத வாசகர்"),
        "customerType": MessageLookupByLibrary.simpleMessage("கஸ்டமர் வகை"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "வாடிக்கையாளர்: நடைபெறும் வாடிக்கையாளர்"),
        "customers":
            MessageLookupByLibrary.simpleMessage("கடன் வாடிக்கையாளர்கள்"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("தினசரி வசூல்"),
        "dailySales": MessageLookupByLibrary.simpleMessage("தினசரி விற்பனை"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("தினசரி பரிவர்த்தனை"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("டேஷ்போர்டு"),
        "date": MessageLookupByLibrary.simpleMessage("தேதி"),
        "dateTime": MessageLookupByLibrary.simpleMessage("நேரம்"),
        "dealer": MessageLookupByLibrary.simpleMessage("டீலர்"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("டீலர் விலை"),
        "delete": MessageLookupByLibrary.simpleMessage("அழி"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("டெலிவரி கட்டணம்"),
        "description": MessageLookupByLibrary.simpleMessage("விளக்கம்"),
        "details": MessageLookupByLibrary.simpleMessage("விவரங்கள் >"),
        "discount": MessageLookupByLibrary.simpleMessage("கழிவு"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("டிஸ்கाउண்ட் விலை"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF பதிவிறக்க"),
        "due": MessageLookupByLibrary.simpleMessage("கடன்"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("கடன் தொகை"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "due தொகை கிடைத்தால் இங்கே காட்டப்படும்"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("due சேகரிப்பு"),
        "dueList": MessageLookupByLibrary.simpleMessage("due பட்டியல்"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("கடந்த பரிவர்த்தனை"),
        "edit": MessageLookupByLibrary.simpleMessage("தொகு"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "சீரியலை திருத்துக/சேர்க்கவும்:"),
        "editYourProfile": MessageLookupByLibrary.simpleMessage(
            "உங்கள் சுயவிவரத்தை திருத்தவும்"),
        "email": MessageLookupByLibrary.simpleMessage("மின்னஞ்சல்"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("தொகையை உள்ளிடவும்"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("பிராண்ட் பெயர் உள்ளிடவும்"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("வகை பெயரை உள்ளிடவும்"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "நிறுவன விளக்கத்தை உள்ளிடவும்"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "நிறுவன மின்னஞ்சல் முகவரியை உள்ளிடவும்"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "நிறுவன தொலைபேசி எண்ணை உள்ளிடவும்"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "நிறுவன வலைத்தள URL ஐ உள்ளிடவும்"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("கஸ்டமர் பெயரை உள்ளிடவும்"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("டீலர் விலை உள்ளிடவும்"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("டிஸ்கाउண்ட் விலை உள்ளிடவும்"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("செலவு வகையை உள்ளிடவும்"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("செலவு தேதியைச் சேர்க்கவும்"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("வருவாயை வகைப்படுத்தவும்"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("வருவாயை தேதி உள்ளிடவும்"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "உற்பத்தியாளர் பெயர் உள்ளிடவும்"),
        "enterName":
            MessageLookupByLibrary.simpleMessage("பெயரைச் சேர்க்கவும்"),
        "enterNames": MessageLookupByLibrary.simpleMessage("பெயர் உள்ளிடவும்"),
        "enterNote": MessageLookupByLibrary.simpleMessage("குறிப்பு எழுதவும்"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("தொடக்க இருப்பு உள்ளிடவும்"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("கட்டண தொகையை உள்ளிடவும்"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("கடவுச்சொல் உள்ளிடவும்"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("கட்டணத் தொகையை உள்ளிடவும்"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("விலை உள்ளிடவும்"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு திறனை உள்ளிடவும்"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage(
            "தயாரிப்பு குறியீடு உள்ளிடவும்"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage(
            "தயாரிப்பு நிறத்தை உள்ளிடவும்"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு பெயர் உள்ளிடவும்"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு அளவு உள்ளிடவும்"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு அளவை உள்ளிடவும்"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு வகை உள்ளிடவும்"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு அலகு உள்ளிடவும்"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு எடையை உள்ளிடவும்"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("கொள்முதல் விலை உள்ளிடவும்"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "குறிப்பு எண்ணைச் சேர்க்கவும்"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("விற்பனை விலையைச் உள்ளிடவும்"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("சீரியல் எண் உள்ளிடவும்"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "செய்தி உள்ளடக்கத்தை உள்ளிடவும்"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("ம stock அளவு உள்ளிடவும்"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("பரிபாலனக் குறி உள்ளிடவும்"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("யூனிட் பெயர் உள்ளிடவும்"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "பயனர் பங்கு பெயரை உள்ளிடவும்"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("பயனர் தலைவனை உள்ளிடவும்"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("உத்தரவாத உள்ளிடவும்"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("மொத்த விலையைச் உள்ளிடவும்"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("உங்கள் தொகையை உள்ளிடவும்"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("உங்கள் முகவரியை உள்ளிடவும்"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "உங்கள் நிறுவன முகவரியை உள்ளிடவும்"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "உங்கள் நிறுவனப் பெயரை உள்ளிடவும்"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "உங்கள் நிறுவனப் பெயரை உள்ளிடவும்"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "உங்கள் மின்னஞ்சல் முகவரியை உள்ளிடவும்"),
        "enterYourPassword": MessageLookupByLibrary.simpleMessage(
            "உங்கள் கடவுச்சொல்லை உள்ளிடவும்"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "உங்கள் கடவுச்சொல்லை மீண்டும் உள்ளிடவும்"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "உங்கள் தொலைபேசி எண்ணை உள்ளிடவும்"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("உங்கள் கடை பெயரை உள்ளிடவும்"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("வகை பெயரைச் சேர்க்கவும்"),
        "expense": MessageLookupByLibrary.simpleMessage("செலவு"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("செலவு தேதி"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("செலவு விவரங்கள்"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("செலவுக்காக"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("செலவு வகை பட்டியல்"),
        "expenses": MessageLookupByLibrary.simpleMessage("செலவுகள்"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "மாதம் சிறந்த ஐந்து வாங்குவது"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("வரம்பில்லா பயன்பாடு செய்யுக"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage(
            "கடவுச்சொல் மறந்துவிட்டீர்களா?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("இலவச தரவு காப்பு"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("இலவச வாழ்க்கை முடிவு"),
        "freePackage": MessageLookupByLibrary.simpleMessage("இலவச திட்டம்"),
        "freePlan": MessageLookupByLibrary.simpleMessage("இலவச திட்டம்"),
        "getStarted": MessageLookupByLibrary.simpleMessage("ஆரம்பிக்கவும்"),
        "govermentId": MessageLookupByLibrary.simpleMessage("அரசு அடையாளம்"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("மொத்தம்"),
        "hold": MessageLookupByLibrary.simpleMessage("பிடி"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("பிடி எண்"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("அடையாள சரிபார்ப்பு"),
        "inc": MessageLookupByLibrary.simpleMessage("வருமானம்"),
        "income": MessageLookupByLibrary.simpleMessage("வருமானம்"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("வருவாயை வகைப்படுத்தல்"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage(
            "வருவாயை வகைப்படுத்தல் பட்டியல்"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("வருவாயை தேதி"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("வருவாயின் விவரங்கள்"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("வருவாயை"),
        "incomeList":
            MessageLookupByLibrary.simpleMessage("வருவாயின் பட்டியல்"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("பொருள் சத்தத்தை அதிகரிக்க"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("உடனடி தனியுரிமை"),
        "invoice": MessageLookupByLibrary.simpleMessage("கணக்காளர்"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("கட்டணம்:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("இன்வாய்ஸ் எண்.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("கட்டண ஆவண எண்."),
        "item": MessageLookupByLibrary.simpleMessage("பொருள்"),
        "itemName": MessageLookupByLibrary.simpleMessage("பொருள் பெயர்"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC சரிபார்ப்பு"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Ledger விவரங்கள்"),
        "ledger": MessageLookupByLibrary.simpleMessage("Ledger"),
        "left": MessageLookupByLibrary.simpleMessage("இடது"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("கடன் கணக்குகள்"),
        "logOut": MessageLookupByLibrary.simpleMessage("லாக் அவுட்"),
        "login": MessageLookupByLibrary.simpleMessage("உள்நுழைவு"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "அழைப்பு கடிதத்தில் லோகோ நிலை?"),
        "loss": MessageLookupByLibrary.simpleMessage("நஷ்டம்"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("இழப்பு/லாபம்"),
        "lossminus": MessageLookupByLibrary.simpleMessage("இழப்பு(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("குறைந்த பங்கு"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("குறைந்த பங்குகள்"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "உங்கள் வாணிக விசாரணையை பிராண்டு விலைச்சீட்டுகளுடன் அந்தர்வு செய்யுங்கள். எங்கள் அவாணிய் மூலத்தை உங்கள் விலைச்சீட்டுகளை தனிப்பயன் செய்ய உம்மெத்தையும் மேம்படுத்தும் அநீதிவார்த்தி செய்திகள் குறித்து உங்கள் மெயில் அல்லது வாட்ஸ்அப் மூலம் உங்கள் கணக்கை அறிவிக்க உள்ளது."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("உற்பத்தியாளர்"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas உள்நுழைவு panel"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas பதிவு panel"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "மொபைல் பயன்பாடு\n+\nடெஸ்க்டாப்"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("பண ரசீது"),
        "nam": MessageLookupByLibrary.simpleMessage("பெயர்*"),
        "name": MessageLookupByLibrary.simpleMessage("பெயர்"),
        "nameCodeOrCateogry": MessageLookupByLibrary.simpleMessage(
            "பெயர் அல்லது குறியீடு அல்லது வகை"),
        "newCusotmers":
            MessageLookupByLibrary.simpleMessage("புதிய வாடிக்கையாளர்கள்"),
        "newCustomers":
            MessageLookupByLibrary.simpleMessage("புதிய வாடிக்கையாளர்கள்"),
        "newIncome": MessageLookupByLibrary.simpleMessage("புதிய வருவாய்"),
        "no": MessageLookupByLibrary.simpleMessage("இல்லை"),
        "noConnection": MessageLookupByLibrary.simpleMessage("இணைப்பு இல்லை"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("கடன் வாடிக்கையாளர் இல்லை"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("கடன் பரிவர்த்தனை இல்லை"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("செலவு வகை இல்லை"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "வருவாயை வகைப்படுத்தல் காணப்படவில்லை"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("வருவாயை காணவில்லை"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("இன்வாய்ஸ் காணவில்லை"),
        "noProductFound": MessageLookupByLibrary.simpleMessage(
            "தயாரிப்பு எதுவும் காணப்படவில்லை"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "கொள்முதல் பரிவர்த்தனை எதுவும் காணப்படவில்லை"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage(
            "விலைப்பட்டியல் எதுவும் காணப்படவில்லை"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("கூறு எதுவும் காணப்படவில்லை"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "விற்பனை பரிவர்த்தனை எதுவும் இல்லை"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "எந்த சீரியல் எண்ணும் காணப்படவில்லை"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("சப்ளையர் எதுவும் இல்லை"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "கணக்கீடு எதுவும் காணப்படவில்லை"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("பயனர் காணப்படவில்லை"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("பயனர் பங்கு காணப்படவில்லை"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("சீரியல் எண் இல்லை"),
        "note": MessageLookupByLibrary.simpleMessage("குறிப்பு"),
        "ok": MessageLookupByLibrary.simpleMessage("சரி"),
        "openCheques": MessageLookupByLibrary.simpleMessage("திறந்த செக்கை"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("தொடக்க இருப்பு"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "அல்லது PNG, JPG ஐ இழுத்து விடுங்கள்"),
        "orders": MessageLookupByLibrary.simpleMessage("ஆர்டர்கள்"),
        "other": MessageLookupByLibrary.simpleMessage("மற்றவை"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("மற்ற வருவாய்"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("திட்ட அம்சம்"),
        "paid": MessageLookupByLibrary.simpleMessage("கொடுக்கப்பட்டது"),
        "paidAmount":
            MessageLookupByLibrary.simpleMessage("செலுத்தப்பட்ட தொகை"),
        "partyName": MessageLookupByLibrary.simpleMessage("கட்சி பெயர்"),
        "partyType": MessageLookupByLibrary.simpleMessage("கட்சி வகை"),
        "password": MessageLookupByLibrary.simpleMessage("கடவுச்சொல்"),
        "payCash": MessageLookupByLibrary.simpleMessage("பணம் செலுத்து"),
        "payable": MessageLookupByLibrary.simpleMessage("கட்டணம்"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("கட்டண தொகை"),
        "payment": MessageLookupByLibrary.simpleMessage("கட்டணம்"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("ஈவுத்தொகை உள்ளே"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("ஈவுத்தொகை வெளியே"),
        "paymentType": MessageLookupByLibrary.simpleMessage("கட்டண வகை"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("கட்டண வகைகள்"),
        "phone": MessageLookupByLibrary.simpleMessage("தொலைபேசி"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("தொலைபேசி எண்"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("தொலைபேசி சரிபார்ப்பு"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "தயவுசெய்து ஒரு விற்பனையைச் சேர்க்கவும்"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("கஸ்டமரை சேர்க்கவும்"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "தயவுசெய்து உங்கள் இணைய இணைப்பைச் சோதிக்கவும்"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "தொலைபேசி பயன்பாட்டைப் பதிவிறக்கவும் மற்றும் பணியாளர் பதிப்பைப் பயன்படுத்த ஒரு பக்கத்தைப் பதிவுசெய்க"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "தயவுசெய்து பொருள் சத்தத்தை உள்ளிடவும்"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("கட்டணமான தரவை உள்ளிடவும்"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "தயவுசெய்து ஒரு வாடிக்கையாளரை தேர்வுசெய்க"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("கட்டாயமான தரவை உள்ளிடவும்"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas உடைந்து பேனல்"),
        "practies": MessageLookupByLibrary.simpleMessage("பயிற்சி"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("பிரீமியம் வாடிக்கை ஆதரண"),
        "premiumPlan":
            MessageLookupByLibrary.simpleMessage("பிரீமியம் திட்டம்"),
        "preview": MessageLookupByLibrary.simpleMessage("முன்னோட்டம்"),
        "previousDue": MessageLookupByLibrary.simpleMessage("முந்தைய தவணை:"),
        "price": MessageLookupByLibrary.simpleMessage("விலை"),
        "print": MessageLookupByLibrary.simpleMessage("அச்சிடுக"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("கட்டண ஆவணம் அச்சிடவும்"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF அச்சிடு"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("தனிப்பயன் கொள்கை"),
        "product": MessageLookupByLibrary.simpleMessage("பொருள்"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு வகை"),
        "productCod":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு குறியீடு*"),
        "productColor": MessageLookupByLibrary.simpleMessage("தயாரிப்பு நிறம்"),
        "productList":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு பட்டியல்"),
        "productNam": MessageLookupByLibrary.simpleMessage("தயாரிப்பு பெயர்*"),
        "productName": MessageLookupByLibrary.simpleMessage("தயாரிப்பு பெயர்"),
        "productSize": MessageLookupByLibrary.simpleMessage("தயாரிப்பு அளவு"),
        "productStock": MessageLookupByLibrary.simpleMessage("பொருள் சத்தம்"),
        "productType": MessageLookupByLibrary.simpleMessage("தயாரிப்பு வகை"),
        "productUnit": MessageLookupByLibrary.simpleMessage("தயாரிப்பு யூனிட்"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு உத்தரவாத"),
        "productWeight": MessageLookupByLibrary.simpleMessage("தயாரிப்பு எடை"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("தயாரிப்பு திறன்"),
        "prof": MessageLookupByLibrary.simpleMessage("சுயவிவரம்"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("சுயவிவரத்தை திருத்து"),
        "profit": MessageLookupByLibrary.simpleMessage("லாபம்"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("லாபம்(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("லாபம்(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("கொள்முதல்"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("கொள்முதல் பட்டியல்"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("பிரீமியம் திட்டம் வாங்க"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("கொள்முதல் விலை"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("கொள்முதல் பரிவர்த்தனை"),
        "quantity": MessageLookupByLibrary.simpleMessage("தொகை"),
        "quotation": MessageLookupByLibrary.simpleMessage("விலைச் சீட்டு"),
        "quotationList": MessageLookupByLibrary.simpleMessage("விலைப்பட்டியல்"),
        "recentSale": MessageLookupByLibrary.simpleMessage("சமீபத்திய விற்பனை"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("பெறப்பட்ட தொகை"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("குறிப்பு எண்."),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("குறிப்பு எண்"),
        "registration": MessageLookupByLibrary.simpleMessage("பதிவு"),
        "remaining": MessageLookupByLibrary.simpleMessage("மீதம்: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("மீதமுள்ள இருப்பு"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("மீதமுள்ள பணம்"),
        "reports": MessageLookupByLibrary.simpleMessage("அறிக்கைகள்"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage(
            "உங்கள் கடவுச்சொல்லை மீட்டமைக்கவும்"),
        "retailer": MessageLookupByLibrary.simpleMessage("மொத்த விற்பனையாளர்"),
        "revenue": MessageLookupByLibrary.simpleMessage("வருமானம்"),
        "right": MessageLookupByLibrary.simpleMessage("வலது"),
        "sAmount": MessageLookupByLibrary.simpleMessage("விற்பனை தொகை"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "உங்கள் வணிக தரவுகளை எளியவாக காப்பாத்தது. எங்கள் Pos Saas POS அனைத்து அதிவரை அப்கேட்டு உள்ளிடுங்கள்டு பிலிக்சுமுந்தல் நியமனம் உங்கள் ம। உங்கள் அம। உங்கள் பிவி பிலிக்சுமுந்தல் தகவல்கள் எந்த எதிர்காலக் நிகழ்ச்சிகள் எந்த எதிர்காலக்கும் பாதுகாப்பப்பட்டுக் கொள்ளுகின்றது. உங்கள் வணிக விளக்குவதை உங்கள் வில்பத்தியை மீற்கின்ற அநீதிவார்த்தி அல்லது வாணிக விசாரணையை வளர்ப்பாடு செய்யும் ஒரு அநீதியத்தை சேர்க்குகின்றது."),
        "sale": MessageLookupByLibrary.simpleMessage("விற்பனை"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("விற்பனை தொகை"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("விற்பனை விவரங்கள்"),
        "saleList": MessageLookupByLibrary.simpleMessage("விற்பனை பட்டியல்"),
        "salePrice": MessageLookupByLibrary.simpleMessage("விற்பனை விலை"),
        "salePrices": MessageLookupByLibrary.simpleMessage("விற்பனை விலை*"),
        "saleReturn":
            MessageLookupByLibrary.simpleMessage("விற்பனை திரும்பப் பெறுதல்"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("விற்பனை பரிவர்த்தனை"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "விற்பனை பரிவர்த்தனைகள் (விலை நிர்ணய விற்பனை வரலாறு)"),
        "sales": MessageLookupByLibrary.simpleMessage("விற்பனை"),
        "salesList": MessageLookupByLibrary.simpleMessage("விற்பனை பட்டியம்"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("சேமித்து வெளியிடவும்"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("சேமித்து வெளியிடவும்"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("மாற்றங்களைச் சேமி"),
        "search": MessageLookupByLibrary.simpleMessage("தேடுக......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("எதையும் தேடுங்கள்..."),
        "searchByInvoice": MessageLookupByLibrary.simpleMessage(
            "செலவு கணக்கீட்டின் மூலம் தேடு"),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "கணக்காளர் அல்லது பெயரால் தேடு"),
        "searchByName": MessageLookupByLibrary.simpleMessage("பெயரால் தேடு"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "பெயர் அல்லது தொலைபேசி மூலம் தேட..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("சீரியல் எண்ணைத் தேடு"),
        "selectParties": MessageLookupByLibrary.simpleMessage(
            "கட்சிகளைத் தேர்ந்தெடுக்கவும்"),
        "selectProductBrand": MessageLookupByLibrary.simpleMessage(
            "தயாரிப்பு பிராண்ட் தேர்வுசெய்க"),
        "selectSerialNumber": MessageLookupByLibrary.simpleMessage(
            "சீரியல் எண்ணைத் தேர்ந்தெடுக்கவும்"),
        "selectVariations": MessageLookupByLibrary.simpleMessage(
            "மாறுபாடுகளை தேர்வுசெய்யவும்:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("உத்தரவாத காலம் தேர்வுசெய்க"),
        "selectYourLanguage": MessageLookupByLibrary.simpleMessage(
            "உங்கள் மொழியைத் தேர்ந்தெடுக்கவும்"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("செய்தி அனுப்ப"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("சீரியல் எண்"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("சீரியல் எண்"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("சேவை கட்டணம்"),
        "setting": MessageLookupByLibrary.simpleMessage("அமைப்பு"),
        "share": MessageLookupByLibrary.simpleMessage("பகிர்"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("கப்பல்/மற்றொன்று"),
        "shopName": MessageLookupByLibrary.simpleMessage("கடை பெயர்"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("கடை திறப்பு இருப்பு"),
        "show": MessageLookupByLibrary.simpleMessage("காட்டு >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "அழைப்பு கடிதத்தில் லோகோ காட்ட வேண்டுமா?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("கடத்தல்/சேவைகள்"),
        "size": MessageLookupByLibrary.simpleMessage("அளவு"),
        "statistic": MessageLookupByLibrary.simpleMessage("புள்ளிவிவரம்"),
        "status": MessageLookupByLibrary.simpleMessage("நிலை"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "கூட்டுவிபர அதிவிவரங்கள் முன்நிலையில் இருந்து உங்களை பிரத்யேக செலவில்லாதிருக்க உள்ளது. எங்கள் Pos Saas POS எக்ஸ்பர்ட் அணி ஆதரித்த குணமாக உங்களை உங்கள் வணிக செயல்பாட்டின் அனைத்து கட்டங்களையும் பயன்பாடு செய்யும்ஆதரணிக்கு கொண்டு வருகிறது."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "தொழில்நுட்ப மேம்பாடுகளின் முன்னிச்சுற்றுவாராது விளக்கு. எங்கள் Pos Sass POS எக்ஸ்பர்ட் அப்கேட்டு உள்ளிடுங்கள், உங்கள் வணிக செயல்பாட்டில் அனைத்து கட்டங்களை உள்ளடக்கி அநுவா஦ித்து உங்கள் வணிக செயலாக்கத்தை அமைக்க உம்மெத்தையும் பொற்று மேற்க்கப்படுத்தும்."),
        "stock": MessageLookupByLibrary.simpleMessage("ம stock"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("பங்கு இருப்பு"),
        "stockReport": MessageLookupByLibrary.simpleMessage("பங்கு அறிக்கை"),
        "stockValue": MessageLookupByLibrary.simpleMessage("பங்கு மதிப்பு"),
        "stockValues": MessageLookupByLibrary.simpleMessage("பங்கு மதிப்புகள்"),
        "subTotal": MessageLookupByLibrary.simpleMessage("கீழ் தொகை"),
        "subciption": MessageLookupByLibrary.simpleMessage("சந்தா"),
        "submit": MessageLookupByLibrary.simpleMessage("சமர்ப்பி"),
        "supplier": MessageLookupByLibrary.simpleMessage("சப்ளையர்கள்"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("சப்ளையர் தங்கியது"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("சப்ளையர் இன்வாய்ஸ்"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("சப்ளையர் பட்டியல்"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT குறி"),
        "tSale": MessageLookupByLibrary.simpleMessage("மொத்த விற்பனை"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "ஓட்டுநர் உரிமம், தேசிய அடையாள அட்டை அல்லது பாஸ்போர்ட் புகைப்படத்தை எடுக்கவும்"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("பயன்பாட்டின் விதிகள்"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "பெயர் அநீதிக்கு அனைத்து உபயோகத்திலும் ஒரு வருகை இல்லாது. கொஞ்சம் வாணிக்கள் கட்டங்கள் செய்யுதல் அல்லது வாணிகர்கள் கூட்டம் அனுபவிப்பதால், நீங்கள் விளக்கப்படுத்தப்பட்டவராக உங்கள் கட்டங்கள் அளவில்லாது என்று அறிந்து வரலாம்"),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "இந்த வாடிக்கையாளருக்கு due இல்லை"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "இந்த வாடிக்கையாளருக்கு முன்கூட்டியே கடன் உள்ளது"),
        "to": MessageLookupByLibrary.simpleMessage("வரை"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("தொடர்புடைய தயாரிப்பு"),
        "total": MessageLookupByLibrary.simpleMessage("மொத்தம்"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("மொத்த தொகை"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("மொத்த தள்ளுபடி"),
        "totalDue": MessageLookupByLibrary.simpleMessage("மொத்த கடன்"),
        "totalDues": MessageLookupByLibrary.simpleMessage("மொத்த பணம்"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("மொத்த செலவு"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("மொத்த வருவாய்"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("மொத்த பொருள்: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("மொத்த இழப்பு"),
        "totalPaid":
            MessageLookupByLibrary.simpleMessage("மொத்தம் செலுத்தப்பட்டது"),
        "totalPayable": MessageLookupByLibrary.simpleMessage(
            "மொத்தம் செலுத்த வேண்டிய தொகை"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("மொத்த ஈவுத்தொகை வெளியே"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("மொத்த விலை"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("மொத்த தயாரிப்பு"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("மொத்த லாபம்"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("மொத்த கொள்முதல்"),
        "totalReturnAmount": MessageLookupByLibrary.simpleMessage(
            "மொத்த திரும்பப் பெறப்பட்ட தொகை"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("மொத்த திரும்பப் பெறுதல்கள்"),
        "totalSale": MessageLookupByLibrary.simpleMessage("மொத்த விற்பனை"),
        "totalSales": MessageLookupByLibrary.simpleMessage("மொத்த விற்பனை"),
        "totalVat": MessageLookupByLibrary.simpleMessage("மொத்த வரி"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("மொத்த ஈவுத்தொகை உள்ளே"),
        "transaction": MessageLookupByLibrary.simpleMessage("கட transactions"),
        "transactionId": MessageLookupByLibrary.simpleMessage("பரிபாலனக் குறி"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("பரிவர்த்தனை அறிக்கை"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("மீண்டும் முயற்சிக்க"),
        "type": MessageLookupByLibrary.simpleMessage("வகை"),
        "unPaid": MessageLookupByLibrary.simpleMessage("கட்டப்படாதது"),
        "unit": MessageLookupByLibrary.simpleMessage("அலகு"),
        "unitName": MessageLookupByLibrary.simpleMessage("யூனிட் பெயர்"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("யூனிட் விலை"),
        "unlimited": MessageLookupByLibrary.simpleMessage("வரம்பில்லா"),
        "unlimitedInvoice": MessageLookupByLibrary.simpleMessage(
            "முடிவில்லா விலைப்பட்டியல்கள்"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("வரம்பில்லா பயன்பாடு"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "உங்கள் வணிக செயல்பாடுகளை செயல்படுத்த, எங்கள் மூல அணியால் கொண்டு வாரியங்களில் வழக்கவர்க்கு உங்கள் அனைத்து கட்டங்களை செயலாக்குவதில் அற்புதமாக அற்புதமாக அறிவற்றுப் பயன்பாடு செய்யும் உங்களை விசாரணையாக அமைக்க உள்ளது."),
        "updateNow": MessageLookupByLibrary.simpleMessage("இப்போ புதுப்பிக்க"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "உங்கள் திட்டத்தை முதலில் புதுப்பிக்கவும்\\nவிற்பனை வரம்பு முடிவடைந்தது."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "மொபைல் பயன்பாட்டில் மேம்படுத்தவும்"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("ஒரு படத்தைப் பதிவேற்றவும்"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "அழைப்பு கடித லோகோ பதிவேற்றம்"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("ஆவணம் பதிவேற்று"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("கோபம் பதிவேற்று"),
        "userName": MessageLookupByLibrary.simpleMessage("பயனர் பெயர்"),
        "userRole": MessageLookupByLibrary.simpleMessage("பயனர் பங்கு"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("பயனர் பங்கு பெயர்"),
        "userTitle": MessageLookupByLibrary.simpleMessage("பயனர் தலை"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("வரி/ஜிஎஸ்டி"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "தொலைபேசி எண்ணை சரிபார்க்கவும்"),
        "view": MessageLookupByLibrary.simpleMessage("பார்வை"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("நடைபெறும் வாடிக்கையாளர்"),
        "warranty": MessageLookupByLibrary.simpleMessage("உத்தரவாத"),
        "warrantys": MessageLookupByLibrary.simpleMessage("உத்தரவாத"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "தொடங்குவதற்கு முன் உங்கள் தொகையை பதிவு செய்ய வேண்டும்!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "மண்டப்பொறியேன்கள் உங்கள் வணிக செயலாக்கங்கள் முதல் விபரங்கள் வரை முடிவான ஆதரணம் முதல் விளக்கிய ஆதரணம் அநுமத்தும், உங்கள் உத்திமம் அநுமத்தில் உங்கள் விளக்குதல் ஐ மற reinforcingார் உங்கள் முதல் அடைவு மற்று வாணிக விசாரணையை உண்டுத்தில் வைப்பதை சாரமாகக் கொண்டு செய்கிறது."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("மொத்த விற்பனை விலை"),
        "wholeSeller":
            MessageLookupByLibrary.simpleMessage("மொத்த விற்பனையாளர்"),
        "wholesale": MessageLookupByLibrary.simpleMessage("மொத்த விற்பனை"),
        "wight": MessageLookupByLibrary.simpleMessage("எடை"),
        "yesReturn":
            MessageLookupByLibrary.simpleMessage("ஆம் திரும்பப் பெறுதல்"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "உங்கள் கணக்கில் மீண்டும் உள்நுழைந்து கொள்ள வேண்டும்."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "செய்திகளை வாங்குவதற்கு முன்பு நீங்கள் அடையாள சரிபார்ப்பு செய்ய வேண்டும்"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "உங்கள் அனைத்து விற்பனை பட்டியல்"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("உங்கள் அனைத்து விற்பனைகளும்"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage(
            "நீங்கள் பயன்படுத்துகின்றீர்கள்"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("உங்கள் due sales"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "செய்திகளை வாங்குவதற்கு முன்பு நீங்கள் அடையாள சரிபார்ப்பு செய்ய வேண்டும்"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("உங்கள் பொருட்டம்"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "உங்கள் பணம் ரத்து செய்யப்பட்டுள்ளது"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "உங்கள் பணம் வெற்றிகரமாக உள்ளது"),
        "yourPaymentIscancelled": MessageLookupByLibrary.simpleMessage(
            "உங்கள் பணம் ரத்து செய்யப்பட்டுள்ளது")
      };
}
