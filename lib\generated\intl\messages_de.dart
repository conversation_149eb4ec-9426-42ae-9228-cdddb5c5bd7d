// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a de locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'de';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("VERKAUF HINZUFÜGEN"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIE"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("RECHNUNG"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS-Verkauf"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PREIS"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("PRODUKTNAME"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Anmeldungs-Panel"),
        "QTY": MessageLookupByLibrary.simpleMessage("MENGE"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Menge*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("GESAMTWERT"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Benutzertitel"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Über die App"),
        "accountName": MessageLookupByLibrary.simpleMessage("Kontoname"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Kontonummer"),
        "action": MessageLookupByLibrary.simpleMessage("Aktion"),
        "add": MessageLookupByLibrary.simpleMessage("Hinzufügen"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Marke hinzufügen"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Kategorie hinzufügen"),
        "addCustomer":
            MessageLookupByLibrary.simpleMessage("Kunden hinzufügen"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Beschreibung hinzufügen..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Dokument hinzufügen"),
        "addItem": MessageLookupByLibrary.simpleMessage("Artikel hinzufügen"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Artikelkategorie hinzufügen"),
        "addNew": MessageLookupByLibrary.simpleMessage("Neu hinzufügen"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Neuen Benutzer hinzufügen"),
        "addProduct":
            MessageLookupByLibrary.simpleMessage("Produkt hinzufügen"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Erfolgreich hinzugefügt"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Lieferanten hinzufügen"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Einheit hinzufügen"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Kostenliste hinzufügen/aktualisieren"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Einkommensliste hinzufügen/aktualisieren"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Benutzerrolle hinzufügen"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriennummer hinzufügen?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresse"),
        "all": MessageLookupByLibrary.simpleMessage("Alle"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Alle Basisfunktionen"),
        "alreadyHaveAnAccounts": MessageLookupByLibrary.simpleMessage(
            "Haben Sie bereits ein Konto?"),
        "amount": MessageLookupByLibrary.simpleMessage("Betrag"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android & iOS App-Unterstützung"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Möchten Sie diese Quotation erstellen?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Möchten Sie diesen Kunden löschen?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Möchten Sie dieses Produkt löschen"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Möchten Sie dieses Angebot löschen?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Möchten Sie diesen Verkauf zurückgeben?"),
        "balance": MessageLookupByLibrary.simpleMessage("Gleichgewicht"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Währung des Bankkontos"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankkonten"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankinformationen"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bankname"),
        "between": MessageLookupByLibrary.simpleMessage("Zwischen"),
        "billTo": MessageLookupByLibrary.simpleMessage("Rechnung an:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Zweigstellenname"),
        "brand": MessageLookupByLibrary.simpleMessage("Marke"),
        "brandName": MessageLookupByLibrary.simpleMessage("Markenname"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Geschäftskategorie"),
        "buy": MessageLookupByLibrary.simpleMessage("Kaufen"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium-Plan kaufen"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMS kaufen"),
        "calculator": MessageLookupByLibrary.simpleMessage("Rechner:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Abbrechen"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapazität"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Bargeld und Bank"),
        "cashInHand":
            MessageLookupByLibrary.simpleMessage("Bargeld in der Hand"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorien"),
        "category": MessageLookupByLibrary.simpleMessage("Kategorie"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategoriename"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Wechselgeld"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Veränderbare Menge"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Garantie prüfen"),
        "choseAplan":
            MessageLookupByLibrary.simpleMessage("Wählen Sie einen Plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Forderungen einziehen >"),
        "color": MessageLookupByLibrary.simpleMessage("Farbe"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Firmenname"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Firmenadresse"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Firmenbeschreibung"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Firmen-E-Mail-Adresse"),
        "companyName": MessageLookupByLibrary.simpleMessage("Firmenname"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Firmentelefonnummer"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Firmen-Website-URL"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Passwort bestätigen"),
        "continu": MessageLookupByLibrary.simpleMessage("Fortsetzen"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("In Verkauf umwandeln"),
        "create": MessageLookupByLibrary.simpleMessage("Erstellen"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Zahlung erstellen"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Erstellt von"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreatives Hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Währung"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Aktueller Plan"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Individuelles Rechnungsdesign"),
        "customer": MessageLookupByLibrary.simpleMessage("Kunde"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Kundenguthaben"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Kundenrechnungen"),
        "customerList": MessageLookupByLibrary.simpleMessage("Kundenliste"),
        "customerName": MessageLookupByLibrary.simpleMessage("Kundenname"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kunde des Monats"),
        "customerType": MessageLookupByLibrary.simpleMessage("Kundentyp"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Kunde: Laufkundschaft"),
        "customers": MessageLookupByLibrary.simpleMessage("Kunden"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Tageseinnahmen"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Tagesumsatz"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Tägliche Transaktion"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum und Uhrzeit"),
        "dealer": MessageLookupByLibrary.simpleMessage("Händler"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Händlerpreis"),
        "delete": MessageLookupByLibrary.simpleMessage("Löschen"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("Liefergebühr"),
        "description": MessageLookupByLibrary.simpleMessage("Beschreibung"),
        "details": MessageLookupByLibrary.simpleMessage("Details >"),
        "discount": MessageLookupByLibrary.simpleMessage("Rabatt"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Rabattpreis"),
        "downloadPDF":
            MessageLookupByLibrary.simpleMessage("PDF herunterladen"),
        "due": MessageLookupByLibrary.simpleMessage("Fällig"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Restbetrag"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Der fällige Betrag wird hier angezeigt, falls vorhanden"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Fälligkeitssammlung"),
        "dueList": MessageLookupByLibrary.simpleMessage("Fällige Liste"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Fällige Transaktion"),
        "edit": MessageLookupByLibrary.simpleMessage("Bearbeiten"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Seriennummer bearbeiten/hinzufügen:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Bearbeiten Sie Ihr Profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("Geben Sie einen Betrag ein"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Markennamen eingeben"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Kategorienamen eingeben"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Geben Sie die Firmenbeschreibung ein"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Geben Sie die Firmen-E-Mail-Adresse ein"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Geben Sie die Firmentelefonnummer ein"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Geben Sie die Firmen-Website-URL ein"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage(
            "Geben Sie den Kundennamen ein"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Händlerpreis eingeben"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Geben Sie den Rabattpreis ein"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Kostenart eingeben"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Kostendatum eingeben"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Einkommenskategorie eingeben"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage(
            "Geben Sie das Einkommensdatum ein"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Herstellernamen eingeben"),
        "enterName": MessageLookupByLibrary.simpleMessage("Name eingeben"),
        "enterNames":
            MessageLookupByLibrary.simpleMessage("Geben Sie einen Namen ein"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Notiz eingeben"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Geben Sie den Eröffnungssaldo ein"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Zahlungsbetrag eingeben"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Passwort eingeben"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Zahlungsbetrag eingeben"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Preis eingeben"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Produktkapazität eingeben"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Produktcode eingeben"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Produktfarbe eingeben"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Produktnamen eingeben"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Produktmenge eingeben"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Produktgröße eingeben"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Produkttyp eingeben"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Produkteinheit eingeben"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Produktgewicht eingeben"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Kaufpreisen eingeben"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Referenznummer eingeben"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Verkaufspreis eingeben"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriennummer eingeben"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Geben Sie den Nachrichteninhalt ein"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Geben Sie den Lagerbestand ein"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Transaktions-ID eingeben"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Einheitsnamen eingeben"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Name der Benutzerrolle eingeben"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Benutzertitel eingeben"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Garantie eingeben"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Großhandelspreis eingeben"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Geben Sie Ihren Betrag ein"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Geben Sie Ihre Adresse ein"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihre Firmenadresse ein"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihren Firmennamen ein"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihren Firmennamen ein"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihre E-Mail-Adresse ein"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Geben Sie Ihr Passwort ein"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihr Passwort erneut ein"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihre Telefonnummer ein"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Geben Sie Ihren Shop-Namen ein"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Kategorienamen eingeben"),
        "expense": MessageLookupByLibrary.simpleMessage("Kosten"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Kostendatum"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("Kostendetails"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Kosten für"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Liste der Kostenarten"),
        "expenses": MessageLookupByLibrary.simpleMessage("Ausgaben"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top fünf Kaufprodukte des Monats"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Für unbegrenzte Verwendung"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Passwort vergessen?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Kostenloses Datenbackup"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Kostenloses lebenslanges Update"),
        "freePackage":
            MessageLookupByLibrary.simpleMessage("Kostenloses Paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Kostenloser Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Los geht\'s"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Regierungsidentität"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Gesamtsumme"),
        "hold": MessageLookupByLibrary.simpleMessage("Halt"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Haltnummer"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Identitätsüberprüfung"),
        "inc": MessageLookupByLibrary.simpleMessage("Einkommen"),
        "income": MessageLookupByLibrary.simpleMessage("Einkommen"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Einkommenskategorie"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Einkommenskategorienliste"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Einkommensdatum"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Einkommensdetails"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Einkommen für"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Einkommensliste"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Bestand erhöhen"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Sofortige Privatsphäre"),
        "invoice": MessageLookupByLibrary.simpleMessage("Rechnung"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Rechnung:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Rechnungsnummer..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Rechnungsnummer"),
        "item": MessageLookupByLibrary.simpleMessage("Artikel"),
        "itemName": MessageLookupByLibrary.simpleMessage("Artikelname"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC-Verifizierung"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Ledger-Details"),
        "ledger": MessageLookupByLibrary.simpleMessage("Hauptbuch"),
        "left": MessageLookupByLibrary.simpleMessage("Links"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Kreditkonten"),
        "logOut": MessageLookupByLibrary.simpleMessage("Abmelden"),
        "login": MessageLookupByLibrary.simpleMessage("Anmeldung"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logoposition in Rechnung?"),
        "loss": MessageLookupByLibrary.simpleMessage("Verlust"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Verlust/Gewinn"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Verlust(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Niedriger Bestand"),
        "lowStocks":
            MessageLookupByLibrary.simpleMessage("Niedriger Lagerbestand"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Hinterlassen Sie einen bleibenden Eindruck bei Ihren Kunden mit individuell gestalteten Rechnungen. Unser unbegrenztes Upgrade bietet den einzigartigen Vorteil, Ihre Rechnungen anzupassen und ihnen eine professionelle Note zu verleihen, die Ihre Markenidentität stärkt und die Kundentreue fördert."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Hersteller"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Login Panel"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Signup Panel"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobile App\n+\nDesktop"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Geldschein"),
        "nam": MessageLookupByLibrary.simpleMessage("Name*"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Name, Code oder Kategorie"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Neue Kunden"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Neue Kunden"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Neues Einkommen"),
        "no": MessageLookupByLibrary.simpleMessage("Nein"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Keine Verbindung"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Kein Kunde gefunden"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Keine fälligen Transaktionen gefunden"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("Keine Kostenart gefunden"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Keine Einkommenskategorie gefunden"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Kein Einkommen gefunden"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Keine Rechnung gefunden"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Kein Produkt gefunden"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Keine Einkaufstransaktion gefunden"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Kein Angebot gefunden"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("KEIN Bericht gefunden"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Keine Verkaufstransaktion gefunden"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Keine Seriennummer gefunden"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Kein Lieferant gefunden"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Keine Transaktion gefunden"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Kein Benutzer gefunden"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Keine Benutzerrolle gefunden"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Keine Seriennummer gefunden"),
        "note": MessageLookupByLibrary.simpleMessage("Notiz"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Offene Schecks"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Eröffnungssaldo"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "oder ziehen und ablegen von PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Bestellungen"),
        "other": MessageLookupByLibrary.simpleMessage("Andere"),
        "otherIncome":
            MessageLookupByLibrary.simpleMessage("Anderes Einkommen"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("Paketfunktion"),
        "paid": MessageLookupByLibrary.simpleMessage("Bezahlt"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Bezahlter Betrag"),
        "partyName": MessageLookupByLibrary.simpleMessage("Parteiname"),
        "partyType": MessageLookupByLibrary.simpleMessage("Parteityp"),
        "password": MessageLookupByLibrary.simpleMessage("Passwort"),
        "payCash": MessageLookupByLibrary.simpleMessage("Barzahlung"),
        "payable": MessageLookupByLibrary.simpleMessage("Zahlbar"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Zahlungsbetrag"),
        "payment": MessageLookupByLibrary.simpleMessage("Zahlung"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Zahlung ein"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Zahlung aus"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Zahlungsart"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Zahlungsarten"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefonnummer"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefonüberprüfung"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Bitte fügen Sie einen Verkauf hinzu"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Bitte Kunden hinzufügen"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Bitte überprüfen Sie Ihre Internetverbindung"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Bitte laden Sie unsere mobile App herunter und abonnieren Sie ein Paket, um die Desktop-Version zu verwenden"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Bitte geben Sie den Produktbestand ein"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Bitte geben Sie gültige Daten ein"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Bitte wählen Sie einen Kunden aus"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Bitte geben Sie gültige Daten ein"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Registrierungs-Panel"),
        "practies": MessageLookupByLibrary.simpleMessage("Üben"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium-Kundensupport"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium-Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Vorschau"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Vorherige Schulden:"),
        "price": MessageLookupByLibrary.simpleMessage("Preis"),
        "print": MessageLookupByLibrary.simpleMessage("Drucken"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Rechnung drucken"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF drucken"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Datenschutzrichtlinie"),
        "product": MessageLookupByLibrary.simpleMessage("Produkt"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Produktkategorie"),
        "productCod": MessageLookupByLibrary.simpleMessage("Produktcode*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Produktfarbe"),
        "productList": MessageLookupByLibrary.simpleMessage("Produktliste"),
        "productNam": MessageLookupByLibrary.simpleMessage("Produktname*"),
        "productName": MessageLookupByLibrary.simpleMessage("Produktname"),
        "productSize": MessageLookupByLibrary.simpleMessage("Produktgröße"),
        "productStock": MessageLookupByLibrary.simpleMessage("Produktbestand"),
        "productType": MessageLookupByLibrary.simpleMessage("Produkttyp"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Produkteinheit"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Produktgarantie"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Produktgewicht"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Produktkapazität"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Profil bearbeiten"),
        "profit": MessageLookupByLibrary.simpleMessage("Gewinn"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Gewinn(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Gewinn(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Einkauf"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Einkaufsliste"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium-Plan kaufen"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Kaufpreis"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Kauftransaktion"),
        "quantity": MessageLookupByLibrary.simpleMessage("Menge"),
        "quotation": MessageLookupByLibrary.simpleMessage("Kaufangebot"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Angebotsliste"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Letzte Verkäufe"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Empfangener Betrag"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referenz-Nr."),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referenznummer"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrierung"),
        "remaining": MessageLookupByLibrary.simpleMessage("Verbleibend: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Restguthaben"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Restbetrag"),
        "reports": MessageLookupByLibrary.simpleMessage("Berichte"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Passwort zurücksetzen"),
        "retailer": MessageLookupByLibrary.simpleMessage("Händler"),
        "revenue": MessageLookupByLibrary.simpleMessage("Umsatz"),
        "right": MessageLookupByLibrary.simpleMessage("Rechts"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Verkaufsbetrag"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Schützen Sie Ihre Geschäftsdaten mühelos. Unser Pos Saas POS Unlimited Upgrade beinhaltet ein kostenloses Datenbackup, das sicherstellt, dass Ihre wertvollen Informationen vor unvorhergesehenen Ereignissen geschützt sind. Konzentrieren Sie sich auf das, was wirklich zählt - das Wachstum Ihres Unternehmens."),
        "sale": MessageLookupByLibrary.simpleMessage("Verkauf"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Verkaufsbetrag"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Verkaufsdetails"),
        "saleList": MessageLookupByLibrary.simpleMessage("Verkaufsliste"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Verkaufspreis"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Verkaufspreise*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Verkaufsrückgabe"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Verkaufstransaktion"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Verkaufstransaktionen (Angebotsverkaufshistorie)"),
        "sales": MessageLookupByLibrary.simpleMessage("Verkäufe"),
        "salesList": MessageLookupByLibrary.simpleMessage("Verkaufsliste"),
        "saveAndPublish": MessageLookupByLibrary.simpleMessage(
            "Speichern und Veröffentlichen"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Speichern & Veröffentlichen"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Änderungen speichern"),
        "search": MessageLookupByLibrary.simpleMessage("Suche ..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Etwas suchen..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Nach Rechnung suchen..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Nach Rechnung oder Name suchen"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Nach Name suchen"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Nach Name oder Telefon suchen..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriennummer suchen"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Parteien auswählen"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Produktmarke auswählen"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriennummer auswählen"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Variationen auswählen:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Garantiezeit auswählen"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Wählen Sie Ihre Sprache"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Nachricht senden"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Seriennummer"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Seriennummern"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Servicegebühr"),
        "setting": MessageLookupByLibrary.simpleMessage("Einstellung"),
        "share": MessageLookupByLibrary.simpleMessage("Teilen"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Versand/Sonstiges"),
        "shopName": MessageLookupByLibrary.simpleMessage("Shop-Name"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Shop-Eröffnungssaldo"),
        "show": MessageLookupByLibrary.simpleMessage("Anzeigen >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Logo in Rechnung anzeigen?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Versand/Dienstleistungen"),
        "size": MessageLookupByLibrary.simpleMessage("Größe"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistik"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Bleiben Sie an vorderster Front der technologischen Fortschritte, ohne zusätzliche Kosten. Unsere Pos Saas POS Unlimited Upgrade stellt sicher, dass Sie immer die neuesten Tools und Funktionen zur Hand haben und Ihr Unternehmen stets auf dem neuesten Stand bleibt."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Bleiben Sie an der Spitze der technologischen Entwicklungen, ohne zusätzliche Kosten. Unsere Pos Sass POS Unlimited Upgrade stellt sicher, dass Sie immer die neuesten Tools und Funktionen zur Hand haben und Ihr Unternehmen stets auf dem neuesten Stand bleibt."),
        "stock": MessageLookupByLibrary.simpleMessage("Lagerbestand"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("Lagerbestand"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Lagerbestandsbericht"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Lagerwert"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Bestandswerte"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Zwischensumme"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abonnement"),
        "submit": MessageLookupByLibrary.simpleMessage("Absenden"),
        "supplier": MessageLookupByLibrary.simpleMessage("Lieferanten"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Lieferantenguthaben"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Lieferantenrechnung"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lieferantenliste"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT-Code"),
        "tSale": MessageLookupByLibrary.simpleMessage("Gesamtverkäufe"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Nehmen Sie ein Foto von einem Führerschein, Personalausweis oder Reisepass"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Nutzungsbedingungen"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Der Name sagt alles. Mit Pos Saas POS Unlimited gibt es keine Begrenzung Ihrer Nutzung. Egal, ob Sie eine Handvoll Transaktionen bearbeiten oder einen Ansturm von Kunden erleben, Sie können selbstbewusst arbeiten, ohne durch Einschränkungen behindert zu werden."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Dieser Kunde hat keine fälligen Beträge"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Dieser Kunde hat einen vorherigen fälligen Betrag"),
        "to": MessageLookupByLibrary.simpleMessage("Nach"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Top-Verkaufsprodukt"),
        "total": MessageLookupByLibrary.simpleMessage("Gesamt"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Gesamtbetrag"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Gesamtrabatt"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Gesamtbetrag"),
        "totalDues":
            MessageLookupByLibrary.simpleMessage("Gesamtbetrag fällig"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Gesamtkosten"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Gesamteinkommen"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Gesamtartikel: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Gesamtverlust"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Gesamt gezahlt"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Summe zahlbar"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Gesamte Zahlung aus"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Gesamtpreis"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Gesamte Produkte"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Gesamtgewinn"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Gesamteinkauf"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Gesamtrückgabebetrag"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Gesamtrückgaben"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Gesamtverkauf"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Gesamtumsatz"),
        "totalVat":
            MessageLookupByLibrary.simpleMessage("Gesamt-Mehrwertsteuer"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Gesamte Zahlung ein"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaktion"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Transaktions-ID"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Transaktionsbericht"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Erneut versuchen"),
        "type": MessageLookupByLibrary.simpleMessage("Typ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Unbezahlt"),
        "unit": MessageLookupByLibrary.simpleMessage("Einheit"),
        "unitName": MessageLookupByLibrary.simpleMessage("Einheitsname"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Einheitspreis"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Unbegrenzt"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Unbegrenzte Rechnungen"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Unbegrenzte Nutzung"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Schalten Sie das volle Potenzial von Pos Saas POS mit personalisierten Schulungssitzungen frei, die von unserem Expertenteam geleitet werden. Von den Grundlagen bis zu fortgeschrittenen Techniken sorgen wir dafür, dass Sie sich gut mit jeder Facette des Systems auskennen, um Ihre Geschäftsprozesse zu optimieren."),
        "updateNow":
            MessageLookupByLibrary.simpleMessage("Jetzt aktualisieren"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Aktualisieren Sie zuerst Ihren Plan\\nDer Verkaufslimit ist überschritten."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Upgrade auf der mobilen App"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Bild hochladen"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Rechnungslogo hochladen"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Dokument hochladen"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Datei hochladen"),
        "userName": MessageLookupByLibrary.simpleMessage("Benutzername"),
        "userRole": MessageLookupByLibrary.simpleMessage("Benutzerrolle"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Name der Benutzerrolle"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Benutzertitel"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("MwSt./GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefonnummer verifizieren"),
        "view": MessageLookupByLibrary.simpleMessage("Ansicht"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Laufkundschaft"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantie"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garantien"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Wir müssen Ihr Telefon vor dem Start registrieren!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Wir verstehen die Bedeutung reibungsloser Abläufe. Deshalb steht Ihnen unser Rund-um-die-Uhr-Support zur Verfügung, egal ob es sich um eine schnelle Anfrage oder eine umfassende Angelegenheit handelt. Kontaktieren Sie uns jederzeit und überall per Anruf oder WhatsApp, um unseren erstklassigen Kundenservice zu erleben."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Großhandelspreis"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Großhändler"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Großhandel"),
        "wight": MessageLookupByLibrary.simpleMessage("Gewicht"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ja, zurückgeben"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Sie müssen sich ERNEUT ANMELDEN, um fortzufahren."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Sie müssen sich vor dem Kauf von Nachrichten identifizieren"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Ihre Liste aller Verkäufe"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Alle Ihre Verkäufe"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Sie verwenden"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Ihre fälligen Verkäufe"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Sie müssen sich vor dem Kauf von Nachrichten identifizieren"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Ihr Paket"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "Ihre Zahlung wurde storniert"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Ihre Zahlung war erfolgreich"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Ihre Zahlung wurde storniert")
      };
}
