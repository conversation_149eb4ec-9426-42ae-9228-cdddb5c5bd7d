// Web-specific implementations
import 'dart:html' as html;

class PlatformHelper {
  static void setBeforeUnloadListener() {
    html.window.onBeforeUnload.listen((event) async {});
  }
  
  static void redirectToUrl(String url) {
    html.window.location.href = url;
  }
  
  static void downloadFile(String url, String filename) {
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', filename)
      ..click();
  }

  static void downloadBase64File(String base64Data, String filename) {
    final anchor = html.AnchorElement(href: "data:application/octet-stream;charset=utf-16le;base64,$base64Data")
      ..setAttribute('download', filename)
      ..click();
  }
  
  static void openInNewTab(String url) {
    html.window.open(url, '_blank');
  }
  
  static String getCurrentUrl() {
    return html.window.location.href;
  }
  
  static List<String> getPathSegments() {
    return Uri.base.pathSegments;
  }
}
