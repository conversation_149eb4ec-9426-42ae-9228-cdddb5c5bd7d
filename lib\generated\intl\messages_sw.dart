// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a sw locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'sw';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("ONGEZA UUZAJI"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("JAMII"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("HOJA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Uuzaji wa POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("BEI"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("JINA LA BIDHAA"),
        "QTY": MessageLookupByLibrary.simpleMessage("KIDATO"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Wingi*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("HADHI"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("JUMLA THAMANI"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Cheo cha Mtumiaji"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Kuhusu Programu"),
        "accountName": MessageLookupByLibrary.simpleMessage("Jina la Akaunti"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Nambari ya Akaunti"),
        "action": MessageLookupByLibrary.simpleMessage("Hatua"),
        "add": MessageLookupByLibrary.simpleMessage("Ongeza"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Ongeza Chapa"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Ongeza Aina"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Ongeza Mteja"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Ongeza maelezo...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Ongeza Nyaraka"),
        "addItem": MessageLookupByLibrary.simpleMessage("Ongeza Bidhaa"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Ongeza Jamii ya Bidhaa"),
        "addNew": MessageLookupByLibrary.simpleMessage("Ongeza Mpya"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Ongeza Mtumiaji Mpya"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Ongeza Bidhaa"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Umeongeza Kwa Mafanikio"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Ongeza Muuzaji"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Ongeza Kipimo"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Ongeza/Boresha Orodha ya Matumizi"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Ongeza/Update Orodha ya Mapato"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Ongeza Wajibu wa Mtumiaji"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Unataka Kuongeza Nambari za Siri?"),
        "address": MessageLookupByLibrary.simpleMessage("Anwani"),
        "all": MessageLookupByLibrary.simpleMessage("Yote"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Vipengele Vyote Msingi"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Tayari una akaunti?"),
        "amount": MessageLookupByLibrary.simpleMessage("Kiasi"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Msaada wa Programu za Android na iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Je, unataka kuunda Hati ya Makadirio haya?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Je, unataka kufuta Mteja huyu?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Je, unataka kufuta bidhaa hii?"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Je, unataka kufuta Kadirio hili?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Je, unataka kurudisha uuzaji huu?"),
        "balance": MessageLookupByLibrary.simpleMessage("Salio"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Sarafu ya Akaunti ya Benki"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Akaunti za Benki"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Taarifa za Benki"),
        "bankName": MessageLookupByLibrary.simpleMessage("Jina la Benki"),
        "between": MessageLookupByLibrary.simpleMessage("Kati ya"),
        "billTo": MessageLookupByLibrary.simpleMessage("Kwa:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Jina la Tawi"),
        "brand": MessageLookupByLibrary.simpleMessage("Chapa"),
        "brandName": MessageLookupByLibrary.simpleMessage("Jina la Chapa"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Jamii ya Biashara"),
        "buy": MessageLookupByLibrary.simpleMessage("Kununua"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Nunua Mpango wa Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Nunua ujumbe"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kielelezo:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Ghairi"),
        "capacity": MessageLookupByLibrary.simpleMessage("Uwezo"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Fedha na Benki"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Fedha Mkoni"),
        "categories": MessageLookupByLibrary.simpleMessage("Jamii"),
        "category": MessageLookupByLibrary.simpleMessage("Aina"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Jina la Aina"),
        "changeAmount":
            MessageLookupByLibrary.simpleMessage("Kiasi cha Kubadilisha"),
        "changeableAmount": MessageLookupByLibrary.simpleMessage(
            "Kiasi Kinachoweza Kubadilishwa"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Angalia Dhamana"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Chagua kifurushi"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Kusanya Inayodaiwa >"),
        "color": MessageLookupByLibrary.simpleMessage(" rangi"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Jina la Kampuni"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Anwani ya Kampuni"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Maelezo ya Kampuni"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Barua pepe ya Kampuni"),
        "companyName": MessageLookupByLibrary.simpleMessage("Jina la Kampuni"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Nambari ya Simu ya Kampuni"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL ya Tovuti ya Kampuni"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Thibitisha nenosiri"),
        "continu": MessageLookupByLibrary.simpleMessage("Endelea"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Geuza Kuwa Uuzaji"),
        "create": MessageLookupByLibrary.simpleMessage("Unda"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Tengeneza Malipo"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Imetengenezwa na"),
        "creativeHub":
            MessageLookupByLibrary.simpleMessage("Kitovu cha Ubunifu"),
        "currency": MessageLookupByLibrary.simpleMessage("sarafu"),
        "currentPlan":
            MessageLookupByLibrary.simpleMessage("Kifurushi Cha Sasa"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Ubunifu wa Bili za Kibinafsi"),
        "customer": MessageLookupByLibrary.simpleMessage("Mteja"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Mteja Ana Deni"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Bili za Wateja"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Orodha ya Wateja"),
        "customerName": MessageLookupByLibrary.simpleMessage("Jina la Mteja"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Mteja wa Mwezi"),
        "customerType": MessageLookupByLibrary.simpleMessage("Aina ya Mteja"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Mteja: Mteja Anayeingia"),
        "customers": MessageLookupByLibrary.simpleMessage("Wateja"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Ukusanyaji wa Kila Siku"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Uuzaji wa Kila Siku"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Shughuli za Kila Siku"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dashibodi"),
        "date": MessageLookupByLibrary.simpleMessage("Tarehe"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Tarehe na Wakati"),
        "dealer": MessageLookupByLibrary.simpleMessage("Muuzaji"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Bei ya Muuzaji"),
        "delete": MessageLookupByLibrary.simpleMessage("Futa"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Gharama ya Kufikisha"),
        "description": MessageLookupByLibrary.simpleMessage("Maelezo"),
        "details": MessageLookupByLibrary.simpleMessage("Maelezo >"),
        "discount": MessageLookupByLibrary.simpleMessage("Punguzo"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Bei ya Punguzo"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Pakua PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Deni"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Kiasi Kinachodaiwa"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Kiasi Kinachodaiwa kitajionyesha hapa iwapo kinapatikana"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Ukusanyaji wa Deni"),
        "dueList": MessageLookupByLibrary.simpleMessage("Orodha ya Madeni"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Shughuli Zinazotakiwa"),
        "edit": MessageLookupByLibrary.simpleMessage("Hariri"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Hariri/Ongeza Nambari ya Siri:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Hariri Wasifu Wako"),
        "email": MessageLookupByLibrary.simpleMessage("Barua pepe"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Ingiza Kiasi"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Chapa"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Jamii"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Ingiza Maelezo ya Kampuni"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Ingiza Barua pepe ya Kampuni"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Ingiza Nambari ya Simu ya Kampuni"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Ingiza URL ya Tovuti ya Kampuni"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Mteja"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Ingiza Bei ya Muuzaji"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Ingiza Bei ya Punguzo"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Ingiza Aina ya Matumizi"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Ingiza Tarehe ya Matumizi"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Ingiza Aina ya Mapato"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Ingiza Tarehe ya Mapato"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Mzalishaji"),
        "enterName": MessageLookupByLibrary.simpleMessage("Ingiza Jina"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Ingiza Jina"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Ingiza Kumbukumbu"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Ingiza Salio la Kufungua"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Ingiza Kiasi Kilicholipwa"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Ingiza Kiasi cha Kulipia"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Ingiza Bei"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Ingiza Uwezo wa Bidhaa"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Ingiza Nambari ya Bidhaa"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Ingiza Rangi ya Bidhaa"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Bidhaa"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Ingiza Wingi wa Bidhaa"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Ingiza Ukubwa wa Bidhaa"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Ingiza Aina ya Bidhaa"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Ingiza Kiwango cha Bidhaa"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Ingiza Uzito wa Bidhaa"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Ingiza Bei ya Ununuzi"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Ingiza Nambari ya Marejeleo"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Ingiza Bei ya Kuuza"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Ingiza Nambari ya Siri"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Ingiza Maudhui ya Ujumbe"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Ingiza Kiasi cha Hisani"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Ingiza Kitambulisho cha Hesabu"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Kipimo"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Weka Jina la Wajibu wa Mtumiaji"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Ingiza Cheo cha Mtumiaji"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Ingiza Dhamana"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Ingiza Bei ya Jumla"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Ingiza kiasi chako"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Ingiza Anwani Yako"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Ingiza Anwani ya Kampuni Yako"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Kampuni Yako"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Kampuni Yako"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Ingiza anwani yako ya barua pepe"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Ingiza Nenosiri Lako"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Ingiza tena nenosiri lako"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Ingiza namba yako ya simu"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Duka Lako"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Ingiza Jina la Aina"),
        "expense": MessageLookupByLibrary.simpleMessage("Gharama"),
        "expenseDate":
            MessageLookupByLibrary.simpleMessage("Tarehe ya Matumizi"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Maelezo ya Matumizi"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Matumizi Kwa"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Orodha ya Aina za Matumizi"),
        "expenses": MessageLookupByLibrary.simpleMessage("Matumizi"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Bidhaa Tano Zinazonunuliwa Zaidi Mwezi Huu"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Kwa Matumizi Yasiyokuwa na Kikomo"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Umesahau Nenosiri?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Nakala za Uhifadhi wa Data Bure"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Sasisho za Maisha Bure"),
        "freePackage":
            MessageLookupByLibrary.simpleMessage("Kifurushi cha Bure"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Mpango wa Bure"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Anza"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Kitambulisho cha Serikali"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Jumla Kubwa"),
        "hold": MessageLookupByLibrary.simpleMessage("Kushikilia"),
        "holdNumber":
            MessageLookupByLibrary.simpleMessage("Nambari ya Kushikilia"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Thibitisha Kitambulisho"),
        "inc": MessageLookupByLibrary.simpleMessage("Mapato"),
        "income": MessageLookupByLibrary.simpleMessage("Mapato"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Jamii ya Mapato"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Orodha ya Jamii ya Mapato"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Tarehe ya Mapato"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Maelezo ya Mapato"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Mapato Kwa"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Orodha ya Mapato"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Ongeza Stoo"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Faragha Mara Moja"),
        "invoice": MessageLookupByLibrary.simpleMessage("Hati"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Hati:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Nambari ya Ankara..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Nambari ya Hoja"),
        "item": MessageLookupByLibrary.simpleMessage("Bidhaa"),
        "itemName": MessageLookupByLibrary.simpleMessage("Jina la Bidhaa"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Uhakiki wa KYC"),
        "ledger": MessageLookupByLibrary.simpleMessage("Leja"),
        "left": MessageLookupByLibrary.simpleMessage("Kushoto"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Akaunti za Mikopo"),
        "logOut": MessageLookupByLibrary.simpleMessage("Toka nje"),
        "login": MessageLookupByLibrary.simpleMessage("Ingia"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Nafasi ya Nembo kwenye Bili?"),
        "loss": MessageLookupByLibrary.simpleMessage("Hasara"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Hasara/Faida"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Hasara(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Hisa Chache"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Bidhaa Chache"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Tengeneza athari ya kudumu kwa wateja wako na bili zenye nembo yako. Sasisho lisilokuwa na kikomo linatoa faida ya kipekee ya kubinafsisha bili zako, kuongeza umuhimu wa kitaalam ambao unaimarisha utambulisho wako wa chapa na kukuza uaminifu wa wateja."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Mzalishaji"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Ubao wa Kuingilia Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Ubao wa Usajili wa Pos Saas"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Programu ya Simu\n+\nDawati"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Risiti ya Fedha"),
        "nam": MessageLookupByLibrary.simpleMessage("Jina*"),
        "name": MessageLookupByLibrary.simpleMessage("Jina"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Wateja Wapya"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Wateja Wapya"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Mapato Mpya"),
        "no": MessageLookupByLibrary.simpleMessage("Hapana"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Hakuna Mteja Aliyepatikana"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Miamala Inayodaiwa Iliyopatikana"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Aina ya Matumizi Iliyopatikana"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Jamii ya Mapato Iliyopatikana"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Hakuna Mapato Iliyopatikana"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Hakuna Bili Iliyopatikana"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Hakuna Bidhaa Iliyopatikana"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna shughuli ya ununuzi iliyopatikana"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Makadirio Iliyopatikana"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Hakuna Ripoti Iliyopatikana"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Miamala ya Uuzaji Iliyopatikana"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Nambari ya Siri Iliyopatikana"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Muuzaji Aliyepatikana"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Hakuna Uuzaji Iliyopatikana"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Jukumu la Mtumiaji Lililopatikana"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Hakuna Nambari ya Siri Iliyopatikana"),
        "note": MessageLookupByLibrary.simpleMessage("Kumbukumbu"),
        "ok": MessageLookupByLibrary.simpleMessage("Sawa"),
        "openCheques":
            MessageLookupByLibrary.simpleMessage("Hundi Zilizofunguliwa"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Salio la Kufungua"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage(" au vuta na weka PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Maagizo"),
        "other": MessageLookupByLibrary.simpleMessage("Nyingine"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Mapato Mengine"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Sifa za Kifurushi"),
        "paid": MessageLookupByLibrary.simpleMessage("LimeLipwa"),
        "paidAmount":
            MessageLookupByLibrary.simpleMessage("Kiasi Kilicholipwa"),
        "partyName": MessageLookupByLibrary.simpleMessage("Jina la Chama"),
        "partyType": MessageLookupByLibrary.simpleMessage("Aina ya Chama"),
        "password": MessageLookupByLibrary.simpleMessage("Nenosiri"),
        "payCash": MessageLookupByLibrary.simpleMessage("Lipa Kwa Pesa"),
        "payable": MessageLookupByLibrary.simpleMessage("Inalipika"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Kiasi cha Kulipia"),
        "payment": MessageLookupByLibrary.simpleMessage("Malipo"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Malipo Kuingia"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Malipo Kutoka"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Aina ya Malipo"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Aina ya Malipo"),
        "phone": MessageLookupByLibrary.simpleMessage("Simu"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Nambari ya Simu"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Uthibitisho wa Simu"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Tafadhali Ongeza Uuzaji"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Tafadhali Ongeza Mteja"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Tafadhali pakua programu yetu ya simu na jiandikishe kwenye kifurushi ili utumie toleo la kompyuta"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Tafadhali ingiza stoo ya bidhaa"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Tafadhali ingiza data sahihi"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Tafadhali Chagua Mteja"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Tafadhali ingiza data sahihi"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Msaada wa Wateja wa Kipekee"),
        "premiumPlan":
            MessageLookupByLibrary.simpleMessage("Mpango wa Premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Onyesho Kabla"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Salio la Awali:"),
        "price": MessageLookupByLibrary.simpleMessage("Bei"),
        "print": MessageLookupByLibrary.simpleMessage("Chapisha"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Chapisha Hoja"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Chapisha PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Sera ya Faragha"),
        "product": MessageLookupByLibrary.simpleMessage("Bidhaa"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Jamii ya Bidhaa"),
        "productCod":
            MessageLookupByLibrary.simpleMessage("Nambari ya Bidhaa*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Rangi ya Bidhaa"),
        "productList": MessageLookupByLibrary.simpleMessage("Orodha ya Bidhaa"),
        "productNam": MessageLookupByLibrary.simpleMessage("Jina la Bidhaa*"),
        "productName": MessageLookupByLibrary.simpleMessage("Jina la Bidhaa"),
        "productSize": MessageLookupByLibrary.simpleMessage("Ukubwa wa Bidhaa"),
        "productStock": MessageLookupByLibrary.simpleMessage("Stoo ya Bidhaa"),
        "productType": MessageLookupByLibrary.simpleMessage("Aina ya Bidhaa"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Kipimo cha Bidhaa"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Dhamana ya Bidhaa"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Uzito wa Bidhaa"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Uwezo wa Bidhaa"),
        "prof": MessageLookupByLibrary.simpleMessage("Profaili"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Hariri Wasifu"),
        "profit": MessageLookupByLibrary.simpleMessage("Faida"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Faida(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Faida(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Ununuzi"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Orodha ya Ununuzi"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Nunua Mpango wa Premium"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Bei ya Ununuzi"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Manunuzi ya Miamala"),
        "quantity": MessageLookupByLibrary.simpleMessage("Kiasi"),
        "quotation": MessageLookupByLibrary.simpleMessage("Hati ya Makadirio"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Orodha ya Makadirio"),
        "recentSale":
            MessageLookupByLibrary.simpleMessage("Mauzo ya Hivi Karibuni"),
        "referenceNo":
            MessageLookupByLibrary.simpleMessage("Nambari ya Marejeleo"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Nambari ya Marejeleo"),
        "registration": MessageLookupByLibrary.simpleMessage("Usajili"),
        "remaining": MessageLookupByLibrary.simpleMessage("Inasalia: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Salio Lililosalia"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Salio la Malimbikizo"),
        "reports": MessageLookupByLibrary.simpleMessage("Ripoti"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Weka Upya Nenosiri Lako"),
        "retailer": MessageLookupByLibrary.simpleMessage("Muuza Rejareja"),
        "revenue": MessageLookupByLibrary.simpleMessage("Mapato"),
        "right": MessageLookupByLibrary.simpleMessage("Kulia"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Kiasi cha Mauzo"),
        "sale": MessageLookupByLibrary.simpleMessage("Uuzaji"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Kiasi cha Uuzaji"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Maelezo ya Uuzaji"),
        "saleList": MessageLookupByLibrary.simpleMessage("Orodha ya Uuzaji"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Bei ya Uuzaji"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Bei ya Kuuza*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Kurudi kwa Uuzaji"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Miamala ya Uuzaji"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Miamala ya Uuzaji (Historia ya Matoleo ya Nukuu)"),
        "sales": MessageLookupByLibrary.simpleMessage("Mauzo"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Hifadhi na Chapisha"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Hifadhi na Chapisha"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Hifadhi Mabadiliko"),
        "search": MessageLookupByLibrary.simpleMessage("Tafuta......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Tafuta Kitu Chochoote..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Tafuta kwa ankara...."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Tafuta kwa hati au jina"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Tafuta kwa Jina"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("Tafuta kwa Jina au Simu..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Tafuta Nambari ya Siri"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Chagua Wahusika"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Chagua Chapa ya Bidhaa"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Chagua Nambari ya Siri"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Chagua Tofauti:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Chagua Muda wa Dhamana"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Chagua Lugha Yako"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Tuma Ujumbe"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Nambari ya Siri"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("Nambari ya Siri"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Gharama ya Huduma"),
        "setting": MessageLookupByLibrary.simpleMessage("Mipangilio"),
        "share": MessageLookupByLibrary.simpleMessage("Shiriki"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Usafirishaji/Nyinginezo"),
        "shopName": MessageLookupByLibrary.simpleMessage("Jina la Duka"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Salio la Kufungua Duka"),
        "show": MessageLookupByLibrary.simpleMessage("Onyesha >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Onyesha Nembo kwenye Bili?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Usafirishaji/Huduma"),
        "size": MessageLookupByLibrary.simpleMessage("Ukubwa"),
        "statistic": MessageLookupByLibrary.simpleMessage("Takwimu"),
        "status": MessageLookupByLibrary.simpleMessage("Hali"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Kuwa mstari wa mbele wa maendeleo ya teknolojia bila gharama za ziada. Sasisho la Pos Saas POS lisilokuwa na kikomo linahakikisha kuwa unakuwa na zana na huduma za hivi karibuni mkononi mwako, kuhakikisha biashara yako inabaki kuwa ya kisasa."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Kaa mbele ya maendeleo ya teknolojia bila gharama za ziada. SmartBiashara POS yetu Inayokusanya Bila Kikomo inahakikisha kuwa daima una zana na huduma za hivi karibuni mikononi mwako, ikidhamini kuwa biashara yako inaendelea kuwa ya kisasa."),
        "stock": MessageLookupByLibrary.simpleMessage("Hisani"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Hifadhi ya Bidhaa"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Ripoti ya Hisa"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Thamani ya Bidhaa"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Thamani ya Hisa"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Jumla Ndogo"),
        "submit": MessageLookupByLibrary.simpleMessage("Tuma"),
        "supplier": MessageLookupByLibrary.simpleMessage("Wauzaji"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Muuza Ana Deni"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Bili ya Muuzaji"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Orodha ya Wauzaji"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Msimbo wa SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Jumla ya Mauzo"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Chukua picha ya leseni ya udereva, kitambulisho cha kitaifa au pasipoti"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Sheria za Matumizi"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Jina linasema yote. Na Pos Saas POS lisilokuwa na kikomo, hakuna kikomo kwa matumizi yako. Iwe unaprocessing idadi ndogo ya manunuzi au unakumbana na msururu wa wateja, unaweza kufanya kazi kwa ujasiri, ukiwa na uhakika kuwa haujazuiwa na kikomo."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("Mteja huyu hana deni lolote"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Mteja huyu ana deni la awali"),
        "to": MessageLookupByLibrary.simpleMessage("Hadi"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Bidhaa Inayouzwa Zaidi"),
        "total": MessageLookupByLibrary.simpleMessage("Jumla"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Jumla Ya Kiasi"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Jumla ya Punguzo"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Jumla Inayodaiwa"),
        "totalDues":
            MessageLookupByLibrary.simpleMessage("Jumla ya Malimbikizo"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("Jumla ya Matumizi"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Jumla ya Mapato"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Jumla ya Bidhaa: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Jumla ya Hasara"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Jumla Imelipwa"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Jumla Inayopaswa Kulipwa"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Jumla ya Malipo Kutoka"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Jumla ya Bei"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Jumla ya Bidhaa"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Jumla ya Faida"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Jumla ya Ununuzi"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Jumla ya Kiasi Kurudi"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Jumla ya Kurudi"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Jumla ya Uuzaji"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Jumla ya Uuzaji"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Jumla ya VAT"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Jumla ya Malipo Kuingia"),
        "transaction": MessageLookupByLibrary.simpleMessage("Hati"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Kitambulisho cha Miamala"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Ripoti ya Miamala"),
        "type": MessageLookupByLibrary.simpleMessage("Aina"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Haijalipwa"),
        "unit": MessageLookupByLibrary.simpleMessage("Kiwango"),
        "unitName": MessageLookupByLibrary.simpleMessage("Jina la Kipimo"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Bei ya Kipande"),
        "unlimited":
            MessageLookupByLibrary.simpleMessage("Isiyokuwa na kikomo"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Bilansi Isiyokuwa na Kikomo"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage(
            "Matumizi Yasiyokuwa na Kikomo"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Fungua uwezo kamili wa Pos Saas POS na mafunzo ya kibinafsi yanayoongozwa na timu yetu ya wataalamu. Kutoka kwa misingi hadi mbinu za juu, tunahakikisha una maarifa ya kutosha ya kutumia kila sehemu ya mfumo ili kuboresha shughuli za biashara yako."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Sasisha Sasa"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Sasisha mpango wako kwanza. Kikomo cha Uuzaji kimeisha."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Sasisha Kupitia Programu ya Simu"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Pakia picha"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Pakia Nembo ya Bili"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("Pakia Hati"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Pakia Faili"),
        "userRole": MessageLookupByLibrary.simpleMessage("Wajibu wa Mtumiaji"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Jina la Wajibu wa Mtumiaji"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Cheo cha Mtumiaji"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("VAT/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Thibitisha Nambari ya Simu"),
        "view": MessageLookupByLibrary.simpleMessage("Angalia"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Mteja Anayeingia"),
        "warranty": MessageLookupByLibrary.simpleMessage("Dhamana"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Dhamana"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Tunahitaji kusajili simu yako kabla ya kuanza!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Tunaelewa umuhimu wa shughuli bila kukwama. Ndio maana msaada wetu wa saa 24 uko tayari kutoa usaidizi, iwe ni swali dogo au suala kubwa. Wasiliana nasi wakati wowote, mahali popote kupitia simu au WhatsApp ili ujue huduma isiyo na kifani."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Bei ya Jumla"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Muuzaji Jumla"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Jumla"),
        "wight": MessageLookupByLibrary.simpleMessage("Uzito"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ndio, Rudisha"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Lazima Uingie tena kwenye akaunti yako."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Unahitaji kuthibitisha kitambulisho kabla ya kununua ujumbe"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Orodha Yako Yote ya Mauzo"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Mauzo Yako Yote"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Unatumia"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Mauzo Yako Yanayodaiwa"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Unahitaji kuthibitisha kitambulisho kabla ya kununua ujumbe"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Kifurushi Chako"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Malipo yako yamefutwa"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Malipo yako yamefanikiwa"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Malipo yako yamefutwa")
      };
}
