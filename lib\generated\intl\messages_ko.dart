// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ko locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ko';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("매출 추가"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("카테고리"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("송장"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS 판매"),
        "PRICE": MessageLookupByLibrary.simpleMessage("가격"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("제품 이름"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas 로그인 패널"),
        "QTY": MessageLookupByLibrary.simpleMessage("수량"),
        "Quantity": MessageLookupByLibrary.simpleMessage("수량*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("상태"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("총 가치"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("사용자 제목"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("앱 정보"),
        "accountName": MessageLookupByLibrary.simpleMessage("계좌 이름"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("계좌 번호"),
        "action": MessageLookupByLibrary.simpleMessage("작업"),
        "add": MessageLookupByLibrary.simpleMessage("추가"),
        "addBrand": MessageLookupByLibrary.simpleMessage("브랜드 추가"),
        "addCategory": MessageLookupByLibrary.simpleMessage("카테고리 추가"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("고객 추가"),
        "addDescription": MessageLookupByLibrary.simpleMessage("설명 추가..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("문서 추가"),
        "addItem": MessageLookupByLibrary.simpleMessage("항목 추가"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage("항목 카테고리 추가"),
        "addNew": MessageLookupByLibrary.simpleMessage("새로 추가"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("새 사용자 추가"),
        "addProduct": MessageLookupByLibrary.simpleMessage("상품 추가"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("추가 성공"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("공급업체 추가"),
        "addUnit": MessageLookupByLibrary.simpleMessage("단위 추가"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("지출 목록 추가/수정"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("수입 목록 추가/수정"),
        "addUserRole": MessageLookupByLibrary.simpleMessage("사용자 역할 추가"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage("일련 번호 추가?"),
        "address": MessageLookupByLibrary.simpleMessage("주소"),
        "all": MessageLookupByLibrary.simpleMessage("전체"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage("모든 기본 기능"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("이미 계정이 있습니까?"),
        "amount": MessageLookupByLibrary.simpleMessage("금액"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("안드로이드 및 iOS 앱 지원"),
        "areYouWantToCreateThisQuation":
            MessageLookupByLibrary.simpleMessage("이 견적서를 생성하시겠습니까?"),
        "areYouWantToDeleteThisCustomer":
            MessageLookupByLibrary.simpleMessage("이 고객을 삭제하시겠습니까?"),
        "areYouWantToDeleteThisProduct":
            MessageLookupByLibrary.simpleMessage("이 제품을 삭제하시겠습니까"),
        "areYouWantToDeleteThisQuotion":
            MessageLookupByLibrary.simpleMessage("이 견적을 삭제하시겠습니까?"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("이 매출을 반품하시겠습니까?"),
        "balance": MessageLookupByLibrary.simpleMessage("잔액"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("은행 계좌"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("은행 정보"),
        "bankName": MessageLookupByLibrary.simpleMessage("은행 이름"),
        "between": MessageLookupByLibrary.simpleMessage("사이"),
        "billTo": MessageLookupByLibrary.simpleMessage("청구서:"),
        "branchName": MessageLookupByLibrary.simpleMessage("지점 이름"),
        "brand": MessageLookupByLibrary.simpleMessage("브랜드"),
        "brandName": MessageLookupByLibrary.simpleMessage("브랜드 이름"),
        "businessCategory": MessageLookupByLibrary.simpleMessage("사업 카테고리"),
        "buy": MessageLookupByLibrary.simpleMessage("구매"),
        "buyPremiumPlan": MessageLookupByLibrary.simpleMessage("프리미엄 플랜 구매"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMS 구매"),
        "calculator": MessageLookupByLibrary.simpleMessage("계산기:"),
        "camera": MessageLookupByLibrary.simpleMessage("카메라"),
        "cancel": MessageLookupByLibrary.simpleMessage("취소"),
        "capacity": MessageLookupByLibrary.simpleMessage("용량"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("현금 및 은행"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("현금 보유"),
        "categories": MessageLookupByLibrary.simpleMessage("카테고리"),
        "category": MessageLookupByLibrary.simpleMessage("카테고리"),
        "categoryName": MessageLookupByLibrary.simpleMessage("카테고리 이름"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("잔액"),
        "changeableAmount": MessageLookupByLibrary.simpleMessage("변경 가능한 금액"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("보증 확인"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("요금제 선택"),
        "collectDue": MessageLookupByLibrary.simpleMessage("미수금 수금 >"),
        "color": MessageLookupByLibrary.simpleMessage("색상"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("회사 이름"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("회사 주소"),
        "companyDescription": MessageLookupByLibrary.simpleMessage("회사 설명"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("회사 이메일 주소"),
        "companyName": MessageLookupByLibrary.simpleMessage("회사 이름"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage("회사 전화번호"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("회사 웹사이트 URL"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("비밀번호 확인"),
        "continu": MessageLookupByLibrary.simpleMessage("계속"),
        "convertToSale": MessageLookupByLibrary.simpleMessage("판매로 전환"),
        "create": MessageLookupByLibrary.simpleMessage("만들기"),
        "createPayment": MessageLookupByLibrary.simpleMessage("결제 생성"),
        "createdBy": MessageLookupByLibrary.simpleMessage("작성자"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("크리에이티브 허브"),
        "currency": MessageLookupByLibrary.simpleMessage("통화"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("현재 요금제"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("맞춤형 송장 브랜딩"),
        "customer": MessageLookupByLibrary.simpleMessage("고객"),
        "customerDue": MessageLookupByLibrary.simpleMessage("고객 미결제금액"),
        "customerInvoices": MessageLookupByLibrary.simpleMessage("고객 송장"),
        "customerList": MessageLookupByLibrary.simpleMessage("고객 목록"),
        "customerName": MessageLookupByLibrary.simpleMessage("고객 이름"),
        "customerOfTheMonth": MessageLookupByLibrary.simpleMessage("이달의 고객"),
        "customerType": MessageLookupByLibrary.simpleMessage("고객 유형"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("고객: 방문 고객"),
        "customers": MessageLookupByLibrary.simpleMessage("고객"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("일일 수금"),
        "dailySales": MessageLookupByLibrary.simpleMessage("일일 매출"),
        "dailyTransaction": MessageLookupByLibrary.simpleMessage("일일 거래"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("대시보드"),
        "date": MessageLookupByLibrary.simpleMessage("날짜"),
        "dateTime": MessageLookupByLibrary.simpleMessage("날짜 시간"),
        "dealer": MessageLookupByLibrary.simpleMessage("딜러"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("딜러 가격"),
        "delete": MessageLookupByLibrary.simpleMessage("삭제"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("배송비"),
        "description": MessageLookupByLibrary.simpleMessage("설명"),
        "details": MessageLookupByLibrary.simpleMessage(">세부 정보"),
        "discount": MessageLookupByLibrary.simpleMessage("할인"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("할인 가격"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF 다운로드"),
        "due": MessageLookupByLibrary.simpleMessage("잔액"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("미수금"),
        "dueAmountWillShowHere":
            MessageLookupByLibrary.simpleMessage("잔액이 있으면 여기에 표시됩니다."),
        "dueCollection": MessageLookupByLibrary.simpleMessage("미결제금 수금"),
        "dueList": MessageLookupByLibrary.simpleMessage("미결제 목록"),
        "dueTransaction": MessageLookupByLibrary.simpleMessage("미결 거래"),
        "edit": MessageLookupByLibrary.simpleMessage("편집"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage("일련 번호 편집/추가:"),
        "editYourProfile": MessageLookupByLibrary.simpleMessage("프로필 편집"),
        "email": MessageLookupByLibrary.simpleMessage("이메일"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("금액 입력"),
        "enterBrandName": MessageLookupByLibrary.simpleMessage("브랜드 이름 입력"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage("카테고리 이름 입력"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("회사 설명을 입력하세요"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("회사 이메일 주소를 입력하세요"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("회사 전화번호를 입력하세요"),
        "enterCompanyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("회사 웹사이트 URL을 입력하세요"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("고객 이름을 입력하세요"),
        "enterDealePrice": MessageLookupByLibrary.simpleMessage("딜러 가격 입력"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("할인 가격을 입력하세요"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("지출 카테고리 입력"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("지출 날짜를 입력하세요"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("수입 카테고리 입력"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage("수입 날짜 입력"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("제조업체 이름 입력"),
        "enterName": MessageLookupByLibrary.simpleMessage("이름을 입력하세요"),
        "enterNames": MessageLookupByLibrary.simpleMessage("이름 입력"),
        "enterNote": MessageLookupByLibrary.simpleMessage("메모를 입력하세요"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("개시 잔고를 입력하세요"),
        "enterPaidAmount": MessageLookupByLibrary.simpleMessage("지불 금액을 입력하세요"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("비밀번호 입력"),
        "enterPayingAmount": MessageLookupByLibrary.simpleMessage("지불 금액 입력"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("가격 입력"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("제품 용량 입력"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage("상품 코드 입력"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage("제품 색상 입력"),
        "enterProductName": MessageLookupByLibrary.simpleMessage("상품명 입력"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("상품 수량 입력"),
        "enterProductSize": MessageLookupByLibrary.simpleMessage("제품 크기 입력"),
        "enterProductType": MessageLookupByLibrary.simpleMessage("상품 유형 입력"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage("제품 단위 입력"),
        "enterProductWeight": MessageLookupByLibrary.simpleMessage("제품 무게 입력"),
        "enterPurchasePrice": MessageLookupByLibrary.simpleMessage("구매 가격 입력"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("참조 번호를 입력하세요"),
        "enterSalePrice": MessageLookupByLibrary.simpleMessage("판매 가격 입력"),
        "enterSerialNumber": MessageLookupByLibrary.simpleMessage("일련 번호 입력"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("메시지 내용을 입력하세요"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("재고 수량을 입력하세요"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage("거래 ID 입력"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage("단위 이름 입력"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("사용자 역할 이름 입력"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage("사용자 제목 입력"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("보증 입력"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage("도매 가격 입력"),
        "enterYOurAmount": MessageLookupByLibrary.simpleMessage("금액을 입력하세요"),
        "enterYourAddress": MessageLookupByLibrary.simpleMessage("주소를 입력하세요"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("회사 주소를 입력하세요"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("회사 이름을 입력하세요"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("회사 이름을 입력하세요"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("이메일 주소를 입력하세요"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("비밀번호를 입력하세요"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("비밀번호를 다시 입력하세요"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("전화 번호를 입력하세요"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("상점 이름을 입력하세요"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("카테고리 이름을 입력하세요"),
        "expense": MessageLookupByLibrary.simpleMessage("비용"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("지출 날짜"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("지출 세부 정보"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("지출 목적"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("지출 카테고리 목록"),
        "expenses": MessageLookupByLibrary.simpleMessage("비용"),
        "fivePurchase":
            MessageLookupByLibrary.simpleMessage("이번 달 최다 판매 상품 5개"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage("무제한 사용을 위해"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("비밀번호를 잊으셨나요?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage("무료 데이터 백업"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("무료 평생 업데이트"),
        "freePackage": MessageLookupByLibrary.simpleMessage("무료 패키지"),
        "freePlan": MessageLookupByLibrary.simpleMessage("무료 플랜"),
        "getStarted": MessageLookupByLibrary.simpleMessage("시작하기"),
        "govermentId": MessageLookupByLibrary.simpleMessage("정부 ID"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("총합계"),
        "hold": MessageLookupByLibrary.simpleMessage("보류"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("보류 번호"),
        "identityVerify": MessageLookupByLibrary.simpleMessage("신원 인증"),
        "inc": MessageLookupByLibrary.simpleMessage("수입"),
        "income": MessageLookupByLibrary.simpleMessage("수입"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("수입 카테고리"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("수입 카테고리 목록"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("수입 날짜"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("수입 세부 정보"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("수입 대상"),
        "incomeList": MessageLookupByLibrary.simpleMessage("수입 목록"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("재고 증가"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage("즉시 비공개"),
        "invoice": MessageLookupByLibrary.simpleMessage("송장"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("송장:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("송장 번호..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("송장 번호"),
        "item": MessageLookupByLibrary.simpleMessage("항목"),
        "itemName": MessageLookupByLibrary.simpleMessage("품목명"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC 인증"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("원장 세부 정보"),
        "ledger": MessageLookupByLibrary.simpleMessage("원장"),
        "left": MessageLookupByLibrary.simpleMessage("왼쪽"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("대출 계좌"),
        "logOut": MessageLookupByLibrary.simpleMessage("로그 아웃"),
        "login": MessageLookupByLibrary.simpleMessage("로그인"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("송장 로고 위치"),
        "loss": MessageLookupByLibrary.simpleMessage("손실"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("손익"),
        "lossminus": MessageLookupByLibrary.simpleMessage("손실(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("낮은 재고"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("낮은 재고"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "고객에게 브랜드 송장으로 오랜 인상을 남기세요. 우리의 무제한 업그레이드는 송장을 맞춤 설정하여 브랜드 정체성을 강화하고 고객 충성을 유발하는 전문적인 터치를 추가하는 독특한 이점을 제공합니다."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("제조업체"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas 로그인 패널"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas 가입 패널"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("모바일 앱\n+\n데스크탑"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("수표 영수증"),
        "nam": MessageLookupByLibrary.simpleMessage("이름*"),
        "name": MessageLookupByLibrary.simpleMessage("이름"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("이름, 코드, 또는 카테고리"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("신규 고객"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("신규 고객"),
        "newIncome": MessageLookupByLibrary.simpleMessage("새 수입"),
        "no": MessageLookupByLibrary.simpleMessage("아니요"),
        "noConnection": MessageLookupByLibrary.simpleMessage("연결 없음"),
        "noCustomerFound": MessageLookupByLibrary.simpleMessage("고객이 없습니다"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("미수 거래가 없습니다."),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("지출 카테고리가 없습니다."),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("수입 카테고리 없음"),
        "noIncomeFound": MessageLookupByLibrary.simpleMessage("수입 없음"),
        "noInvoiceFound": MessageLookupByLibrary.simpleMessage("송장이 없습니다"),
        "noProductFound": MessageLookupByLibrary.simpleMessage("제품이 없습니다."),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("구매 거래가 없습니다."),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage("견적이 없습니다."),
        "noReportFound": MessageLookupByLibrary.simpleMessage("보고서가 없습니다."),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("매출 거래가 없습니다"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("일련 번호가 없습니다."),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage("공급업체가 없습니다"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage("거래 없음"),
        "noUserFound": MessageLookupByLibrary.simpleMessage("사용자를 찾을 수 없음"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("사용자 역할을 찾을 수 없음"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("일련 번호가 없습니다."),
        "note": MessageLookupByLibrary.simpleMessage("메모"),
        "ok": MessageLookupByLibrary.simpleMessage("확인"),
        "openCheques": MessageLookupByLibrary.simpleMessage("오픈 수표"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("개시 잔고"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("또는 PNG, JPG를 드래그 앤 드롭하세요"),
        "orders": MessageLookupByLibrary.simpleMessage("주문"),
        "other": MessageLookupByLibrary.simpleMessage("기타"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("기타 수입"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("패키지 기능"),
        "paid": MessageLookupByLibrary.simpleMessage("지불됨"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("지불 금액"),
        "partyName": MessageLookupByLibrary.simpleMessage("거래처 이름"),
        "partyType": MessageLookupByLibrary.simpleMessage("거래처 유형"),
        "password": MessageLookupByLibrary.simpleMessage("비밀번호"),
        "payCash": MessageLookupByLibrary.simpleMessage("현금 결제"),
        "payable": MessageLookupByLibrary.simpleMessage("지불 가능"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("지불 금액"),
        "payment": MessageLookupByLibrary.simpleMessage("지불"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("수입"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("지출"),
        "paymentType": MessageLookupByLibrary.simpleMessage("결제 유형"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("결제 유형"),
        "phone": MessageLookupByLibrary.simpleMessage("전화번호"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("전화 번호"),
        "phoneVerification": MessageLookupByLibrary.simpleMessage("전화 인증"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage("판매를 추가하세요."),
        "pleaseAddCustomer": MessageLookupByLibrary.simpleMessage("고객을 추가하세요"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage("인터넷 연결을 확인하세요"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "모바일 앱을 다운로드하고 데스크톱 버전을 사용하려면 패키지를 구독하세요"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("제품 재고를 입력하세요"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("유효한 데이터를 입력하세요"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("고객을 선택하세요."),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("유효한 데이터를 입력하세요"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas 가입 패널"),
        "practies": MessageLookupByLibrary.simpleMessage("연습"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("프리미엄 고객 지원"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("프리미엄 플랜"),
        "preview": MessageLookupByLibrary.simpleMessage("미리보기"),
        "previousDue": MessageLookupByLibrary.simpleMessage("이전 due:"),
        "price": MessageLookupByLibrary.simpleMessage("가격"),
        "print": MessageLookupByLibrary.simpleMessage("인쇄"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("송장 인쇄"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF 인쇄"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("개인 정보 정책"),
        "product": MessageLookupByLibrary.simpleMessage("상품"),
        "productCategory": MessageLookupByLibrary.simpleMessage("제품 카테고리"),
        "productCod": MessageLookupByLibrary.simpleMessage("상품 코드*"),
        "productColor": MessageLookupByLibrary.simpleMessage("제품 색상"),
        "productList": MessageLookupByLibrary.simpleMessage("상품 목록"),
        "productNam": MessageLookupByLibrary.simpleMessage("상품명*"),
        "productName": MessageLookupByLibrary.simpleMessage("제품 이름"),
        "productSize": MessageLookupByLibrary.simpleMessage("제품 크기"),
        "productStock": MessageLookupByLibrary.simpleMessage("제품 재고"),
        "productType": MessageLookupByLibrary.simpleMessage("상품 유형"),
        "productUnit": MessageLookupByLibrary.simpleMessage("상품 단위"),
        "productWaranty": MessageLookupByLibrary.simpleMessage("상품 보증"),
        "productWeight": MessageLookupByLibrary.simpleMessage("제품 무게"),
        "productcapacity": MessageLookupByLibrary.simpleMessage("제품 용량"),
        "prof": MessageLookupByLibrary.simpleMessage("프로필"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("프로필 편집"),
        "profit": MessageLookupByLibrary.simpleMessage("이익"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("이익(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("이익(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("구매"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("구매 목록"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("프리미엄 플랜 구매"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("구매 가격"),
        "purchaseTransaction": MessageLookupByLibrary.simpleMessage("구매 거래"),
        "quantity": MessageLookupByLibrary.simpleMessage("수량"),
        "quotation": MessageLookupByLibrary.simpleMessage("견적서"),
        "quotationList": MessageLookupByLibrary.simpleMessage("견적 목록"),
        "recentSale": MessageLookupByLibrary.simpleMessage("최근 판매"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("받은 금액"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("참조 번호"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("참조 번호"),
        "registration": MessageLookupByLibrary.simpleMessage("등록"),
        "remaining": MessageLookupByLibrary.simpleMessage("남은 것: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("잔여 잔액"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("잔여 미결제금액"),
        "reports": MessageLookupByLibrary.simpleMessage("보고서"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage("비밀번호 재설정"),
        "retailer": MessageLookupByLibrary.simpleMessage("소매점"),
        "revenue": MessageLookupByLibrary.simpleMessage("수익"),
        "right": MessageLookupByLibrary.simpleMessage("오른쪽"),
        "sAmount": MessageLookupByLibrary.simpleMessage("판매 금액"),
        "sale": MessageLookupByLibrary.simpleMessage("판매"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("판매 금액"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("판매 세부 정보"),
        "saleList": MessageLookupByLibrary.simpleMessage("매출 목록"),
        "salePrice": MessageLookupByLibrary.simpleMessage("판매 가격"),
        "salePrices": MessageLookupByLibrary.simpleMessage("판매 가격*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("매출 반품"),
        "saleTransaction": MessageLookupByLibrary.simpleMessage("매출 거래"),
        "saleTransactionQuatationHistory":
            MessageLookupByLibrary.simpleMessage("매출 거래 (견적 판매 내역)"),
        "sales": MessageLookupByLibrary.simpleMessage("매출"),
        "salesList": MessageLookupByLibrary.simpleMessage("판매 목록"),
        "saveAndPublish": MessageLookupByLibrary.simpleMessage("저장 및 게시"),
        "saveAndPublished": MessageLookupByLibrary.simpleMessage("저장 및 게시"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("변경 사항 저장"),
        "search": MessageLookupByLibrary.simpleMessage("검색......."),
        "searchAnyThing": MessageLookupByLibrary.simpleMessage("무엇이든 검색..."),
        "searchByInvoice": MessageLookupByLibrary.simpleMessage("송장으로 검색..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("송장 또는 이름으로 검색"),
        "searchByName": MessageLookupByLibrary.simpleMessage("이름으로 검색"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("이름 또는 전화로 검색..."),
        "searchSerialNumber": MessageLookupByLibrary.simpleMessage("일련 번호 검색"),
        "selectParties": MessageLookupByLibrary.simpleMessage("당사자 선택"),
        "selectProductBrand": MessageLookupByLibrary.simpleMessage("상품 브랜드 선택"),
        "selectSerialNumber": MessageLookupByLibrary.simpleMessage("일련 번호 선택"),
        "selectVariations": MessageLookupByLibrary.simpleMessage("변경 사항 선택:"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage("보증 기간 선택"),
        "selectYourLanguage": MessageLookupByLibrary.simpleMessage("언어 선택"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("메시지 보내기"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("일련 번호"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("일련 번호"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("서비스 요금"),
        "setting": MessageLookupByLibrary.simpleMessage("설정"),
        "share": MessageLookupByLibrary.simpleMessage("공유"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("배송/기타"),
        "shopName": MessageLookupByLibrary.simpleMessage("상점 이름"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage("매장 개장 잔고"),
        "show": MessageLookupByLibrary.simpleMessage(">보여주기"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage("송장에 로고 표시?"),
        "shpingOrServices": MessageLookupByLibrary.simpleMessage("배송/서비스"),
        "size": MessageLookupByLibrary.simpleMessage("크기"),
        "statistic": MessageLookupByLibrary.simpleMessage("통계"),
        "status": MessageLookupByLibrary.simpleMessage("상태"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "추가 비용 없이 최첨단 기술 개발 최전선에 머물러 있습니다. 우리의 Pos Saas POS 무제한 업그레이드는 항상 최신 도구와 기능을 손끝에 갖고 있도록 보장하여 비즈니스가 최첨단 유지됩니다."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "추가 비용 없이 기술 발전의 최전선에 머물러 있습니다. 우리의 Pos Sass POS 무제한 업그레이드는 항상 최신 도구와 기능을 손끝에 갖고 있도록 보장하여 비즈니스가 최전선에 남는다."),
        "stock": MessageLookupByLibrary.simpleMessage("재고"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("재고 목록"),
        "stockReport": MessageLookupByLibrary.simpleMessage("재고 보고서"),
        "stockValue": MessageLookupByLibrary.simpleMessage("재고 가치"),
        "stockValues": MessageLookupByLibrary.simpleMessage("재고 가치"),
        "subTotal": MessageLookupByLibrary.simpleMessage("소계"),
        "subciption": MessageLookupByLibrary.simpleMessage("구독"),
        "submit": MessageLookupByLibrary.simpleMessage("제출"),
        "supplier": MessageLookupByLibrary.simpleMessage("공급업체"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("공급업체 미결제금액"),
        "supplierInvoice": MessageLookupByLibrary.simpleMessage("공급업체 송장"),
        "supplierList": MessageLookupByLibrary.simpleMessage("공급업체 목록"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT 코드"),
        "tSale": MessageLookupByLibrary.simpleMessage("총 판매"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "운전 면허증, 주민등록증 또는 여권 사진을 찍으세요"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("사용 조건"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "이름이 모든 것을 말합니다. Pos Saas POS 무제한으로 사용량 제한이 없습니다. 소수 거래를 처리하든 고객 폭주를 경험하든 한계에 제약받지 않고 자신감을 가지고 작동할 수 있습니다."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("이 고객은 미결제금이 없습니다."),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("이 고객은 이전에 미결제금액이 있습니다"),
        "to": MessageLookupByLibrary.simpleMessage("에"),
        "topSellingProduct": MessageLookupByLibrary.simpleMessage("최고 판매 제품"),
        "total": MessageLookupByLibrary.simpleMessage("합계"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("총 금액"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("총 할인"),
        "totalDue": MessageLookupByLibrary.simpleMessage("총 미수금"),
        "totalDues": MessageLookupByLibrary.simpleMessage("총 미결제금액"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("총 지출"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("총 수입"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("총 품목: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("총 손실"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("총 지불"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("총 지불 금액"),
        "totalPaymentOut": MessageLookupByLibrary.simpleMessage("총 지출"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("총 금액"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("총 상품"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("총 이익"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("총 구매"),
        "totalReturnAmount": MessageLookupByLibrary.simpleMessage("총 반품 금액"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("총 반품"),
        "totalSale": MessageLookupByLibrary.simpleMessage("총 매출"),
        "totalSales": MessageLookupByLibrary.simpleMessage("총 매출"),
        "totalVat": MessageLookupByLibrary.simpleMessage("총 부가가치세"),
        "totalpaymentIn": MessageLookupByLibrary.simpleMessage("총 수입"),
        "transaction": MessageLookupByLibrary.simpleMessage("거래"),
        "transactionId": MessageLookupByLibrary.simpleMessage("거래 ID"),
        "transactionReport": MessageLookupByLibrary.simpleMessage("거래 보고서"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("다시 시도"),
        "type": MessageLookupByLibrary.simpleMessage("유형"),
        "unPaid": MessageLookupByLibrary.simpleMessage("미지불"),
        "unit": MessageLookupByLibrary.simpleMessage("단위"),
        "unitName": MessageLookupByLibrary.simpleMessage("단위 이름"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("단가"),
        "unlimited": MessageLookupByLibrary.simpleMessage("무제한"),
        "unlimitedInvoice": MessageLookupByLibrary.simpleMessage("무제한 송장"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage("무제한 사용"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "우리의 전문 팀이 이끄는 맞춤 교육 세션으로 Pos Saas POS의 최대 잠재력을 끄집어내보세요. 기초부터 고급 기술까지 모든 측면을 활용하여 비즈니스 프로세스를 최적화하도록 보장합니다."),
        "updateNow": MessageLookupByLibrary.simpleMessage("지금 업데이트"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "먼저 요금제를 업데이트하세요.\n판매 한도가 초과되었습니다."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("모바일 앱에서 업그레이드"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("이미지 업로드"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("송장 로고 업로드"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("문서 업로드"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("파일 업로드"),
        "userName": MessageLookupByLibrary.simpleMessage("사용자 이름"),
        "userRole": MessageLookupByLibrary.simpleMessage("사용자 역할"),
        "userRoleName": MessageLookupByLibrary.simpleMessage("사용자 역할 이름"),
        "userTitle": MessageLookupByLibrary.simpleMessage("사용자 제목"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("부가가치세/GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage("전화번호 인증"),
        "view": MessageLookupByLibrary.simpleMessage("보기"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("방문 고객"),
        "warranty": MessageLookupByLibrary.simpleMessage("보증"),
        "warrantys": MessageLookupByLibrary.simpleMessage("보증"),
        "weNeedToRegisterYourPhone":
            MessageLookupByLibrary.simpleMessage("시작하기 전에 휴대폰을 등록해야 합니다!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "우리는 원활한 운영의 중요성을 이해합니다. 그래서 연중무휴 지원팀이 빠른 문의든 포괄적인 문제든 도와 드릴 준비가 되어 있습니다. 언제 어디서나 전화 또는 WhatsApp을 통해 우리와 연락하여 탁월한 고객 서비스를 경험하세요."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("도매 가격"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("도매업자"),
        "wholesale": MessageLookupByLibrary.simpleMessage("도매"),
        "wight": MessageLookupByLibrary.simpleMessage("무게"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("예, 반품"),
        "youHaveToRelogin":
            MessageLookupByLibrary.simpleMessage("계정을 다시 로그인해야 합니다."),
        "youNeedToIdentityVerifySms":
            MessageLookupByLibrary.simpleMessage("메시지를 구매하기 전에 신원 인증이 필요합니다"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage("전체 판매 목록"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("전체 매출"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("당신은 사용 중입니다"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("미결제 매출"),
        "yourNeedToIdentityVerify":
            MessageLookupByLibrary.simpleMessage("메시지를 구매하기 전에 신원 인증이 필요합니다"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("귀하의 패키지"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("결제가 취소되었습니다."),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("결제가 성공적으로 완료되었습니다."),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("결제가 취소되었습니다.")
      };
}
