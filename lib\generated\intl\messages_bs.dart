// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a bs locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'bs';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("DODAJ PRODAJU"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIJA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("RAČUN"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS prodaja"),
        "PRICE": MessageLookupByLibrary.simpleMessage("CIJENA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NAZIV PROIZVODA"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas panel za prijavu"),
        "QTY": MessageLookupByLibrary.simpleMessage("KOL"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("UKUPNA VRIJEDNOST"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Naslov korisnika"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("O aplikaciji"),
        "accountName": MessageLookupByLibrary.simpleMessage("Ime računa"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Broj računa"),
        "action": MessageLookupByLibrary.simpleMessage("Akcija"),
        "add": MessageLookupByLibrary.simpleMessage("Dodaj"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Dodaj brend"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Dodaj kategoriju"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Dodaj kupca"),
        "addDescription": MessageLookupByLibrary.simpleMessage("Dodaj opis..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Dodajte Dokumente"),
        "addItem": MessageLookupByLibrary.simpleMessage("Dodaj Stavku"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Dodaj kategoriju proizvoda"),
        "addNew": MessageLookupByLibrary.simpleMessage("Dodaj novi"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Dodaj novog korisnika"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Dodaj proizvod"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Dodano uspješno"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Dodaj Dobavljača"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Dodaj jedinicu"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/Ažuriraj listu troškova"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/Ažuriraj listu prihoda"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Dodaj ulogu korisnika"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Dodavanje serijskog broja?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresa"),
        "all": MessageLookupByLibrary.simpleMessage("Svi"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Sve osnovne karakteristike"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Već imate nalog?"),
        "amount": MessageLookupByLibrary.simpleMessage("Iznos"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Podrška za Android i iOS aplikaciju"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Da li želite kreirati ovu ponudu?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Želite li izbrisati ovog kupca?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Želite li obrisati ovaj proizvod"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Želite li izbrisati ovu ponudu?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Želite li vratiti ovu prodaju?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Valuta bankovnog računa"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankovni računi"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankovni podaci"),
        "bankName": MessageLookupByLibrary.simpleMessage("Ime banke"),
        "between": MessageLookupByLibrary.simpleMessage("Između"),
        "billTo": MessageLookupByLibrary.simpleMessage("Račun za:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Ime podružnice"),
        "brand": MessageLookupByLibrary.simpleMessage("Brend"),
        "brandName": MessageLookupByLibrary.simpleMessage("Naziv brenda"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija poslovanja"),
        "buy": MessageLookupByLibrary.simpleMessage("Kupi"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kupi Premium plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Kupi SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Otkaži"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacitet"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Gotovina i banka"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Gotovina u ruci"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorije"),
        "category": MessageLookupByLibrary.simpleMessage("Kategorija"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Naziv kategorije"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Iznos za povrat"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Promjenjivi iznos"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Provjeri Garanciju"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Izaberite plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Naplati dugovanje >"),
        "color": MessageLookupByLibrary.simpleMessage("Boja"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Naziv kompanije"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Adresa Tvrtke"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Opis Tvrtke"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Email Adresa Tvrtke"),
        "companyName": MessageLookupByLibrary.simpleMessage("Naziv kompanije"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefonski Broj Tvrtke"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL Web Stranice Tvrtke"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Potvrdi šifru"),
        "continu": MessageLookupByLibrary.simpleMessage("Nastavi"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konvertuj u prodaju"),
        "create": MessageLookupByLibrary.simpleMessage("Kreiraj"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Kreiraj uplatu"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Kreirao"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreativni centar"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Trenutni plan"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Prilagođeno brendiranje računa"),
        "customer": MessageLookupByLibrary.simpleMessage("Kupac"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Dugovanje kupca"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Fakture Klijenata"),
        "customerList": MessageLookupByLibrary.simpleMessage("Lista kupaca"),
        "customerName": MessageLookupByLibrary.simpleMessage("Ime kupca"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kupac mjeseca"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tip Klijenta"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "Kupac: Klijent na licu mjesta"),
        "customers": MessageLookupByLibrary.simpleMessage("Kupci"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Dnevna naplata"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Dnevna prodaja"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Dnevna transakcija"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Kontrolna tabla"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum i Vrijeme"),
        "dealer": MessageLookupByLibrary.simpleMessage("Trgovac"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Cijena za trgovca"),
        "delete": MessageLookupByLibrary.simpleMessage("Obriši"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Trošak dostave"),
        "description": MessageLookupByLibrary.simpleMessage("Opis"),
        "details": MessageLookupByLibrary.simpleMessage("Detalji >"),
        "discount": MessageLookupByLibrary.simpleMessage("Popust"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Cijena sa Popustom"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Preuzmi PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Dug"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Iznos duga"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Dospjeli iznos će biti prikazan ovdje ako je dostupan"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Prikupljeni dospjeli iznosi"),
        "dueList":
            MessageLookupByLibrary.simpleMessage("Lista dospjelih iznosa"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcija duga"),
        "edit": MessageLookupByLibrary.simpleMessage("Uredi"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Uredi/Dodaj serijski broj:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Uredite svoj profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Unesite iznos"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv brenda"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv kategorije"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Unesite Opis Tvrtke"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Unesite Email Adresu Tvrtke"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Unesite Telefonski Broj Tvrtke"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Unesite URL Web Stranice Tvrtke"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Unesite ime kupca"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Unesite cijenu za trgovca"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Unesite Cijenu sa Popustom"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Unesite kategoriju troška"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Unesite datum troška"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Unesite kategoriju prihoda"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Unesite datum prihoda"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv proizvođača"),
        "enterName": MessageLookupByLibrary.simpleMessage("Unesite naziv"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Unesite ime"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Unesite napomenu"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Unesite početno stanje"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos plaćanja"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Unesite lozinku"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos plaćanja"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Unesite cijenu"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Unesite kapacitet proizvoda"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Unesite šifru proizvoda"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Unesite boju proizvoda"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv proizvoda"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Unesite količinu proizvoda"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Unesite veličinu proizvoda"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Unesite tip proizvoda"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Unesite jedinicu proizvoda"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Unesite težinu proizvoda"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Unesite nabavnu cijenu"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Unesite referentni broj"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Unesite cijenu prodaje"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Unesite serijski broj"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Unesite Sadržaj Poruke"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Unesite Količinu Zalihe"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Unesite ID transakcije"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv jedinice"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Unesite ime uloge korisnika"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Unesite naslov korisnika"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Unesite garanciju"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Unesite veleprodajnu cijenu"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Unesite vašu adresu"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Unesite Adresu Vaše Tvrtke"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Unesite naziv vaše kompanije"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Unesite naziv vaše kompanije"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Unesite vašu email adresu"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Unesite vašu šifru"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Ponovo unesite vašu šifru"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Unesite broj telefona"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Unesite naziv vaše prodavnice"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv kategorije"),
        "expense": MessageLookupByLibrary.simpleMessage("Trošak"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Datum troška"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detalji troškova"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Trošak za"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Lista kategorija troškova"),
        "expenses": MessageLookupByLibrary.simpleMessage("Troškovi"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top pet proizvoda kupljenih ovog mjeseca"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Za neograničenu upotrebu"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Zaboravili ste šifru?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Besplatno sigurnosno kopiranje podataka"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Besplatno doživotno ažuriranje"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Besplatan paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Besplatan plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Počnite"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Vladina Identifikacija"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Ukupno"),
        "hold": MessageLookupByLibrary.simpleMessage("Zadrži"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Broj zadržavanja"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verifikacija Identiteta"),
        "inc": MessageLookupByLibrary.simpleMessage("Prihod"),
        "income": MessageLookupByLibrary.simpleMessage("Prihod"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija prihoda"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Lista kategorija prihoda"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Datum prihoda"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detalji prihoda"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Prihod za"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Lista prihoda"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Povećaj stanje"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Brivnost na trenutak"),
        "invoice": MessageLookupByLibrary.simpleMessage("Račun"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Račun:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Broj računa..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Broj računa"),
        "item": MessageLookupByLibrary.simpleMessage("Artikl"),
        "itemName": MessageLookupByLibrary.simpleMessage("Naziv stavke"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Verifikacija KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Detalji glavne knjige"),
        "ledger": MessageLookupByLibrary.simpleMessage("Glavna knjiga"),
        "left": MessageLookupByLibrary.simpleMessage("Lijevo"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Zajmovi"),
        "logOut": MessageLookupByLibrary.simpleMessage("Odjava"),
        "login": MessageLookupByLibrary.simpleMessage("Prijavi se"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Pozicija Logotipa na Fakturi?"),
        "loss": MessageLookupByLibrary.simpleMessage("Gubitak"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Gubitak/Profit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Gubitak(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Niska Zaliha"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Niski zalihi"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Napravite trajan utisak na svoje kupce s personaliziranim računima. Naš Unlimited Upgrade nudi jedinstvenu prednost prilagodbe računa, dodajući profesionalan dodir koji jača identitet vašeg brenda i potiče vjernost kupaca."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Proizvođač"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Panel za prijavu"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Panel za prijavu"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Mobilna aplikacija\n+\nRadna površina"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Potvrda o plaćanju"),
        "nam": MessageLookupByLibrary.simpleMessage("Naziv*"),
        "name": MessageLookupByLibrary.simpleMessage("Naziv"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Ime ili kod ili kategorija"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Novi kupci"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Novi kupci"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Novi prihod"),
        "no": MessageLookupByLibrary.simpleMessage("Ne"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Nema veze"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih kupaca"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih transakcija sa dugovanjima"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih kategorija troškova"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih kategorija prihoda"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih prihoda"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih Faktura"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih proizvoda"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nije pronađena transakcija kupovine"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih ponuda"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih izvještaja"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih Transakcija Prodaje"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih serijskih brojeva"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih Dobavljača"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih transakcija"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih korisnika"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih uloga korisnika"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih serijskih brojeva"),
        "note": MessageLookupByLibrary.simpleMessage("Napomena"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Otvoreni čekovi"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Početno stanje"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "ili prevucite i otpustite PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Narudžbe"),
        "other": MessageLookupByLibrary.simpleMessage("Ostalo"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Ostali prihodi"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Funkcionalnosti paketa"),
        "paid": MessageLookupByLibrary.simpleMessage("Plaćeno"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Uplaćeni iznos"),
        "partyName": MessageLookupByLibrary.simpleMessage("Naziv stranke"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tip stranke"),
        "password": MessageLookupByLibrary.simpleMessage("Šifra"),
        "payCash": MessageLookupByLibrary.simpleMessage("Plati gotovinom"),
        "payable": MessageLookupByLibrary.simpleMessage("Za plaćanje"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Iznos za plaćanje"),
        "payment": MessageLookupByLibrary.simpleMessage("Plaćanje"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Plaćanje unutra"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Plaćanje van"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Vrsta plaćanja"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Tip plaćanja"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Broj telefona"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verifikacija broja telefona"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Molimo dodajte prodaju"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Molimo dodajte klijenta"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Molimo provjerite svoju internet vezu"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Molimo vas preuzmite našu mobilnu aplikaciju i pretplatite se na paket kako biste koristili desktop verziju"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Molimo unesite stanje proizvoda"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Unesite validne podatke"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Molimo odaberite kupca"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Unesite validne podatke"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas panel za registraciju"),
        "practies": MessageLookupByLibrary.simpleMessage("Prakse"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium korisnička podrška"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Pregled"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Prethodni dug:"),
        "price": MessageLookupByLibrary.simpleMessage("Cijena"),
        "print": MessageLookupByLibrary.simpleMessage("Ispiši"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Ispis računa"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Ispis PDF-a"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Politika privatnosti"),
        "product": MessageLookupByLibrary.simpleMessage("Proizvod"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija proizvoda"),
        "productCod": MessageLookupByLibrary.simpleMessage("Šifra proizvoda*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Boja proizvoda"),
        "productList": MessageLookupByLibrary.simpleMessage("Lista proizvoda"),
        "productNam": MessageLookupByLibrary.simpleMessage("Naziv proizvoda*"),
        "productName": MessageLookupByLibrary.simpleMessage("Naziv proizvoda"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Veličina proizvoda"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Stanje proizvoda"),
        "productType": MessageLookupByLibrary.simpleMessage("Tip proizvoda"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Jedinica proizvoda"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garancija proizvoda"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Težina proizvoda"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapacitet proizvoda"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Uredi profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profit(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profit(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Kupovina"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Lista kupovine"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kupi Premium plan"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Cijena kupovine"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Kupovna Transakcija"),
        "quantity": MessageLookupByLibrary.simpleMessage("Količina*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Ponuda"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Lista ponuda"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Nedavne Prodaje"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referentni broj"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referentni broj"),
        "registration": MessageLookupByLibrary.simpleMessage("Registracija"),
        "remaining": MessageLookupByLibrary.simpleMessage("Preostalo: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Preostali saldo"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Preostalo dugovanje"),
        "reports": MessageLookupByLibrary.simpleMessage("Izvještaji"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Resetujte vašu šifru"),
        "retailer": MessageLookupByLibrary.simpleMessage("Prodavač"),
        "revenue": MessageLookupByLibrary.simpleMessage("Prihod"),
        "right": MessageLookupByLibrary.simpleMessage("Desno"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Iznos Prodaje"),
        "sale": MessageLookupByLibrary.simpleMessage("Prodaja"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Iznos prodaje"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Detalji prodaje"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista Prodaja"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Prodajna cijena*"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Cijena prodaje*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Povrat Prodaje"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcija Prodaje"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transakcije Prodaje (Povijest Ponuda)"),
        "sales": MessageLookupByLibrary.simpleMessage("Prodaje"),
        "salesList": MessageLookupByLibrary.simpleMessage("Lista prodaje"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Sačuvaj i objavi"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Spremi i objavi"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Spremi Promjene"),
        "search": MessageLookupByLibrary.simpleMessage("Pretraživanje..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Pretraži bilo što..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Pretraži po računu..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Pretraži po računu ili imenu"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Pretraži po imenu"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Pretraži po imenu ili broju telefona..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pretraži serijski broj"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Odaberite strane"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Odaberite brend proizvoda"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Odaberite serijski broj"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Odaberite varijacije:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Odaberite vreme garancije"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Izaberite jezik"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Pošalji Poruku"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serijski broj"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serijski broj"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Trošak usluge"),
        "setting": MessageLookupByLibrary.simpleMessage("Postavke"),
        "share": MessageLookupByLibrary.simpleMessage("Podijeli"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Dostava/Ostalo"),
        "shopName": MessageLookupByLibrary.simpleMessage("Naziv prodavnice"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Početno stanje prodavnice"),
        "show": MessageLookupByLibrary.simpleMessage("Prikaži >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Prikaz Logotipa na Fakturi?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Dostava/Usluga"),
        "size": MessageLookupByLibrary.simpleMessage("Veličina"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistika"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Ostanite u središtu tehnološkog napretka bez dodatnih troškova. Naš Pos Saas POS Unlimited Upgrade osigurava da uvijek imate najnovije alate i značajke na dohvat ruke, čime se osigurava da vaš posao ostane na samom vrhu."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Ostanite u središtu tehnološkog napretka bez dodatnih troškova. Naš Pos Sass POS Unlimited Upgrade osigurava da uvijek imate najnovije alate i značajke na dohvat ruke, čime se osigurava da vaš posao ostane na samom vrhu."),
        "stock": MessageLookupByLibrary.simpleMessage("Zaliha"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventar zaliha"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Izvještaj o zalihi"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Vrijednost zaliha"),
        "stockValues":
            MessageLookupByLibrary.simpleMessage("Vrijednost Zaliha"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Međuzbir"),
        "subciption": MessageLookupByLibrary.simpleMessage("Pretplata"),
        "submit": MessageLookupByLibrary.simpleMessage("Potvrdi"),
        "supplier": MessageLookupByLibrary.simpleMessage("Dobavljači"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Dugovanje dobavljača"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Faktura Dobavljača"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lista Dobavljača"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT kod"),
        "tSale": MessageLookupByLibrary.simpleMessage("Ukupne Prodaje"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Uzmite fotografiju vozačke dozvole, osobne iskaznice ili putovnice"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Uslovi korištenja"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Ime sve govori. S Pos Saas POS Unlimited nema ograničenja u upotrebi. Bez obzira radi li se o obradi nekoliko transakcija ili naglom prilivu kupaca, možete raditi s povjerenjem znajući da niste ograničeni limitima."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Ovaj kupac nema dospjelih iznosa"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Ovaj kupac ima prethodni dug"),
        "to": MessageLookupByLibrary.simpleMessage("Do"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Najprodavaniji proizvod"),
        "total": MessageLookupByLibrary.simpleMessage("ukupno"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Ukupan iznos"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Ukupni popust"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Ukupno za platiti"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Ukupna dugovanja"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Ukupni trošak"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Ukupni prihod"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Ukupno artikala: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Ukupni gubitak"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Ukupno plaćeno"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Ukupno za plaćanje"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Ukupno plaćanje van"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Ukupna cijena"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Ukupno proizvoda"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Ukupni profit"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Ukupna kupovina"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Ukupni Iznos Povrata"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Ukupni Povrati"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Ukupna prodaja"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Ukupna prodaja"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Ukupan PDV"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Ukupno plaćanje unutra"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transakcija"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID transakcije"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Izvještaj o Transakcijama"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Pokušajte ponovno"),
        "type": MessageLookupByLibrary.simpleMessage("Tip"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Neplaćeno"),
        "unit": MessageLookupByLibrary.simpleMessage("Jedinica"),
        "unitName": MessageLookupByLibrary.simpleMessage("Naziv jedinice"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Jedinična cijena"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Neograničeno"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Neograničeni računi"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Neograničena upotreba"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Otkrijte puni potencijal Pos Saas POS uz personalizirane obuke koje vodi naš stručni tim. Od osnovnih do naprednih tehnika, osiguravamo da budete dobro upućeni u korištenje svakog aspekta sustava radi optimizacije vaših poslovnih procesa."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Ažurirajte sada"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Prvo ažurirajte svoj plan.\\nLimit prodaje je premašen."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Nadogradnja putem mobilne aplikacije"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Otpremite sliku"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Učitajte Logotip Fakture"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Učitaj dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Učitaj datoteku"),
        "userName": MessageLookupByLibrary.simpleMessage("Korisničko ime"),
        "userRole": MessageLookupByLibrary.simpleMessage("Uloga korisnika"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Ime uloge korisnika"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Naslov korisnika"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("PDV/PDV"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifikuj broj telefona"),
        "view": MessageLookupByLibrary.simpleMessage("Pregled"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Klijent na licu mjesta"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garancija"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garancija"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Moramo registrovati vaš broj telefona prije nego što počnemo!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Razumijemo važnost besprijekornog poslovanja. Zato je naša podrška dostupna non-stop da vam pomogne, bilo da se radi o brzom upitu ili sveobuhvatnoj zabrinutosti. Povežite se s nama bilo kada, bilo gdje putem poziva ili WhatsApp-a kako biste doživjeli neusporedivu korisničku uslugu."),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Veletrgovac"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Veleprodaja"),
        "wight": MessageLookupByLibrary.simpleMessage("Težina"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Da, Vrati"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Morate se ponovno prijaviti na svoj račun."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Morate provjeriti identitet prije nego što kupite poruke"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Lista svih vaših prodaja"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Sve vaše prodaje"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Koristite"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Vaše dospjele prodaje"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Morate provjeriti identitet prije nego što kupite poruke"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Vaš paket"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Vaša uplata je otkazana"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Vaša uplata je uspješno izvršena"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Vaše plaćanje je otkazano")
      };
}
