// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ms locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ms';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("LISÄÄ MYYNTI"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORI"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("INVOIS"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS-myynti"),
        "PRICE": MessageLookupByLibrary.simpleMessage("HARGA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NAMA PRODUK"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Panel Log Masuk Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("JUMLAH"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Kuantiti*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("JUMLAH NILAI"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Tajuk Pengguna"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Mengenai Aplikasi"),
        "accountName": MessageLookupByLibrary.simpleMessage("Nama Akaun"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Nombor Akaun"),
        "action": MessageLookupByLibrary.simpleMessage("Tindakan"),
        "add": MessageLookupByLibrary.simpleMessage("Tambah"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Tambah Jenama"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Tambah Kategori"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Tambah Pelanggan"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Tambah penerangan...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Lisää asiakirja"),
        "addItem": MessageLookupByLibrary.simpleMessage("Lisää tuote"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Tambah Kategori Item"),
        "addNew": MessageLookupByLibrary.simpleMessage("Tambah Baru"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Tambah Pengguna Baru"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Tambah Produk"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Berjaya Ditambah"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Lisää toimittaja"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Tambah Unit"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Tambah/Kemaskini Senarai Perbelanjaan"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Tambah/Kemaskini Senarai Pendapatan"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Tambah Peranan Pengguna"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Menambah Nombor Siri?"),
        "address": MessageLookupByLibrary.simpleMessage("Alamat"),
        "all": MessageLookupByLibrary.simpleMessage("Semua"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Semua Ciri Asas"),
        "alreadyHaveAnAccounts": MessageLookupByLibrary.simpleMessage(
            "Adakah anda sudah mempunyai akaun?"),
        "amount": MessageLookupByLibrary.simpleMessage("Jumlah"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Sokongan Aplikasi Android & iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Adakah anda ingin membuat sebut harga ini?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Adakah anda mahu memadam pelanggan ini?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Adakah anda ingin memadam produk ini"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Adakah anda mahu memadam Kutipan ini?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Haluatko palauttaa tämän myynnin?"),
        "balance": MessageLookupByLibrary.simpleMessage("Baki"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Mata Wang Akaun Bank"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Pankkitilit"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Maklumat Bank"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nama Bank"),
        "between": MessageLookupByLibrary.simpleMessage("Antara"),
        "billTo": MessageLookupByLibrary.simpleMessage("Kepada:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Nama Cawangan"),
        "brand": MessageLookupByLibrary.simpleMessage("Jenama"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nama Jenama"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Kategori Perniagaan"),
        "buy": MessageLookupByLibrary.simpleMessage("Beli"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Beli Pelan Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Osta tekstiviestejä"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Batal"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapasiti"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("Käteinen ja pankki"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Käteinen kädessä"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategori"),
        "category": MessageLookupByLibrary.simpleMessage("Kategori"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Nama Kategori"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Jumlah Tukar"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Muutettava summa"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("Tarkista takuu"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Pilih pelan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Kumpulkan Hutang >"),
        "color": MessageLookupByLibrary.simpleMessage("Warna"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Nama Syarikat"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Yrityksen osoite"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Yrityksen kuvaus"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Yrityksen sähköpostiosoite"),
        "companyName": MessageLookupByLibrary.simpleMessage("Nama Syarikat"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Yrityksen puhelinnumero"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Yrityksen verkkosivuston osoite"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Sahkan kata laluan"),
        "continu": MessageLookupByLibrary.simpleMessage("Teruskan"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Tukar Ke Jualan"),
        "create": MessageLookupByLibrary.simpleMessage("Buat"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Buat Pembayaran"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Dicipta Oleh"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Luovuuden keskus"),
        "currency": MessageLookupByLibrary.simpleMessage("Mata Wang"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Pelan Semasa"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Pengekalan Jentera Invois Pengekalan"),
        "customer": MessageLookupByLibrary.simpleMessage("Asiakas"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Hutang Pelanggan"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Asiakaslaskut"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Senarai Pelanggan"),
        "customerName": MessageLookupByLibrary.simpleMessage("Nama Pelanggan"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kuukauden asiakas"),
        "customerType": MessageLookupByLibrary.simpleMessage("Asiakastyyppi"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Asiakas: Astumiskävijä"),
        "customers": MessageLookupByLibrary.simpleMessage("Pelanggan"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Pengumpulan Harian"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Jualan Harian"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transaksi Harian"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Papan Pemuka"),
        "date": MessageLookupByLibrary.simpleMessage("Tarikh"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Päivämäärä ja aika"),
        "dealer": MessageLookupByLibrary.simpleMessage("Peniaga"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Harga Peniaga"),
        "delete": MessageLookupByLibrary.simpleMessage("Padam"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Caj Penghantaran"),
        "description": MessageLookupByLibrary.simpleMessage("Penerangan"),
        "details": MessageLookupByLibrary.simpleMessage("Butiran >"),
        "discount": MessageLookupByLibrary.simpleMessage("Diskaun"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Alennettu hinta"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Lataa PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Tanggungan"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Jumlah Hutang"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Avoin summa näytetään tässä, jos saatavilla"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Saamisten kerääminen"),
        "dueList": MessageLookupByLibrary.simpleMessage("Avoin lista"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transaksi Tertunggak"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Edit/Tambah Siri:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Edit profil anda"),
        "email": MessageLookupByLibrary.simpleMessage("Emel"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Masukkan Jumlah"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Jenama"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Kategori"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Syötä yrityksen kuvaus"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Syötä yrityksen sähköpostiosoite"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Syötä yrityksen puhelinnumero"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Syötä yrityksen verkkosivuston osoite"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Pelanggan"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Peniaga"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Syötä alennettu hinta"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Masukkan Kategori Perbelanjaan"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage(
            "Masukkan Tarikh Perbelanjaan"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Masukkan Kategori Pendapatan"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Masukkan Tarikh Pendapatan"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Pembuat"),
        "enterName": MessageLookupByLibrary.simpleMessage("Masukkan Nama"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Masukkan Nama"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Masukkan Catatan"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Masukkan Baki Pembukaan"),
        "enterPaidAmount": MessageLookupByLibrary.simpleMessage(
            "Masukkan jumlah yang dibayar"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Kata Laluan"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Masukkan Jumlah Bayaran"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Masukkan Harga"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Masukkan Kapasiti Produk"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Masukkan Kod Produk"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Masukkan Warna Produk"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Produk"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Masukkan Kuantiti Produk"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Masukkan Saiz Produk"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Masukkan Jenis Produk"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Masukkan Unit Produk"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Masukkan Berat Produk"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Perolehan"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Masukkan Nombor Rujukan"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Jualan"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Masukkan Nombor Siri"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Syötä viestin sisältö"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Syötä varaston määrä"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Masukkan ID Transaksi"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Unit"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Masukkan Nama Peranan Pengguna"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Masukkan Tajuk Pengguna"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Masukkan Jaminan"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Runcit"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Masukkan jumlah anda"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Masukkan Alamat Anda"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Syötä yrityksen osoite"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Syarikat Anda"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Syarikat Anda"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Masukkan alamat e-mel anda"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Kata Laluan Anda"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Masukkan kata laluan anda sekali lagi"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Masukkan nombor telefon anda"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Kedai Anda"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Kategori"),
        "expense": MessageLookupByLibrary.simpleMessage("Perbelanjaan"),
        "expenseDate":
            MessageLookupByLibrary.simpleMessage("Tarikh Perbelanjaan"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Butiran Perbelanjaan"),
        "expenseFor":
            MessageLookupByLibrary.simpleMessage("Perbelanjaan Untuk"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Senarai Kategori Perbelanjaan"),
        "expenses": MessageLookupByLibrary.simpleMessage("Perbelanjaan"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Lima Produk Paling Membeli-belah Bulan Ini"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Untuk Penggunaan Tanpa Had"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Lupa Kata Laluan?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Pencadangan Data Percuma"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Kemaskini Seumur Hidup Percuma"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Pakej Percuma"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Pelan Percuma"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Mula"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Virallinen henkilötodistus"),
        "grandTotal":
            MessageLookupByLibrary.simpleMessage("Jumlah Keseluruhan"),
        "hold": MessageLookupByLibrary.simpleMessage("Tahan"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Nombor Tahan"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Tunnistus vahvistettu"),
        "inc": MessageLookupByLibrary.simpleMessage("Pendapatan"),
        "income": MessageLookupByLibrary.simpleMessage("Pendapatan"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategori Pendapatan"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Senarai Kategori Pendapatan"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Tarikh Pendapatan"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Butiran Pendapatan"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Pendapatan Untuk"),
        "incomeList":
            MessageLookupByLibrary.simpleMessage("Senarai Pendapatan"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Tambah Stok"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Kerahsiaan Segera"),
        "invoice": MessageLookupByLibrary.simpleMessage("Invois"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Invois:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Laskun numero..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("No. Invois"),
        "item": MessageLookupByLibrary.simpleMessage("Tuote"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nama Item"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC-tarkistus"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Butiran Lejar"),
        "ledger": MessageLookupByLibrary.simpleMessage("Päiväkirja"),
        "left": MessageLookupByLibrary.simpleMessage("Vasen"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Lainatilit"),
        "logOut": MessageLookupByLibrary.simpleMessage("Kirjaudu ulos"),
        "login": MessageLookupByLibrary.simpleMessage("Log Masuk"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logon sijainti laskussa?"),
        "loss": MessageLookupByLibrary.simpleMessage("Kerugian"),
        "lossOrProfit":
            MessageLookupByLibrary.simpleMessage("Keuntungan/Kerugian"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Kerugian(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Stok Rendah"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Stok Rendah"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Buat kesan yang berkekalan kepada pelanggan anda dengan invois berjenama. Kemaskini Tanpa Had kami menawarkan kelebihan unik penyesuaian invois anda, menambah sentuhan profesional yang memperkuat identiti jenama anda dan membina kesetiaan pelanggan."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Pembuat"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Panel Log Masuk Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Panel Pendaftaran Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Aplikasi Mudah Alih\n+\nDesktop"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Resit Wang"),
        "nam": MessageLookupByLibrary.simpleMessage("Nama*"),
        "name": MessageLookupByLibrary.simpleMessage("Nama"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nama atau Kod atau Kategori"),
        "newCusotmers":
            MessageLookupByLibrary.simpleMessage("Pelanggan Baharu"),
        "newCustomers":
            MessageLookupByLibrary.simpleMessage("Pelanggan Baharu"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Pendapatan Baharu"),
        "no": MessageLookupByLibrary.simpleMessage("Ei"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Tiada Sambungan"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Tiada Pelanggan Dijumpai"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Tiada Transaksi Hutang Dijumpai"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Tiada Kategori Perbelanjaan Dijumpai"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Tiada Kategori Pendapatan Dijumpai"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Tiada Pendapatan Dijumpai"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Laskuja ei löytynyt"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Tiada Produk Dijumpai"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Tiada transaksi pembelian dijumpai"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Tiada Kutipan Dijumpai"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("TIADA Laporan Dijumpai"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Myyntitapahtumia ei löytynyt"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Tiada Nombor Siri Dijumpai"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Toimittajia ei löytynyt"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Tiada Transaksi Dijumpai"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Tiada Pengguna Dijumpai"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Tiada Peranan Pengguna Dijumpai"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Tiada Nombor Siri Dijumpai"),
        "note": MessageLookupByLibrary.simpleMessage("Catatan"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Avoimet sekit"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Baki Pembukaan"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " atau seret & lepas PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Pesanan"),
        "other": MessageLookupByLibrary.simpleMessage("Lain-lain"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Pendapatan Lain"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("Ciri Pakej"),
        "paid": MessageLookupByLibrary.simpleMessage("Dibayar"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Jumlah Dibayar"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nama Pihak"),
        "partyType": MessageLookupByLibrary.simpleMessage("Jenis Pihak"),
        "password": MessageLookupByLibrary.simpleMessage("Kata Laluan"),
        "payCash": MessageLookupByLibrary.simpleMessage("Bayar Tunai"),
        "payable": MessageLookupByLibrary.simpleMessage("Hutang"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Jumlah Bayaran"),
        "payment": MessageLookupByLibrary.simpleMessage("Pembayaran"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Pembayaran Masuk"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Pembayaran Keluar"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Jenis Pembayaran"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Jenis Pembayaran"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Nombor Telefon"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Pengesahan Telefon"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage("Lisää myynti"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Lisää asiakas"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Sila periksa konektiviti Internet anda"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Sila muat turun aplikasi mudah alih kami dan langgan pakej untuk menggunakan versi desktop"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Sila masukkan stok produk"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Sila masukkan data yang sah"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Valitse asiakas"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Sila masukkan data yang sah"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Panel Pendaftaran Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Latihan"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Sokongan Pelanggan Premium"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Pelan Premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Esikatselu"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Terdahulu:"),
        "price": MessageLookupByLibrary.simpleMessage("Harga"),
        "print": MessageLookupByLibrary.simpleMessage("Cetak"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Cetak Invois"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Tulosta PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Polisi Privasi"),
        "product": MessageLookupByLibrary.simpleMessage("Produk"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategori Produk"),
        "productCod": MessageLookupByLibrary.simpleMessage("Kod Produk*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Warna Produk"),
        "productList": MessageLookupByLibrary.simpleMessage("Senarai Produk"),
        "productNam": MessageLookupByLibrary.simpleMessage("Nama Produk*"),
        "productName": MessageLookupByLibrary.simpleMessage("Nama Produk"),
        "productSize": MessageLookupByLibrary.simpleMessage("Saiz Produk"),
        "productStock": MessageLookupByLibrary.simpleMessage("Stok Produk"),
        "productType": MessageLookupByLibrary.simpleMessage("Jenis Produk"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Unit Produk"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Jaminan Produk"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Berat Produk"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapasiti Produk"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Sunting Profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Keuntungan"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Kerugian(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Keuntungan(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Beli"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Senarai Pembelian"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Beli Pelan Premium"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Harga Perolehan"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Ostotapahtuma"),
        "quantity": MessageLookupByLibrary.simpleMessage("Kuantiti"),
        "quotation": MessageLookupByLibrary.simpleMessage("Sebut Harga"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Senarai Kutipan"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Jualan Terkini"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Jumlah Diterima"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("No. Rujukan"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Nombor Rujukan"),
        "registration": MessageLookupByLibrary.simpleMessage("Pendaftaran"),
        "remaining": MessageLookupByLibrary.simpleMessage("Baki: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("Baki Baki"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Hutang Baki"),
        "reports": MessageLookupByLibrary.simpleMessage("Raportit"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Set Semula Kata Laluan Anda"),
        "retailer": MessageLookupByLibrary.simpleMessage("Peruncit"),
        "revenue": MessageLookupByLibrary.simpleMessage("Pendapatan"),
        "right": MessageLookupByLibrary.simpleMessage("Oikea"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Jumlah Jualan"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Lindungi data perniagaan anda dengan mudah. Kemaskini Tanpa Had Pos Saas POS kami termasuk pencadangan data percuma, memastikan maklumat berharga anda dilindungi daripada sebarang peristiwa yang tidak dijangka. Tumpukan kepada apa yang benar-benar penting - pertumbuhan perniagaan anda."),
        "sale": MessageLookupByLibrary.simpleMessage("Myynti"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Jumlah Jualan"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Myyntitiedot"),
        "saleList": MessageLookupByLibrary.simpleMessage("Myyntilista"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Harga Jualan"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Harga Jualan*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Myyntipalautus"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Myyntitapahtuma"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Myyntitapahtumat (Tarjousmyyntihistoria)"),
        "sales": MessageLookupByLibrary.simpleMessage("Myynnit"),
        "salesList": MessageLookupByLibrary.simpleMessage("Senarai Jualan"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Simpan & Terbitkan"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Simpan & Terbitkan"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Tallenna muutokset"),
        "search": MessageLookupByLibrary.simpleMessage("Hae..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Etsi mitä tahansa..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Cari mengikut invois...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Cari mengikut invois atau nama"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Cari mengikut Nama"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Cari mengikut Nama atau Telefon..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Cari Nombor Siri"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Pilih Pihak"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Pilih Jenama Produk"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pilih Nombor Siri"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Pilih Variasi:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Pilih Tempoh Jaminan"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Pilih bahasa anda"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Lähetä viesti"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Nombor Siri"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Nombor Siri"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Caj Perkhidmatan"),
        "setting": MessageLookupByLibrary.simpleMessage("Asetukset"),
        "share": MessageLookupByLibrary.simpleMessage("Jaa"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Toimitus/Muu"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nama Kedai"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Pembukaan Baki Kedai"),
        "show": MessageLookupByLibrary.simpleMessage("Tunjuk >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Näytä logo laskussa?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Penghantaran/Perkhidmatan"),
        "size": MessageLookupByLibrary.simpleMessage("Saiz"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistik"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Tetap di hadapan perkembangan teknologi tanpa sebarang kos tambahan. Kemaskini Tanpa Had Pos Saas POS kami memastikan anda sentiasa memiliki alat dan ciri terkini di hujung jari anda, menjamin perniagaan anda sentiasa di hadapan."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Tetap di hadapan perkembangan teknologi tanpa sebarang kos tambahan. Kemaskini Tanpa Had Pos Sass POS kami memastikan anda sentiasa memiliki alat dan ciri terkini di hujung jari anda, menjamin perniagaan anda sentiasa di hadapan."),
        "stock": MessageLookupByLibrary.simpleMessage("Varasto"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventori Stok"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Laporan Stok"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Nilai Stok"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Nilai Stok"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Jumlah Harga"),
        "subciption": MessageLookupByLibrary.simpleMessage("Tilaus"),
        "submit": MessageLookupByLibrary.simpleMessage("Hantar"),
        "supplier": MessageLookupByLibrary.simpleMessage("Pembekal"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Hutang Pemiutang"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Toimittajan lasku"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Toimittajaluettelo"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Kod SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Jualan Keseluruhan"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Ota ajokortti, henkilökortti tai passikuva"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Terma Penggunaan"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Nama itu mencakup segalanya. Dengan Pos Saas POS Tanpa Had, tidak ada had pada penggunaan anda. Sama ada anda memproses beberapa transaksi atau menghadapi lonjakan pelanggan, anda boleh beroperasi dengan yakin, mengetahui anda tidak terikat oleh had."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Tällä asiakkaalla ei ole avointa summaa"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Pelanggan ini mempunyai hutang terdahulu"),
        "to": MessageLookupByLibrary.simpleMessage("Ke"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Eniten myyty tuote"),
        "total": MessageLookupByLibrary.simpleMessage("Jumlah"),
        "totalAmount":
            MessageLookupByLibrary.simpleMessage("Jumlah Keseluruhan"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Jumlah Diskaun"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Jumlah Hutang"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Jumlah Hutang"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("Jumlah Perbelanjaan"),
        "totalIncome":
            MessageLookupByLibrary.simpleMessage("Pendapatan Keseluruhan"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Kokonaismäärä: 2"),
        "totalLoss":
            MessageLookupByLibrary.simpleMessage("Kerugian Keseluruhan"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Jumlah Dibayar"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Jumlah Perlu Dibayar"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Jumlah Pembayaran Keluar"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Jumlah Harga"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Jumlah Produk"),
        "totalProfit":
            MessageLookupByLibrary.simpleMessage("Keuntungan Keseluruhan"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Jumlah Pembelian"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Palautussumma yhteensä"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Palautukset yhteensä"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Jumlah Jualan"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Jumlah Jualan"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Jumlah Cukai"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Jumlah Pembayaran Masuk"),
        "transaction": MessageLookupByLibrary.simpleMessage("Tapahtuma"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID Transaksi"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Tapahtumaraportti"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Cuba Lagi"),
        "type": MessageLookupByLibrary.simpleMessage("Jenis"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Tidak Dibayar"),
        "unit": MessageLookupByLibrary.simpleMessage("Unit"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nama Unit"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Harga Satuan"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Tanpa Had"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Invois Tanpa Had"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Penggunaan Tanpa Had"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Buka potensi penuh Pos Saas POS dengan sesi latihan peribadi yang dipimpin oleh pasukan pakar kami. Dari asas hingga teknik lanjutan, kami memastikan anda mahir dalam menggunakan setiap aspek sistem untuk mengoptimumkan proses perniagaan anda."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Kemaskini Sekarang"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Sila kemaskini rancangan anda terlebih dahulu\\nHad Jualan telah tamat."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Päivitä mobiilisovelluksessa"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Muat naik imej"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Lataa laskun logo"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Muat Naik Dokumen"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Muat Naik Fail"),
        "userName": MessageLookupByLibrary.simpleMessage("Nama Pengguna"),
        "userRole": MessageLookupByLibrary.simpleMessage("Peranan Pengguna"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nama Peranan Pengguna"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Tajuk Pengguna"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("Cukai Jualan/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Sahkan Nombor Telefon"),
        "view": MessageLookupByLibrary.simpleMessage("Lihat"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Astumiskävijä"),
        "warranty": MessageLookupByLibrary.simpleMessage("Jaminan"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Jaminan"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Kami perlu mendaftarkan telefon anda sebelum memulakan!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Kami memahami kepentingan operasi yang lancar. Itulah mengapa sokongan sepanjang masa kami sentiasa ada untuk membantu anda, sama ada untuk pertanyaan pantas atau kebimbangan menyeluruh. Hubungi kami pada bila-bila masa, di mana-mana melalui panggilan atau WhatsApp untuk mengalami perkhidmatan pelanggan yang tiada tandingan."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Harga Runcit"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Tukkumyyjä"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Runcit"),
        "wight": MessageLookupByLibrary.simpleMessage("Berat"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Kyllä, palauta"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Anda perlu LOG MASUK semula ke akaun anda."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Sinun on vahvistettava henkilöllisyytesi ennen viestien ostamista"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Kaikki myyntilistat"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Kaikki myynnit"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Anda menggunakan"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("Avoin myynti"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Sinun on vahvistettava henkilöllisyytesi ennen viestien ostamista"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Pakej Anda"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "Pembayaran anda telah dibatalkan"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Pembayaran anda telah berjaya"),
        "yourPaymentIscancelled": MessageLookupByLibrary.simpleMessage(
            "Pembayaran anda telah dibatalkan")
      };
}
