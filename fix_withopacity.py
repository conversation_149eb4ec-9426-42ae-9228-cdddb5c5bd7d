#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت لحل مشكلة withOpacity في مشروع Flutter
يستبدل جميع استخدامات withOpacity بـ Color.fromRGBO لتجنب التحذيرات

المؤلف: مساعد الذكي
التاريخ: 2025-07-15
"""

import os
import re
import sys
from pathlib import Path
import argparse

class WithOpacityFixer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.changes_made = 0
        self.files_processed = 0
        self.detailed_changes = []  # لحفظ تفاصيل التغييرات
        
        # قاموس الألوان المعرفة مسبقاً في المشروع
        self.predefined_colors = {
            'kMainColor': '0xff8424FF',
            'kDarkGreyColor': '0xFF2E2E3E',
            'kLitGreyColor': '0xFFD4D4D8',
            'kGreyTextColor': '0xFF585865',
            'kChartColor': '0xff2E2E3E',
            'kBorderColorTextField': '0xFFE8E7E5',
            'kDarkWhite': '0xFFF2F6F8',
            'kbgColor': '0xFFF8F3FF',
            'kWhite': '0xFFFFFFFF',
            'kRedTextColor': '0xFFFE2525',
            'kBlueTextColor': '0xff8424FF',
            'kYellowColor': '0xFFFF8C00',
            'kGreenTextColor': '0xff8424FF',
            'kTitleColor': '0xFF2E2E3E',
            'kPremiumPlanColor': '0xFF8752EE',
            'kPremiumPlanColor2': '0xFFFF5F00',
            'lightGreyColor': '0xFFF8F3FF',
            'dropdownItemColor': '0xFFF2F6F8',
        }
        
    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")
        
        for file_path in self.project_path.rglob("*.dart"):
            # تجاهل ملفات البناء والملفات المولدة
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")
        
    def hex_to_rgba(self, hex_color, opacity):
        """تحويل اللون من hex إلى RGBA"""
        # إزالة 0x أو # من بداية اللون
        hex_color = hex_color.replace('0x', '').replace('0X', '').replace('#', '')
        
        # التأكد من أن اللون 8 أحرف (ARGB) أو 6 أحرف (RGB)
        if len(hex_color) == 8:
            # ARGB format
            a = int(hex_color[0:2], 16)
            r = int(hex_color[2:4], 16)
            g = int(hex_color[4:6], 16)
            b = int(hex_color[6:8], 16)
        elif len(hex_color) == 6:
            # RGB format
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            a = 255
        else:
            return None
            
        # حساب الشفافية الجديدة
        new_alpha = int(a * opacity)
        
        return f"Color.fromRGBO({r}, {g}, {b}, {opacity})"
        
    def get_color_value(self, color_name):
        """الحصول على قيمة اللون من الاسم"""
        # البحث في الألوان المعرفة مسبقاً
        if color_name in self.predefined_colors:
            return self.predefined_colors[color_name]
            
        # البحث في ألوان Flutter المدمجة
        flutter_colors = {
            'Colors.white': '0xFFFFFFFF',
            'Colors.black': '0xFF000000',
            'Colors.red': '0xFFF44336',
            'Colors.green': '0xFF4CAF50',
            'Colors.blue': '0xFF2196F3',
            'Colors.yellow': '0xFFFFEB3B',
            'Colors.orange': '0xFFFF9800',
            'Colors.purple': '0xFF9C27B0',
            'Colors.grey': '0xFF9E9E9E',
            'Colors.redAccent': '0xFFFF5252',
            'Colors.greenAccent': '0xFF69F0AE',
            'Colors.blueAccent': '0xFF448AFF',
            'Colors.yellowAccent': '0xFFFFFF00',
            'Colors.orangeAccent': '0xFFFF9100',
            'Colors.purpleAccent': '0xFFE040FB',
            'Colors.transparent': '0x00000000',
            'Colors.white70': '0xB3FFFFFF',
            'Colors.white60': '0x99FFFFFF',
            'Colors.white54': '0x8AFFFFFF',
            'Colors.white38': '0x62FFFFFF',
            'Colors.white30': '0x4DFFFFFF',
            'Colors.white24': '0x3DFFFFFF',
            'Colors.white12': '0x1FFFFFFF',
            'Colors.white10': '0x1AFFFFFF',
            'Colors.black87': '0xDD000000',
            'Colors.black54': '0x8A000000',
            'Colors.black45': '0x73000000',
            'Colors.black38': '0x61000000',
            'Colors.black26': '0x42000000',
            'Colors.black12': '0x1F000000',
        }
        
        return flutter_colors.get(color_name)
        
    def fix_withopacity_in_content(self, content):
        """إصلاح withOpacity في محتوى الملف"""
        changes = 0

        # أنماط مختلفة للبحث عن withOpacity
        patterns = [
            # نمط للألوان المباشرة: Color(0xFF...).withOpacity(value)
            r'(Color\(0x[A-Fa-f0-9]+\))\.withOpacity\(([\d.]+)\)',
            # نمط للألوان مع const: const Color(0xFF...).withOpacity(value)
            r'(const\s+Color\(0x[A-Fa-f0-9]+\))\.withOpacity\(([\d.]+)\)',
            # نمط للألوان المعرفة مسبقاً فقط (تجنب المتغيرات الديناميكية)
            r'(k\w+Color|Colors\.\w+)\.withOpacity\(([\d.]+)\)',
        ]

        def replace_withopacity(match):
            nonlocal changes
            color_part = match.group(1)
            opacity = float(match.group(2))

            # إزالة const إذا وجدت
            clean_color_part = color_part.replace('const ', '')

            # إذا كان اللون مباشر مثل Color(0xFF...)
            if clean_color_part.startswith('Color(0x'):
                hex_match = re.search(r'Color\(0x([A-Fa-f0-9]+)\)', clean_color_part)
                if hex_match:
                    hex_value = '0x' + hex_match.group(1)
                    replacement = self.hex_to_rgba(hex_value, opacity)
                    if replacement:
                        changes += 1
                        # إضافة const إذا كانت موجودة في الأصل
                        if color_part.startswith('const'):
                            return f"const {replacement}"
                        return replacement

            # إذا كان اللون متغير معرف مسبقاً
            else:
                hex_value = self.get_color_value(clean_color_part)
                if hex_value:
                    replacement = self.hex_to_rgba(hex_value, opacity)
                    if replacement:
                        changes += 1
                        return replacement

            # إذا لم نتمكن من تحويل اللون، نتركه كما هو
            return match.group(0)

        # تطبيق جميع الأنماط
        new_content = content
        for pattern in patterns:
            new_content = re.sub(pattern, replace_withopacity, new_content)

        return new_content, changes
        
    def process_file(self, file_path):
        """معالجة ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            new_content, file_changes = self.fix_withopacity_in_content(content)
            
            if file_changes > 0:
                # إنشاء نسخة احتياطية
                backup_path = str(file_path) + '.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                # كتابة المحتوى الجديد
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                    
                print(f"✅ {file_path.name}: تم إصلاح {file_changes} استخدام")
                self.changes_made += file_changes
                
            self.files_processed += 1
            
        except Exception as e:
            print(f"❌ خطأ في معالجة {file_path}: {e}")

    def generate_report(self):
        """إنشاء تقرير مفصل عن التغييرات"""
        if self.changes_made == 0:
            return

        report_path = self.project_path / "withopacity_fix_report.txt"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("تقرير إصلاح مشكلة withOpacity\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"التاريخ: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"الملفات المعالجة: {self.files_processed}\n")
            f.write(f"إجمالي التغييرات: {self.changes_made}\n\n")

            if self.detailed_changes:
                f.write("تفاصيل التغييرات:\n")
                f.write("-" * 20 + "\n")
                for change in self.detailed_changes:
                    f.write(f"الملف: {change['file']}\n")
                    f.write(f"التغييرات: {change['count']}\n")
                    f.write(f"الأمثلة: {change['examples'][:3]}\n\n")  # أول 3 أمثلة

        print(f"📄 تم إنشاء تقرير مفصل: {report_path}")
            
    def run(self):
        """تشغيل السكربت"""
        print("🚀 بدء إصلاح مشكلة withOpacity...")
        print("=" * 50)
        
        self.find_dart_files()
        
        if not self.dart_files:
            print("❌ لم يتم العثور على ملفات Dart")
            return
            
        print(f"📝 معالجة {len(self.dart_files)} ملف...")
        print("-" * 30)
        
        for file_path in self.dart_files:
            self.process_file(file_path)
            
        print("-" * 30)
        print(f"✅ تم الانتهاء!")
        print(f"📊 الإحصائيات:")
        print(f"   - الملفات المعالجة: {self.files_processed}")
        print(f"   - التغييرات المطبقة: {self.changes_made}")

        if self.changes_made > 0:
            self.generate_report()
            print(f"\n💾 تم إنشاء نسخ احتياطية بامتداد .backup")
            print(f"🔄 لاستعادة الملفات الأصلية، استخدم:")
            print(f"   python fix_withopacity.py --restore")
        else:
            print(f"\n🎉 لا توجد مشاكل withOpacity في المشروع!")

def main():
    parser = argparse.ArgumentParser(description='إصلاح مشكلة withOpacity في مشروع Flutter')
    parser.add_argument('--path', default='.', help='مسار المشروع (افتراضي: المجلد الحالي)')
    parser.add_argument('--restore', action='store_true', help='استعادة الملفات من النسخ الاحتياطية')
    
    args = parser.parse_args()
    
    if args.restore:
        # استعادة الملفات من النسخ الاحتياطية
        project_path = Path(args.path)
        backup_files = list(project_path.rglob("*.dart.backup"))
        
        if not backup_files:
            print("❌ لم يتم العثور على نسخ احتياطية")
            return
            
        print(f"🔄 استعادة {len(backup_files)} ملف...")
        
        for backup_file in backup_files:
            original_file = backup_file.with_suffix('')
            try:
                backup_file.replace(original_file)
                print(f"✅ تم استعادة {original_file.name}")
            except Exception as e:
                print(f"❌ خطأ في استعادة {original_file.name}: {e}")
                
        print("✅ تم الانتهاء من الاستعادة")
    else:
        # تشغيل إصلاح withOpacity
        fixer = WithOpacityFixer(args.path)
        fixer.run()

if __name__ == "__main__":
    main()
