// // String currency = localCurrency;
// String currency = '\$';
//
// String localCurrency = 'Tsh';
//
// // List<String> items = [
// //   '\$ (US Dollar)',
// //   'Tsh (TZ Shillings)'
// // ];

String currency = localCurrency;


String localCurrency = '\$';

List<String> items = [
  '\$ (US Dollar)',
  "₹ (Rupee)",
  "€ (Euro)",
  "₽ (Ruble)",
  "£ (UK Pound)",
  '৳ (Taka)',
  "R (Rial)",
  "؋ (Afghani)",
  "Lek (Lek)",
  "د.ج (Algerian Dinar)",
  "Kz (Kwanza)",
  "EC\$ (East Caribbean Dollar)",
  "\$ (Argentine Peso)",
  "֏ (Armenian Dram)",
  "A\$ (Australian Dollar)",
  "₼ (Azerbaijani Manat)",
  "B\$ (Bahamian Dollar)",
  ".د.ب (Bahraini Dinar)",
  "৳ (Bangladeshi Taka)",
  "Bds\$ (Barbadian Dollar)",
  "Br (Belarusian Ruble)",
  "BZ\$ (Belize Dollar)",
  "CFA (West African CFA franc)",
  "FCFA (West African franc)",
  "Nu. (Bhutanese Ngultrum)",
  "Bs. (Bolivian Boliviano)",
  "KM (Bosnia and Herzegovina Convertible Mark)",
  "P (Botswana Pula)",
  "R\$ (Brazilian Real)",
  "B\$ (Brunei Dollar)",
  "лв (Bulgarian Lev)",
  "FBu (Burundian Franc)",
  "Esc (Cape Verdean Escudo)",
  "៛ (Cambodian Riel)",
  "CFA F (Central African CFA franc)",
  "CA\$ (Canadian Dollar)",
  "\$ (Chilean Peso)",
  "¥ (Chinese Yuan)",
  "\$ (Colombian Peso)",
  "CF (Comorian Franc)",
  "FC (Congolese Franc)",
  "₡ (Costa Rican Colón)",
  "kn (Croatian Kuna)",
  "CUP (Cuban Peso)",
  "Kč (Czech Koruna)",
  "kr (Danish Krone)",
  "Fdj (Djiboutian Franc)",
  "RD\$ (Dominican Peso)",
  "US\$ (United States Dollar)",
  "\$ (United States Dollar)",
  "E£ (Egyptian Pound)",
  "Nfk (Eritrean Nakfa)",
  "E (Swazi Lilangeni)",
  "Br (Ethiopian Birr)",
  "FJ\$ (Fijian Dollar)",
  "D (Gambian Dalasi)",
  "₾ (Georgian Lari)",
  "GH₵ (Ghanaian Cedi)",
  "Q (Guatemalan Quetzal)",
  "GNF (Guinean Franc)",
  "GY\$ (Guyanese Dollar)",
  "G (Haitian Gourde)",
  "L (Honduran Lempira)",
  "Ft (Hungarian Forint)",
  "kr (Icelandic Króna)",
  "₹ (Indian Rupee)",
  "Rp (Indonesian Rupiah)",
  "﷼ (Iranian Rial)",
  "ع.د (Iraqi Dinar)",
  "₪ (Israeli New Shekel)",
  "J\$ (Jamaican Dollar)",
  "¥ (Japanese Yen)",
  "د.ا (Jordanian Dinar)",
  "₸ (Kazakhstani Tenge)",
  "KSh (Kenyan Shilling)",
  "AU\$ (Australian Dollar)",
  "د.ك (Kuwaiti Dinar)",
  "som (Kyrgyzstani Som)",
  "₭ (Laotian Kip)",
  "ل.ل (Lebanese Pound)",
  "L (Lesotho Loti)",
  "L\$ (Liberian Dollar)",
  "ل.د (Libyan Dinar)",
  "CHF (Swiss Franc)",
  "Ar (Malagasy Ariary)",
  "MK (Malawian Kwacha)",
  "RM (Malaysian Ringgit)",
  "Rf (Maldivian Rufiyaa)",
  "Ouguiya (Mauritanian Ouguiya)",
  "Rs (Mauritian Rupee)",
  "Mex\$ (Mexican Peso)",
  "lei (Moldovan Leu)",
  "₮ (Mongolian Tugrik)",
  "د.م. (Moroccan Dirham)",
  "MT (Mozambican Metical)",
  "K (Myanmar Kyat)",
  "N\$ (Namibian Dollar)",
  "रू (Nepalese Rupee)",
  "NZ\$ (New Zealand Dollar)",
  "C\$ (Nicaraguan Córdoba)",
  "₦ (Nigerian Naira)",
  "kr (Norwegian Krone)",
  "﷼ (Omani Rial)",
  "PKR (Pakistani Rupee)",
  "₱ (Philippine Peso)",
  "zł (Polish Złoty)",
  "﷼ (Qatari Riyal)",
  "lei (Romanian Leu)",
  "руб (Russian Ruble)",
  "RF (Rwandan Franc)",
  "₣ (Swiss Franc)",
  "₲ (Paraguayan Guarani)",
  "SR (Saudi Riyal)",
  "د.س (Sudanese Pound)",
  "\$ (Singapore Dollar)",
  "S (Solomon Islands Dollar)",
  "Sh (Somali Shilling)",
  "R (South African Rand)",
  "₩ (South Korean Won)",
  "£ (British Pound)",
  "\$ (Sri Lankan Rupee)",
  "L (Saint Helenian Pound)",
  "\$ (East Caribbean Dollar)",
  "kr (Swedish Krona)",
  "TJS (Tajikistani Somoni)",
  "TSh (Tanzanian Shilling)",
  "฿ (Thai Baht)",
  "T (Tongan Paʻanga)",
  "TTD (Trinidad and Tobago Dollar)",
  "XAf (XAf Central Africa)",
];


List<String> countryList = [
  'English',
  'Spanish',
  'Hindi',
  'Arabic',
  'France',
  'Bengali',
  'Turkish',
  'Chinese',
  'Japanese',
  'Romanian',
  'Germany',
  'Vietnamese',
  'Italian',
  'Thai',
  'Portuguese',
  'Hebrew',
  'Polish',
  'Hungarian',
  'Finland',
  'Korean',
  'Malay',
  'Indonesian',
  'Ukrainian',
  'Bosnian',
  'Greek',
  'Dutch',
  'Urdu',
  'Sinhala',
  'Persian',
  'Serbian',
  'Khmer',
  'Lao',
  'Russian',
  'Kannada',
  'Marathi',
  'Tamil',
  'Afrikaans',
  'Czech',
  'Swedish',
  'Slovak',
  'Swahili',
  'Albanian',
  'Danish',
  'Azerbaijani',
  'Kazakh',
  'Croatian',
  'Nepali'
];
//
// List<String> localeList = [
//   'en',
//   'es',
//   'hi',
//   'ar',
//   'fr',
//   'bn',
//   'tr',
//   'zh',
//   'ja',
//   'ro',
//   'de',
//   'vi',
//   'it',
//   'th',
//   'pt',
//   'he',
//   'pl',
//   'hu',
//   'fi',
//   'ko',
//   'ms',
//   'id',
//   'uk',
//   'bs',
//   'el',
//   'nl',
//   'ur',
//   'si',
//   'fa',
//   'sr',
//   'km',
//   'lo',
//   'ru',
//   'kn',
//   'mr',
//   'ta',
//   'af',
//   'cs',
//   'sv',
//   'sk',
//   'sw',
//   'sq',
//   'da',
//   'az',
//   'kk',
//   'hr',
//   'ne'
// ];