// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a tr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'tr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("SATIŞ EKLE"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORİ"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FATURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS Satış"),
        "PRICE": MessageLookupByLibrary.simpleMessage("FİYAT"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("ÜRÜN ADI"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Giriş Paneli"),
        "QTY": MessageLookupByLibrary.simpleMessage("ADET"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Miktar*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("DURUMU"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("TOPLAM DEĞER"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Kullanıcı Başlığı"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Uygulama Hakkında"),
        "accountName": MessageLookupByLibrary.simpleMessage("Hesap Adı"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Hesap Numarası"),
        "action": MessageLookupByLibrary.simpleMessage("Aksiyon"),
        "add": MessageLookupByLibrary.simpleMessage("Ekle"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Marka Ekle"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Kategori Ekle"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Müşteri Ekle"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Açıklama Ekle...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Belge Ekle"),
        "addItem": MessageLookupByLibrary.simpleMessage("Öğe Ekle"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Ürün Kategorisi Ekle"),
        "addNew": MessageLookupByLibrary.simpleMessage("Yeni Ekle"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Yeni Kullanıcı Ekle"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Ürün Ekle"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Başarıyla Eklendi"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Tedarikçi Ekle"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Birim Ekle"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Gider Listesini Ekle/Güncelle"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Gelir Listesini Ekle/Güncelle"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Kullanıcı Rolü Ekle"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seri Numarası Ekleniyor mu?"),
        "address": MessageLookupByLibrary.simpleMessage("Adres"),
        "all": MessageLookupByLibrary.simpleMessage("Hepsi"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Tüm Temel Özellikler"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Zaten bir hesabınız var mı?"),
        "amount": MessageLookupByLibrary.simpleMessage("Tutar"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android ve iOS Uygulama Desteği"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Bu Teklifi Oluşturmak İstiyor musunuz?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Bu Müşteriyi Silmek İstiyor musunuz?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Bu ürünü silmek istiyor musunuz"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Bu Teklifi Silmek İstiyor musunuz?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Bu satışı iade etmek istiyor musunuz?"),
        "balance": MessageLookupByLibrary.simpleMessage("Bakiye"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Banka Hesap Para Birimi"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Banka Hesapları"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Banka Bilgileri"),
        "bankName": MessageLookupByLibrary.simpleMessage("Banka Adı"),
        "between": MessageLookupByLibrary.simpleMessage("Arasında"),
        "billTo": MessageLookupByLibrary.simpleMessage("Fatura:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Şube Adı"),
        "brand": MessageLookupByLibrary.simpleMessage("Marka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Marka Adı"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("İş Kategorisi"),
        "buy": MessageLookupByLibrary.simpleMessage("Satın Al"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium Plan Satın Al"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMS Satın Al"),
        "calculator": MessageLookupByLibrary.simpleMessage("Hesap Makinesi:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("İptal"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapasite"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Nakit ve Banka"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Nakit Elde"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategoriler"),
        "category": MessageLookupByLibrary.simpleMessage("Kategori"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategori Adı"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Para Üstü"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Değiştirilebilir Tutar"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("Garantiye Bak"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Bir plan seçin"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Toplam Borç >"),
        "color": MessageLookupByLibrary.simpleMessage("Renk"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Şirket Adı"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Şirket Adresi"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Şirket Tanımı"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Şirket E-posta Adresi"),
        "companyName": MessageLookupByLibrary.simpleMessage("Şirket Adı"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Şirket Telefon Numarası"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Şirket Web Sitesi URL\'si"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("Şifre Onayı"),
        "continu": MessageLookupByLibrary.simpleMessage("Devam et"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Satışa Dönüştür"),
        "create": MessageLookupByLibrary.simpleMessage("Oluştur"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Ödeme Oluştur"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Oluşturan"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Yaratıcı Hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Para Birimi"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Geçerli Plan"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Özel Fatura Markalama"),
        "customer": MessageLookupByLibrary.simpleMessage("Müşteri"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Müşteri Borcu"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Müşteri Faturaları"),
        "customerList": MessageLookupByLibrary.simpleMessage("Müşteri Listesi"),
        "customerName": MessageLookupByLibrary.simpleMessage("Müşteri Adı"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Ayın Müşteri"),
        "customerType": MessageLookupByLibrary.simpleMessage("Müşteri Tipi"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Müşteri: Yürüyen Müşteri"),
        "customers": MessageLookupByLibrary.simpleMessage("Müşteriler"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Günlük Tahsilât"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Günlük Satışlar"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Günlük İşlem"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Gösterge Paneli"),
        "date": MessageLookupByLibrary.simpleMessage("Tarih"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Tarih ve Saat"),
        "dealer": MessageLookupByLibrary.simpleMessage("Bayi"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Bayi Fiyatı"),
        "delete": MessageLookupByLibrary.simpleMessage("Sil"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("Taşıma Ücreti"),
        "description": MessageLookupByLibrary.simpleMessage("Açıklama"),
        "details": MessageLookupByLibrary.simpleMessage("Detaylar >"),
        "discount": MessageLookupByLibrary.simpleMessage("İndirim"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("İndirim Fiyatı"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF İndir"),
        "due": MessageLookupByLibrary.simpleMessage("Vadesi Geldi"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Borç Tutarı"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Mevcut tutar burada gösterilecek"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Mevcut Tahsilatı"),
        "dueList": MessageLookupByLibrary.simpleMessage("Mevcut Liste"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Vade Tarihi Dolan İşlemler"),
        "edit": MessageLookupByLibrary.simpleMessage("Düzenle"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Seri Düzenle/Ekle:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Profilinizi Düzenle"),
        "email": MessageLookupByLibrary.simpleMessage("E-posta"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Tutarı Giriniz"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Marka Adını Girin"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Kategori Adını Giriniz"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Şirket Tanımını Girin"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Şirket E-posta Adresinizi Girin"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Şirket Telefon Numaranızı Girin"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Şirket Web Sitesi URL\'nizi Girin"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Müşteri Adını Girin"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Bayi Fiyatını Girin"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("İndirim Fiyatını Girin"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Gider Kategorisi Giriniz"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Gider Tarihini Girin"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Gelir Kategorisi Girin"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Gelir Tarihini Girin"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Üretici Adı Girin"),
        "enterName": MessageLookupByLibrary.simpleMessage("Ad Girin"),
        "enterNames": MessageLookupByLibrary.simpleMessage("İsim Girin"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Not Girin"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Açılış Bakiyesini Girin"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Ödenen tutarı girin"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Şifre Girin"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Ödenecek Tutarı Giriniz"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Fiyatı Girin"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Ürün Kapasitesini Giriniz"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Ürün Kodunu Girin"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Ürün Rengini Giriniz"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Ürün Adını Girin"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Ürün Miktarını Girin"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Ürün Boyutunu Giriniz"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Ürün Tipini Girin"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Ürün Birimini Giriniz"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Ürün Ağırlığını Giriniz"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Alış Fiyatını Girin"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Referans Numarası Girin"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Satış Fiyatını Giriniz"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seri Numarasını Girin"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Mesaj İçeriğini Girin"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Stok Miktarını Girin"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("İşlem Kimliği Girin"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Birim Adını Girin"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Kullanıcı Rol Adı Girin"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Kullanıcı başlığı girin"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Garantinizi Girin"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Toptan Fiyatını Giriniz"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Tutarı Giriniz"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Adresinizi Girin"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Şirket Adresinizi Girin"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Şirket Adınızı Girin"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Şirket Adınızı Girin"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("E-posta Adresinizi Girin"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Şifrenizi Girin"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Şifrenizi Tekrar Girin"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefon Numaranızı Girin"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Mağaza Adınızı Girin"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Kategori Adı Girin"),
        "expense": MessageLookupByLibrary.simpleMessage("Gider"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Gider Tarihi"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Gider Detayları"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Gider İçin"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Gider Kategori Listesi"),
        "expenses": MessageLookupByLibrary.simpleMessage("Giderler"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Ayın en iyi beş satın alınan ürünü"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Sınırsız Kullanım İçin"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Parolanızı mı unuttunuz?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Ücretsiz Veri Yedekleme"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Ücretsiz Ömür Boyu Güncelleme"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Ücretsiz Paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Ücretsiz Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Başla"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Hükümet Kimliği"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Genel Toplam"),
        "hold": MessageLookupByLibrary.simpleMessage("Bekle"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Bekle Numarası"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Kimlik Doğrulama"),
        "inc": MessageLookupByLibrary.simpleMessage("Gelir"),
        "income": MessageLookupByLibrary.simpleMessage("Gelir"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Gelir Kategorisi"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Gelir Kategori Listesi"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Gelir Tarihi"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Gelir Detayları"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Gelir İçin"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Gelir Listesi"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Stoğu Artır"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Anında Gizlilik"),
        "invoice": MessageLookupByLibrary.simpleMessage("Fatura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Fatura:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Fatura NO.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Fatura No."),
        "item": MessageLookupByLibrary.simpleMessage("Öğe"),
        "itemName": MessageLookupByLibrary.simpleMessage("Ürün Adı"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC Doğrulama"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Defteri Kebir Detayları"),
        "ledger": MessageLookupByLibrary.simpleMessage("Defteri Kebir"),
        "left": MessageLookupByLibrary.simpleMessage("Sol"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Kredi Hesapları"),
        "logOut": MessageLookupByLibrary.simpleMessage("Oturumu Kapat"),
        "login": MessageLookupByLibrary.simpleMessage("Oturum aç"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Faturada Logo Pozisyonu?"),
        "loss": MessageLookupByLibrary.simpleMessage("Zarar"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Kâr/Zarar"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Kayıp(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Düşük Stok"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Düşük Stoklar"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Markalı faturalarınızla müşterileriniz üzerinde kalıcı bir izlenim bırakın. Sınırsız Yükseltme, faturalarınızı özelleştirmenin benzersiz avantajını sunar ve marka kimliğinizi güçlendiren ve müşteri sadakati oluşturan profesyonel bir dokunuş ekler."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Üretici"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Giriş Paneli"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Kayıt Paneli"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobil Uygulama\n+\nMasaüstü"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Para Makbuzu"),
        "nam": MessageLookupByLibrary.simpleMessage("Ad*"),
        "name": MessageLookupByLibrary.simpleMessage("Ad"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Ad, Kod veya Kategori"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Yeni Müşteriler"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Yeni Müşteriler"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Yeni Gelir"),
        "no": MessageLookupByLibrary.simpleMessage("Hayır"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Bağlantı Yok"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Müşteri Bulunamadı"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("Borç İşlemi Bulunamadı"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("Gider Kategorisi Bulunamadı"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("Gelir Kategorisi Bulunamadı"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Gelir Bulunamadı"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Fatura Bulunamadı"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Ürün Bulunamadı"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Satın alma işlemi bulunamadı"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Teklif Bulunamadı"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Rapor Bulunamadı"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("Satış İşlemi Bulunamadı"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Seri Numarası Bulunamadı"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Tedarikçi Bulunamadı"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("İşlem Bulunamadı"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Kullanıcı Bulunamadı"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Kullanıcı Rolü Bulunamadı"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Seri Numarası Bulunamadı"),
        "note": MessageLookupByLibrary.simpleMessage("Not"),
        "ok": MessageLookupByLibrary.simpleMessage("Tamam"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Açık Çekler"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Açılış Bakiyesi"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "veya PNG, JPG sürükleyip bırakın"),
        "orders": MessageLookupByLibrary.simpleMessage("Siparişler"),
        "other": MessageLookupByLibrary.simpleMessage("Diğer"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Diğer Gelir"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Paket Özellikleri"),
        "paid": MessageLookupByLibrary.simpleMessage("Ödendi"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Ödenen Tutar"),
        "partyName": MessageLookupByLibrary.simpleMessage("Parti Adı"),
        "partyType": MessageLookupByLibrary.simpleMessage("Parti Tipi"),
        "password": MessageLookupByLibrary.simpleMessage("Şifre"),
        "payCash": MessageLookupByLibrary.simpleMessage("Nakit Ödeme"),
        "payable": MessageLookupByLibrary.simpleMessage("Ödenecek"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Ödenen Tutar"),
        "payment": MessageLookupByLibrary.simpleMessage("Ödeme"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Ödeme Girişi"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Ödeme Çıkışı"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Ödeme Türü"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Ödeme Türü"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefon Numarası"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefon Doğrulama"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Lütfen bir satış ekleyin"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Lütfen Müşteri Ekleyin"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Lütfen İnternet Bağlantınızı Kontrol Edin"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Lütfen mobil uygulamamızı indirin ve masaüstü sürümünü kullanmak için bir pakete abone olun"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Lütfen ürün stokunu girin"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Lütfen geçerli veri girin"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Lütfen bir müşteri seçiniz"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Lütfen geçerli veri girin"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Kayıt Paneli"),
        "practies": MessageLookupByLibrary.simpleMessage("Pratikler"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Müşteri Desteği"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Önizleme"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Önceki Vadesi:"),
        "price": MessageLookupByLibrary.simpleMessage("Fiyat"),
        "print": MessageLookupByLibrary.simpleMessage("Yazdır"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Fatura Yazdır"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF Yazdır"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Gizlilik Politikası"),
        "product": MessageLookupByLibrary.simpleMessage("Ürün"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Ürün Kategorisi"),
        "productCod": MessageLookupByLibrary.simpleMessage("Ürün Kodu*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Ürün Rengi"),
        "productList": MessageLookupByLibrary.simpleMessage("Ürün Listesi"),
        "productNam": MessageLookupByLibrary.simpleMessage("Ürün Adı*"),
        "productName": MessageLookupByLibrary.simpleMessage("Ürün Adı"),
        "productSize": MessageLookupByLibrary.simpleMessage("Ürün Boyutu"),
        "productStock": MessageLookupByLibrary.simpleMessage("Ürün Stoğu"),
        "productType": MessageLookupByLibrary.simpleMessage("Ürün Tipi"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Ürün Birimi"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Ürün Garantisi"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Ürün Ağırlığı"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Ürün Kapasitesi"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Profil Düzenle"),
        "profit": MessageLookupByLibrary.simpleMessage("Kar"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Kar(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Kar(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Alış"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Alış Listesi"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium Plan Satın Al"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Alış Fiyatı"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Satın Alma İşlemi"),
        "quantity": MessageLookupByLibrary.simpleMessage("Miktar"),
        "quotation": MessageLookupByLibrary.simpleMessage("Teklif"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Teklif Listesi"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Son Satışlar"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Alınan Tutar"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referans No."),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referans Numarası"),
        "registration": MessageLookupByLibrary.simpleMessage("Kayıt"),
        "remaining": MessageLookupByLibrary.simpleMessage("Kalan: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Kalan Bakiye"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Kalan Borç"),
        "reports": MessageLookupByLibrary.simpleMessage("Raporlar"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Şifrenizi Sıfırlayın"),
        "retailer": MessageLookupByLibrary.simpleMessage("Perakendeci"),
        "revenue": MessageLookupByLibrary.simpleMessage("Gelir"),
        "right": MessageLookupByLibrary.simpleMessage("Sağ"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Satış Tutarı"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "İşletmenizin verilerini kolayca koruyun. Pos Saas POS Unlimited Yükseltmesi, değerli bilgilerinizin beklenmedik olaylara karşı korunduğunu ücretsiz veri yedeklemeyi içerir. Gerçekten önemli olan şeye odaklanın - işletme büyümenize."),
        "sale": MessageLookupByLibrary.simpleMessage("Satış"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Satış Tutarı"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Satış Detayları"),
        "saleList": MessageLookupByLibrary.simpleMessage("Satış Listesi"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Satış Fiyatı"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Satış Fiyatı*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Satış İadesi"),
        "saleTransaction": MessageLookupByLibrary.simpleMessage("Satış İşlemi"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Satış İşlemleri (Teklif Satış Geçmişi)"),
        "sales": MessageLookupByLibrary.simpleMessage("Satışlar"),
        "salesList": MessageLookupByLibrary.simpleMessage("Satış Listesi"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Kaydet ve Yayınla"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Kaydet ve Yayınla"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Değişiklikleri Kaydet"),
        "search": MessageLookupByLibrary.simpleMessage("Ara...."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Herhangi Bir Şeyi Ara..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Fatura ile Ara...."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Fatura veya isim ile ara"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Adına Göre Ara"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("Ad veya Telefonla Ara..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seri Numarasını Ara"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Tarafları Seç"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Ürün Markasını Seç"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seri Numarasını Seç"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Varyasyonları Seç:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Garanti Süresini Seçin"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Dilinizi Seçin"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Mesaj Gönder"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Seri Numarası"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Seri Numarası"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Hizmet Ücreti"),
        "setting": MessageLookupByLibrary.simpleMessage("Ayarlar"),
        "share": MessageLookupByLibrary.simpleMessage("Paylaş"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Nakliye/Diğer"),
        "shopName": MessageLookupByLibrary.simpleMessage("Mağaza Adı"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Mağaza Açılış Bakiyesi"),
        "show": MessageLookupByLibrary.simpleMessage("Göster >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Faturada Logo Gösterilsin mi?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Nakliye/Hizmet"),
        "size": MessageLookupByLibrary.simpleMessage("Boyut"),
        "statistic": MessageLookupByLibrary.simpleMessage("İstatistik"),
        "status": MessageLookupByLibrary.simpleMessage("Durum"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Herhangi bir ek maliyet olmadan teknolojik ilerlemenin önünde kalın. Pos Saas POS Sınırsız Yükseltme işletmenizin her zaman en son araçlara ve özelliklere sahip olduğunu garanti eder."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Herhangi bir ek maliyet olmadan teknolojik ilerlemenin önünde kalın. Pos Sass POS Sınırsız Yükseltme, işletmenizin her zaman en son araçlara ve özelliklere sahip olduğunu garanti eder."),
        "stock": MessageLookupByLibrary.simpleMessage("Stok"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("Stok Envanter"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Stok Raporu"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Stok Değeri"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Stok Değerleri"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Ara Toplam"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abonelik"),
        "submit": MessageLookupByLibrary.simpleMessage("Gönder"),
        "supplier": MessageLookupByLibrary.simpleMessage("Tedarikçiler"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Tedarikçi Borcu"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Tedarikçi Faturası"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Tedarikçi Listesi"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT Kodu"),
        "tSale": MessageLookupByLibrary.simpleMessage("Toplam Satış"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Sürücü ehliyeti, ulusal kimlik kartı veya pasaport fotoğrafı çekin"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Kullanım Koşulları"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "İsim her şeyi söylüyor. Pos Saas POS Unlimited ile kullanımınızda bir sınırlama yok. Birkaç işlemi işliyor olsanız da veya müşterilerin akınına uğruyorsanız, sınırlamalar tarafından kısıtlanmadığınızı bilerek güvenle işlem yapabilirsiniz."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("Bu müşterinin borcu yok"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Bu müşterinin önceki borcu var"),
        "to": MessageLookupByLibrary.simpleMessage("İçine"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("En Çok Satan Ürün"),
        "total": MessageLookupByLibrary.simpleMessage("toplam"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Toplam Tutar"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Toplam İndirim"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Toplam Borç"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Toplam Borç"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Toplam Gider"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Toplam Gelir"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Toplam Öğe: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Toplam Zarar"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Toplam Ödenen"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Toplam Ödenecek"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Toplam Ödeme Çıkışı"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Toplam Fiyat"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Toplam Ürün"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Toplam Kar"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Toplam Alış"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Toplam İade Tutarı"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Toplam İadeler"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Toplam Satış"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Toplam Satış"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Toplam KDV"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Toplam Ödeme Girişi"),
        "transaction": MessageLookupByLibrary.simpleMessage("İşlem"),
        "transactionId": MessageLookupByLibrary.simpleMessage("İşlem Kimliği"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("İşlem Raporu"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Tekrar Deneyin"),
        "type": MessageLookupByLibrary.simpleMessage("Tip"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Ödenmemiş"),
        "unit": MessageLookupByLibrary.simpleMessage("Birim"),
        "unitName": MessageLookupByLibrary.simpleMessage("Birim Adı"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Birim Fiyat"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Sınırsız"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Sınırsız Fatura"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Sınırsız Kullanım"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Pos Saas POS\'un tam potansiyelini uzman ekibimizin liderliğindeki kişiselleştirilmiş eğitim oturumları ile açın. Temellerden ileri tekniklere kadar her yönünü optimize etmek için iyi bir şekilde bilgilendiğinizden emin oluyoruz."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Şimdi Güncelle"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Planınızı önce güncelleyin\\nSatış Limiti aşıldı."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Mobil Uygulamada Yükseltme"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Bir Resim Yükle"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Fatura Logosu Yükle"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("Belge Yükle"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Dosya Yükle"),
        "userName": MessageLookupByLibrary.simpleMessage("Kullanıcı Adı"),
        "userRole": MessageLookupByLibrary.simpleMessage("Kullanıcı Rolü"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Kullanıcı Rol Adı"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Kullanıcı Başlığı"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("KDV/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefon Numarasını Doğrula"),
        "view": MessageLookupByLibrary.simpleMessage("Görünüm"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Yürüyüş Müşteri"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garanti"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garanti"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Başlamadan önce telefonunuzu kaydetmemiz gerekiyor!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Sorunsuz işlemlerin önemini anlıyoruz. Bu nedenle gece gündüz destek ekibimiz, hızlı bir soru veya kapsamlı bir endişe olup olmadığına bakılmaksızın size yardımcı olmaya hazır. Olağanüstü müşteri hizmeti deneyimi yaşamak için bize her zaman her yerden çağrı veya WhatsApp aracılığıyla ulaşın."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Toptan Satış Fiyatı"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Toptan Satıcı"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Toptan"),
        "wight": MessageLookupByLibrary.simpleMessage("Ağırlık"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Evet İade"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Hesabınıza TEKRAR GİRİŞ yapmalısınız."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Mesaj satın almadan önce kimlik doğrulama yapmanız gerekir"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Tüm satış listeniz"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Tüm Satışlarınız"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Kullanıyorsunuz"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Mevcut Satışlarınız"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Mesaj satın almadan önce kimlik doğrulama yapmanız gerekir"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Paketiniz"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Ödemeniz iptal edildi"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Ödemeniz başarıyla gerçekleşti"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Ödemeniz iptal edildi")
      };
}
