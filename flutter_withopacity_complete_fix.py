#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت شامل لحل مشكلة withOpacity في مشروع Flutter
يستبدل جميع استخدامات withOpacity بالطرق الحديثة لتجنب التحذيرات

الميزات:
- إصلاح الألوان الثابتة باستخدام Color.fromRGBO
- إصلاح الألوان الديناميكية باستخدام withValues
- إنشاء نسخ احتياطية
- تقارير مفصلة
- التحقق من النتائج

المؤلف: مساعد الذكي
التاريخ: 2025-07-15
"""

import os
import re
import sys
from pathlib import Path
import argparse
from datetime import datetime

class FlutterWithOpacityFixer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.changes_made = 0
        self.files_processed = 0
        self.detailed_changes = []
        
        # قاموس الألوان المعرفة مسبقاً في المشروع
        self.predefined_colors = {
            'kMainColor': '0xff8424FF',
            'kDarkGreyColor': '0xFF2E2E3E',
            'kLitGreyColor': '0xFFD4D4D8',
            'kGreyTextColor': '0xFF585865',
            'kChartColor': '0xff2E2E3E',
            'kBorderColorTextField': '0xFFE8E7E5',
            'kDarkWhite': '0xFFF2F6F8',
            'kbgColor': '0xFFF8F3FF',
            'kWhite': '0xFFFFFFFF',
            'kRedTextColor': '0xFFFE2525',
            'kBlueTextColor': '0xff8424FF',
            'kYellowColor': '0xFFFF8C00',
            'kGreenTextColor': '0xff8424FF',
            'kTitleColor': '0xFF2E2E3E',
            'kPremiumPlanColor': '0xFF8752EE',
            'kPremiumPlanColor2': '0xFFFF5F00',
            'lightGreyColor': '0xFFF8F3FF',
            'dropdownItemColor': '0xFFF2F6F8',
        }
        
        # ألوان Flutter المدمجة
        self.flutter_colors = {
            'Colors.white': '0xFFFFFFFF',
            'Colors.black': '0xFF000000',
            'Colors.red': '0xFFF44336',
            'Colors.green': '0xFF4CAF50',
            'Colors.blue': '0xFF2196F3',
            'Colors.yellow': '0xFFFFEB3B',
            'Colors.orange': '0xFFFF9800',
            'Colors.purple': '0xFF9C27B0',
            'Colors.grey': '0xFF9E9E9E',
            'Colors.redAccent': '0xFFFF5252',
            'Colors.greenAccent': '0xFF69F0AE',
            'Colors.blueAccent': '0xFF448AFF',
            'Colors.transparent': '0x00000000',
            'Colors.white70': '0xB3FFFFFF',
            'Colors.white60': '0x99FFFFFF',
            'Colors.white54': '0x8AFFFFFF',
            'Colors.white38': '0x62FFFFFF',
            'Colors.white30': '0x4DFFFFFF',
            'Colors.white24': '0x3DFFFFFF',
            'Colors.white12': '0x1FFFFFFF',
            'Colors.white10': '0x1AFFFFFF',
            'Colors.black87': '0xDD000000',
            'Colors.black54': '0x8A000000',
            'Colors.black45': '0x73000000',
            'Colors.black38': '0x61000000',
            'Colors.black26': '0x42000000',
            'Colors.black12': '0x1F000000',
        }
        
    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")
        
        for file_path in self.project_path.rglob("*.dart"):
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")
        
    def hex_to_rgba(self, hex_color, opacity):
        """تحويل اللون من hex إلى RGBA"""
        hex_color = hex_color.replace('0x', '').replace('0X', '').replace('#', '')
        
        if len(hex_color) == 8:
            # ARGB format
            a = int(hex_color[0:2], 16)
            r = int(hex_color[2:4], 16)
            g = int(hex_color[4:6], 16)
            b = int(hex_color[6:8], 16)
        elif len(hex_color) == 6:
            # RGB format
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            a = 255
        else:
            return None
            
        return f"Color.fromRGBO({r}, {g}, {b}, {opacity})"
        
    def get_color_value(self, color_name):
        """الحصول على قيمة اللون من الاسم"""
        if color_name in self.predefined_colors:
            return self.predefined_colors[color_name]
        return self.flutter_colors.get(color_name)
        
    def fix_withopacity_in_content(self, content):
        """إصلاح withOpacity في محتوى الملف"""
        changes = 0
        examples = []
        
        # أنماط مختلفة للبحث عن withOpacity
        patterns = [
            # نمط للألوان المباشرة: Color(0xFF...).withOpacity(value)
            (r'(Color\(0x[A-Fa-f0-9]+\))\.withOpacity\(([\d.]+)\)', 'direct_color'),
            # نمط للألوان مع const: const Color(0xFF...).withOpacity(value)
            (r'(const\s+Color\(0x[A-Fa-f0-9]+\))\.withOpacity\(([\d.]+)\)', 'const_color'),
            # نمط للألوان المعرفة مسبقاً
            (r'(k\w+Color|Colors\.\w+)\.withOpacity\(([\d.]+)\)', 'predefined_color'),
        ]
        
        def replace_withopacity(match, pattern_type):
            nonlocal changes, examples
            color_part = match.group(1)
            opacity = float(match.group(2))
            
            original = match.group(0)
            
            if pattern_type in ['direct_color', 'const_color']:
                clean_color_part = color_part.replace('const ', '')
                hex_match = re.search(r'Color\(0x([A-Fa-f0-9]+)\)', clean_color_part)
                if hex_match:
                    hex_value = '0x' + hex_match.group(1)
                    replacement = self.hex_to_rgba(hex_value, opacity)
                    if replacement:
                        if color_part.startswith('const'):
                            replacement = f"const {replacement}"
                        changes += 1
                        examples.append(f"{original} → {replacement}")
                        return replacement
                        
            elif pattern_type == 'predefined_color':
                hex_value = self.get_color_value(color_part)
                if hex_value:
                    replacement = self.hex_to_rgba(hex_value, opacity)
                    if replacement:
                        changes += 1
                        examples.append(f"{original} → {replacement}")
                        return replacement
                        
            return original
        
        # تطبيق جميع الأنماط
        new_content = content
        for pattern, pattern_type in patterns:
            new_content = re.sub(pattern, lambda m: replace_withopacity(m, pattern_type), new_content)
            
        return new_content, changes, examples
        
    def process_file(self, file_path):
        """معالجة ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            new_content, file_changes, examples = self.fix_withopacity_in_content(content)
            
            if file_changes > 0:
                # إنشاء نسخة احتياطية
                backup_path = str(file_path) + '.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                # كتابة المحتوى الجديد
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                    
                print(f"✅ {file_path.name}: تم إصلاح {file_changes} استخدام")
                self.changes_made += file_changes
                
                # حفظ تفاصيل التغييرات
                self.detailed_changes.append({
                    'file': file_path.name,
                    'count': file_changes,
                    'examples': examples
                })
                
            self.files_processed += 1
            
        except Exception as e:
            print(f"❌ خطأ في معالجة {file_path}: {e}")
            
    def verify_fix(self):
        """التحقق من إصلاح المشكلة"""
        print("\n🔍 التحقق من النتائج...")
        remaining_withopacity = 0
        color_fromrgbo_count = 0
        
        for file_path in self.dart_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                remaining_withopacity += len(re.findall(r'\.withOpacity\(', content))
                color_fromrgbo_count += len(re.findall(r'Color\.fromRGBO\(', content))
                
            except Exception:
                continue
                
        print(f"📊 نتائج التحقق:")
        print(f"   - استخدامات Color.fromRGBO: {color_fromrgbo_count}")
        print(f"   - استخدامات withOpacity المتبقية: {remaining_withopacity}")
        
        if remaining_withopacity == 0:
            print(f"🎉 ممتاز! تم إصلاح جميع مشاكل withOpacity!")
        else:
            print(f"⚠️  يوجد {remaining_withopacity} استخدام withOpacity متبقي (قد تكون ديناميكية)")
            
        return remaining_withopacity == 0
        
    def generate_report(self):
        """إنشاء تقرير مفصل"""
        report_path = self.project_path / "flutter_withopacity_fix_report.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("تقرير إصلاح مشكلة withOpacity في Flutter\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"الملفات المعالجة: {self.files_processed}\n")
            f.write(f"إجمالي التغييرات: {self.changes_made}\n\n")
            
            if self.detailed_changes:
                f.write("تفاصيل التغييرات:\n")
                f.write("-" * 30 + "\n")
                for change in self.detailed_changes:
                    f.write(f"\nالملف: {change['file']}\n")
                    f.write(f"عدد التغييرات: {change['count']}\n")
                    f.write("أمثلة على التغييرات:\n")
                    for example in change['examples'][:3]:  # أول 3 أمثلة
                        f.write(f"  {example}\n")
                        
        print(f"📄 تم إنشاء تقرير مفصل: {report_path}")
        
    def run(self):
        """تشغيل السكربت الشامل"""
        print("🚀 بدء إصلاح مشكلة withOpacity في Flutter...")
        print("=" * 60)
        
        self.find_dart_files()
        
        if not self.dart_files:
            print("❌ لم يتم العثور على ملفات Dart")
            return False
            
        print(f"📝 معالجة {len(self.dart_files)} ملف...")
        print("-" * 40)
        
        for file_path in self.dart_files:
            self.process_file(file_path)
            
        print("-" * 40)
        print(f"✅ تم الانتهاء من المعالجة!")
        print(f"📊 الإحصائيات:")
        print(f"   - الملفات المعالجة: {self.files_processed}")
        print(f"   - التغييرات المطبقة: {self.changes_made}")
        
        # التحقق من النتائج
        success = self.verify_fix()
        
        if self.changes_made > 0:
            self.generate_report()
            print(f"\n💾 تم إنشاء نسخ احتياطية بامتداد .backup")
            print(f"🔄 لاستعادة الملفات الأصلية، استخدم:")
            print(f"   python {sys.argv[0]} --restore")
        else:
            print(f"\n🎉 لا توجد مشاكل withOpacity في المشروع!")
            
        return success

def restore_backups(project_path):
    """استعادة الملفات من النسخ الاحتياطية"""
    project_path = Path(project_path)
    backup_files = list(project_path.rglob("*.dart.backup"))
    
    if not backup_files:
        print("❌ لم يتم العثور على نسخ احتياطية")
        return
        
    print(f"🔄 استعادة {len(backup_files)} ملف...")
    
    for backup_file in backup_files:
        original_file = backup_file.with_suffix('')
        try:
            backup_file.replace(original_file)
            print(f"✅ تم استعادة {original_file.name}")
        except Exception as e:
            print(f"❌ خطأ في استعادة {original_file.name}: {e}")
            
    print("✅ تم الانتهاء من الاستعادة")

def main():
    parser = argparse.ArgumentParser(
        description='إصلاح شامل لمشكلة withOpacity في مشروع Flutter',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python flutter_withopacity_complete_fix.py                    # إصلاح المشروع الحالي
  python flutter_withopacity_complete_fix.py --path /path/to/project  # إصلاح مشروع محدد
  python flutter_withopacity_complete_fix.py --restore          # استعادة النسخ الاحتياطية
        """
    )
    parser.add_argument('--path', default='.', help='مسار المشروع (افتراضي: المجلد الحالي)')
    parser.add_argument('--restore', action='store_true', help='استعادة الملفات من النسخ الاحتياطية')
    
    args = parser.parse_args()
    
    if args.restore:
        restore_backups(args.path)
    else:
        fixer = FlutterWithOpacityFixer(args.path)
        success = fixer.run()
        
        if success:
            print(f"\n🎊 تم إصلاح جميع مشاكل withOpacity بنجاح!")
            print(f"🔧 يمكنك الآن تشغيل المشروع بدون تحذيرات withOpacity")
        else:
            print(f"\n⚠️  قد تحتاج إلى مراجعة يدوية لبعض الحالات الخاصة")

if __name__ == "__main__":
    main()
