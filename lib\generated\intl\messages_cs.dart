// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a cs locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'cs';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("PŘIDAT PRODEJ"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIE"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale":
            MessageLookupByLibrary.simpleMessage("Prodej na pokladně (POS)"),
        "PRICE": MessageLookupByLibrary.simpleMessage("CENA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NÁZEV PRODUKTU"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Přihlašovací panel Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("MNOŽSTVÍ"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STAV"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("CELKOVÁ HODNOTA"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Titul uživatele"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("O aplikaci"),
        "accountName": MessageLookupByLibrary.simpleMessage("Název účtu"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Číslo účtu"),
        "action": MessageLookupByLibrary.simpleMessage("Akce"),
        "add": MessageLookupByLibrary.simpleMessage("Přidat"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Přidat značku"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Přidat kategorii"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Přidat zákazníka"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Přidat popis..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Přidat dokumenty"),
        "addItem": MessageLookupByLibrary.simpleMessage("Přidat položku"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Přidat kategorii položky"),
        "addNew": MessageLookupByLibrary.simpleMessage("Přidat nový"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Přidat nového uživatele"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Přidat produkt"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Přidání úspěšné"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Přidat dodavatele"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Přidat jednotku"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Přidat/Aktualizovat seznam výdajů"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Přidat / Aktualizovat seznam příjmů"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Přidat roli uživatele"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Přidání sériového čísla?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresa"),
        "all": MessageLookupByLibrary.simpleMessage("Vše"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Všechny základní funkce"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Máte již účet?"),
        "amount": MessageLookupByLibrary.simpleMessage("Částka"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Podpora aplikace pro Android a iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Chcete vytvořit tuto nabídku?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Opravdu chcete smazat tohoto zákazníka?"),
        "areYouWantToDeleteThisProduct":
            MessageLookupByLibrary.simpleMessage("Chcete tento produkt smazat"),
        "areYouWantToDeleteThisQuotion":
            MessageLookupByLibrary.simpleMessage("Chcete smazat tuto nabídku?"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("Chcete tento prodej vrátit?"),
        "balance": MessageLookupByLibrary.simpleMessage("Zůstatek"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Měna bankovního účtu"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankovní účty"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankovní informace"),
        "bankName": MessageLookupByLibrary.simpleMessage("Název banky"),
        "between": MessageLookupByLibrary.simpleMessage("Mezi"),
        "billTo": MessageLookupByLibrary.simpleMessage("Fakturováno:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Název pobočky"),
        "brand": MessageLookupByLibrary.simpleMessage("Značka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Název značky"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Kategorie podnikání"),
        "buy": MessageLookupByLibrary.simpleMessage("Koupit"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Koupit prémiový balíček"),
        "buySms": MessageLookupByLibrary.simpleMessage("Koupit SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulačka:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Zrušit"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacita"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Hotovost a banka"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Hotovost v ruce"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorie"),
        "category": MessageLookupByLibrary.simpleMessage("Kategorie"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Název kategorie"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Změnit částku"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Změnitelná částka"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Zkontrolovat záruku"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Vyberte si plán"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Vybrat dluh >"),
        "color": MessageLookupByLibrary.simpleMessage("Barva"),
        "comapnyName":
            MessageLookupByLibrary.simpleMessage("Název společnosti"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Adresa společnosti"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Popis společnosti"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Emailová adresa společnosti"),
        "companyName":
            MessageLookupByLibrary.simpleMessage("Název společnosti"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefonní číslo společnosti"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "URL adresa webové stránky společnosti"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Potvrďte heslo"),
        "continu": MessageLookupByLibrary.simpleMessage("Pokračovat"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Převést na prodej"),
        "create": MessageLookupByLibrary.simpleMessage("Vytvořit"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Vytvořit platbu"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Vytvořil"),
        "creativeHub":
            MessageLookupByLibrary.simpleMessage("Kreativní centrum"),
        "currency": MessageLookupByLibrary.simpleMessage("Měna"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Aktuální plán"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Přizpůsobení faktur"),
        "customer": MessageLookupByLibrary.simpleMessage("Klient"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Dluh zákazníka"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Faktury zákazníků"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Seznam zákazníků"),
        "customerName": MessageLookupByLibrary.simpleMessage("Jméno zákazníka"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Zákazník měsíce"),
        "customerType": MessageLookupByLibrary.simpleMessage("Typ zákazníka"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "Klient: Klient bez objednávky"),
        "customers": MessageLookupByLibrary.simpleMessage("Zákazníci"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("Denní výběr"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Denní prodeje"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Denní transakce"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Přehled"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum a čas"),
        "dealer": MessageLookupByLibrary.simpleMessage("Dealer"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Cena pro dealery"),
        "delete": MessageLookupByLibrary.simpleMessage("Smazat"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Poplatek za doručení"),
        "description": MessageLookupByLibrary.simpleMessage("Popis"),
        "details": MessageLookupByLibrary.simpleMessage("Podrobnosti >"),
        "discount": MessageLookupByLibrary.simpleMessage("Sleva"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Slevová cena"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Stáhnout PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Dluh"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Částka dluhu"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Splatná částka se zobrazí zde, pokud je k dispozici"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Sběr splatných částek"),
        "dueList":
            MessageLookupByLibrary.simpleMessage("Seznam splatných částek"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Dlužné transakce"),
        "edit": MessageLookupByLibrary.simpleMessage("Upravit"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Upravit/Přidat sériové číslo:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Upravit svůj profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Zadejte částku"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Zadejte název značky"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Zadejte název kategorie"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Zadejte popis společnosti"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Zadejte emailovou adresu společnosti"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Zadejte telefonní číslo společnosti"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Zadejte URL adresu webové stránky společnosti"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Zadejte jméno zákazníka"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Zadejte slevovou cenu"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Zadejte kategorii výdajů"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Zadejte datum výdaje"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Zadejte kategorii příjmu"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Zadejte datum příjmu"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Zadejte název výrobce"),
        "enterName": MessageLookupByLibrary.simpleMessage("Zadejte název"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Zadejte jméno"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Zadejte poznámku"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Zadejte počáteční zůstatek"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Zadejte zaplacenou částku"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Zadejte heslo"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Zadejte částku k úhradě"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Zadejte cenu"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Zadejte kapacitu produktu"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Zadejte kód produktu"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Zadejte barvu produktu"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Zadejte název produktu"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Zadejte množství produktu"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Zadejte velikost produktu"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Zadejte typ produktu"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Zadejte jednotku produktu"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Zadejte váhu produktu"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Zadejte nákupní cenu"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Zadejte referenční číslo"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Zadejte prodejní cenu"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Zadejte sériové číslo"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Zadejte obsah zprávy"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Zadejte množství na skladě"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Zadejte ID transakce"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Zadejte název jednotky"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Zadejte název role uživatele"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Zadejte titul uživatele"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Zadejte záruku"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Zadejte velkoobchodní cenu"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Zadejte částku"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Zadejte vaši adresu"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Zadejte adresu vaší společnosti"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Zadejte název vaší společnosti"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Zadejte název vaší společnosti"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Zadejte svou emailovou adresu"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Zadejte vaše heslo"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Zadejte vaše heslo znovu"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Zadejte své telefonní číslo"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Zadejte název vašeho obchodu"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Zadejte název kategorie"),
        "expense": MessageLookupByLibrary.simpleMessage("Výdaje"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Datum výdaje"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detaily výdajů"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Výdaj pro"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Seznam kategorií výdajů"),
        "expenses": MessageLookupByLibrary.simpleMessage("Výdaje"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top pět nejprodávanějších produktů měsíce"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Pro neomezené použití"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Zapomenuté heslo?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Zálohování dat zdarma"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Zdarma celoživotní aktualizace"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Zdarma"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Zdarma"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Začít"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Identifikační doklad"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Celková suma"),
        "hold": MessageLookupByLibrary.simpleMessage("Držet"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Číslo držení"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Ověření identity"),
        "inc": MessageLookupByLibrary.simpleMessage("Příjem"),
        "income": MessageLookupByLibrary.simpleMessage("Příjem"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategorie příjmů"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Seznam kategorií příjmů"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Datum příjmu"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Podrobnosti o příjmech"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Příjem pro"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Seznam příjmů"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Zvýšit zásoby"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Okamžité soukromí"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Faktura:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Číslo faktury..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Číslo faktury"),
        "item": MessageLookupByLibrary.simpleMessage("Položka"),
        "itemName": MessageLookupByLibrary.simpleMessage("Název položky"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Verifikace KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Podrobnosti účetnictví"),
        "ledger": MessageLookupByLibrary.simpleMessage("Účetnictví"),
        "left": MessageLookupByLibrary.simpleMessage("Vlevo"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Půjčky a úvěry"),
        "logOut": MessageLookupByLibrary.simpleMessage("Odhlásit se"),
        "login": MessageLookupByLibrary.simpleMessage("Přihlásit se"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Pozice loga na faktuře?"),
        "loss": MessageLookupByLibrary.simpleMessage("Ztráta"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Ztráta/Zisk"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Ztráta(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Nízký Sklad"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Nízké zásoby"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Zanechte trvalý dojem na své zákazníky s vlastními fakturami. Naše neomezené upgrady nabízejí jedinečnou výhodu přizpůsobení faktur, přidávajíc profesionální dotek, který posiluje identitu vaší značky a podporuje věrnost zákazníků."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Výrobce"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Přihlašovací panel Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Registrační panel Pos Saas"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobilní aplikace\n+\nPlocha"),
        "nam": MessageLookupByLibrary.simpleMessage("Název*"),
        "name": MessageLookupByLibrary.simpleMessage("Název"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Název, kód nebo kategorie"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Noví zákazníci"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Noví zákazníci"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nový příjem"),
        "no": MessageLookupByLibrary.simpleMessage("Ne"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Žádné spojení"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Žádný zákazník nenalezen"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nebyla nalezena žádná transakce s dluhem"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nenalezena žádná kategorie výdajů"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Žádná kategorie příjmů nenalezena"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Žádný příjem nenalezen"),
        "noInvoiceFound": MessageLookupByLibrary.simpleMessage(
            "Nebyla nalezena žádná faktura"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Produkt nenalezen"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nenalezena žádná transakce nákupu"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nenalezena žádná nabídka"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Žádná zpráva nenalezena"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nebyla nalezena žádná prodejní transakce"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Sériové číslo nenalezeno"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "Nebyl nalezen žádný dodavatel"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nebyla nalezena žádná transakce"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Nenalezen žádný uživatel"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Nebyla nalezena žádná role uživatele"),
        "note": MessageLookupByLibrary.simpleMessage("Poznámka"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Otevřené šeky"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Počáteční zůstatek"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("nebo přetáhněte PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Objednávky"),
        "other": MessageLookupByLibrary.simpleMessage("Ostatní"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Ostatní příjmy"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Funkce balíčku"),
        "paid": MessageLookupByLibrary.simpleMessage("Zaplaceno"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Zaplacená částka"),
        "partyName": MessageLookupByLibrary.simpleMessage("Název strany"),
        "partyType": MessageLookupByLibrary.simpleMessage("Typ strany"),
        "password": MessageLookupByLibrary.simpleMessage("Heslo"),
        "payCash": MessageLookupByLibrary.simpleMessage("Zaplacení hotově"),
        "payable": MessageLookupByLibrary.simpleMessage("K platbě"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Placená částka"),
        "payment": MessageLookupByLibrary.simpleMessage("Platba"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Platba přijata"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Platba vyplacena"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Typ platby"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Typ platby"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefonní číslo"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Ověření telefonu"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Prosím, přidejte prodej"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Prosím přidejte zákazníka"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Zkontrolujte své internetové připojení"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Stáhněte si prosím naši mobilní aplikaci a přihlaste se k odběru balíčku pro použití desktopové verze"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Zadejte stav zásob"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Prosím, zadejte platná data"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Prosím, vyberte si zákazníka"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Zadejte platná data, prosím"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Registrace do Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Praxe"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Prémiová zákaznická podpora"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Prémiový balíček"),
        "preview": MessageLookupByLibrary.simpleMessage("Náhled"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Předchozí dluh:"),
        "price": MessageLookupByLibrary.simpleMessage("Cena"),
        "print": MessageLookupByLibrary.simpleMessage("Tisk"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Tisknout fakturu"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Tisknout PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage(
            "Zásady ochrany osobních údajů"),
        "product": MessageLookupByLibrary.simpleMessage("Produkt"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategorie produktu"),
        "productColor": MessageLookupByLibrary.simpleMessage("Barva produktu"),
        "productList": MessageLookupByLibrary.simpleMessage("Seznam produktů"),
        "productName": MessageLookupByLibrary.simpleMessage("Název produktu"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Velikost produktu"),
        "productStock": MessageLookupByLibrary.simpleMessage("Stav zásob"),
        "productType": MessageLookupByLibrary.simpleMessage("Typ produktu"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Jednotka produktu"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Váha produktu"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapacita produktu"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Upravit profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Zisk"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Zisk(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Zisk(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Nákup"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Seznam nákupů"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Koupit prémiový balíček"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Kupní cena"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Nákupní transakce"),
        "quantity": MessageLookupByLibrary.simpleMessage("Množství*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Nabídka"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Seznam nabídek"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Nedávné Prodeje"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referenční číslo"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referenční číslo"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrace"),
        "remaining": MessageLookupByLibrary.simpleMessage("Zbývá: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Zbývající zůstatek"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Zbývající dluh"),
        "reports": MessageLookupByLibrary.simpleMessage("Zprávy"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Obnovit vaše heslo"),
        "retailer": MessageLookupByLibrary.simpleMessage("Prodejce"),
        "revenue": MessageLookupByLibrary.simpleMessage("Příjmy"),
        "right": MessageLookupByLibrary.simpleMessage("Vpravo"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Částka Prodeje"),
        "sale": MessageLookupByLibrary.simpleMessage("Prodej"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Částka prodeje"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Podrobnosti prodeje"),
        "saleList": MessageLookupByLibrary.simpleMessage("Seznam prodejů"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Prodejní cena"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Prodejní cena*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Vrácení prodeje"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Prodejní transakce"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Prodejní transakce (Historie prodeje nabídky)"),
        "sales": MessageLookupByLibrary.simpleMessage("Prodeje"),
        "salesList": MessageLookupByLibrary.simpleMessage("Seznam prodeje"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Uložit a publikovat"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Uložit a publikovat"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Uložit změny"),
        "search": MessageLookupByLibrary.simpleMessage("Hledat......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Hledat cokoli..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Hledat podle faktury...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Vyhledávání podle faktury nebo jména"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Hledat podle názvu"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Hledat podle jména nebo telefonu..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Vyhledat sériové číslo"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Vybrat strany"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Vyberte značku produktu"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Vyberte sériové číslo"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Vyberte varianty:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Vyberte dobu záruky"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Vyberte svůj jazyk"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Odeslat zprávu"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Sériové číslo"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Sériové číslo"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Poplatek za služby"),
        "setting": MessageLookupByLibrary.simpleMessage("Nastavení"),
        "share": MessageLookupByLibrary.simpleMessage("Sdílení"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Doprava/Jiné"),
        "shopName": MessageLookupByLibrary.simpleMessage("Název obchodu"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Zůstatek při otevření obchodu"),
        "show": MessageLookupByLibrary.simpleMessage("Zobrazit >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Zobrazit logo na faktuře?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Doprava/Služby"),
        "size": MessageLookupByLibrary.simpleMessage("Velikost"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistika"),
        "status": MessageLookupByLibrary.simpleMessage("Stav"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Zůstaňte v čele technologického pokroku bez dalších nákladů. Naše aktualizace Pos Saas POS Unlimited zajistí, že budete mít vždy nejnovější nástroje a funkce na dosah ruky, což zaručuje, že vaše firma zůstane na řezu."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Zůstaňte na špičce technologického pokroku bez dalších nákladů. Naše aktualizace Pos Sass POS Unlimited zajistí, že budete mít vždy nejnovější nástroje a funkce na dosah ruky, což zaručuje, že vaše firma zůstane na řezu."),
        "stock": MessageLookupByLibrary.simpleMessage("Sklad"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventář zásob"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Zpráva o zásobách"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Hodnota zásob"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Hodnota Skladu"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Mezisoučet"),
        "subciption": MessageLookupByLibrary.simpleMessage("Předplatné"),
        "submit": MessageLookupByLibrary.simpleMessage("Odeslat"),
        "supplier": MessageLookupByLibrary.simpleMessage("Dodavatelé"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Dluh u dodavatele"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Dodavatelská faktura"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Seznam dodavatelů"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT kód"),
        "tSale": MessageLookupByLibrary.simpleMessage("Celkový Prodej"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Fotografie řidičského průkazu, občanského průkazu nebo cestovního pasu"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Podmínky použití"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Jméno říká vše. S Pos Saas POS Unlimited neexistuje omezení ve vašem použití. Ať už zpracováváte několik transakcí nebo máte nával zákazníků, můžete provozovat s jistotou, že nejste omezeni limity."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Tento zákazník nemá žádné splatné dluhy"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Tento zákazník má předchozí dluh"),
        "to": MessageLookupByLibrary.simpleMessage("Do"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Nejprodávanější produkt"),
        "total": MessageLookupByLibrary.simpleMessage("celkem"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Celková částka"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Celková sleva"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Celkem dluh"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Celkem dluhy"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Celkové náklady"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Celkový příjem"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Celkem položek: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Celková ztráta"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Celkem zaplaceno"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Celkem k zaplacení"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Celková platba vyplacena"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Celková cena"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Celkem produktů"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Celkový zisk"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Celkový nákup"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Celková částka vrácení"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Celkové vrácení"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Celkový prodej"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Celkové prodeje"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Celková DPH"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Celková platba přijata"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transakce"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID transakce"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Zpráva o transakcích"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Zkuste to znovu"),
        "type": MessageLookupByLibrary.simpleMessage("Typ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Nezaplaceno"),
        "unit": MessageLookupByLibrary.simpleMessage("Jednotka"),
        "unitName": MessageLookupByLibrary.simpleMessage("Název jednotky"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Jednotková cena"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Neomezené"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Neomezené faktury"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Neomezené použití"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Odemkněte plný potenciál Pos Saas POS s personalizovanými školeními vedenými naším odborným týmem. Od základů po pokročilé techniky vám zaručíme, že budete dobře obeznámeni s využitím každého aspektu systému k optimalizaci vašich firemních procesů."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Aktualizovat nyní"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Nejdříve aktualizujte svůj plán.\\nLimit prodeje byl překročen."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Upgradovat v mobilní aplikaci"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Nahrát obrázek"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Nahrát logo faktury"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Nahrát dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Nahrát soubor"),
        "userName": MessageLookupByLibrary.simpleMessage("Uživatelské jméno"),
        "userRole": MessageLookupByLibrary.simpleMessage("Role uživatele"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Název role uživatele"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Titul uživatele"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("DPH/OSS"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Ověřit telefonní číslo"),
        "view": MessageLookupByLibrary.simpleMessage("Zobrazit"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Klient bez objednávky"),
        "warranty": MessageLookupByLibrary.simpleMessage("Záruka"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Záruky"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Před začátkem je třeba zaregistrovat váš telefon!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Rozumíme důležitosti bezproblémového provozu. Proto máme non-stop podporu, která vám pomůže, ať už se jedná o rychlý dotaz nebo komplexní dotaz. Kontaktujte nás kdykoli a kdekoliv prostřednictvím volání nebo WhatsApp, abyste zažili nepřekonatelný servis."),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Velkoobchodník"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Velkoobchod"),
        "wight": MessageLookupByLibrary.simpleMessage("Hmotnost"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ano, vrátit"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Musíte se znovu přihlásit do svého účtu."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Před nákupem zpráv je nutné ověření totožnosti"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Seznam všech vašich prodejů"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Všechny vaše prodeje"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Používáte"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Vaše splatné prodeje"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Před nákupem zpráv je nutné ověření totožnosti"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Váš balíček"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Vaše platba byla zrušena"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Platba byla úspěšná"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Vaše platba byla zrušena")
      };
}
