// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a it locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'it';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("AGGIUNGI VENDITA"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATEGORIA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FATTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Vendita POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PREZZO"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NOME PRODOTTO"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Pannello di Accesso Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("QTY"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Quantità*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATO"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("VALORE TOTALE"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Titolo Utente"),
        "aboutApp":
            MessageLookupByLibrary.simpleMessage("Informazioni sull\'App"),
        "accountName":
            MessageLookupByLibrary.simpleMessage("Nome dell\'Account"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Numero dell\'Account"),
        "action": MessageLookupByLibrary.simpleMessage("Azione"),
        "add": MessageLookupByLibrary.simpleMessage("Aggiungi"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Aggiungi marchio"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Aggiungi categoria"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Aggiungi cliente"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Aggiungi descrizione..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Aggiungi documento"),
        "addItem": MessageLookupByLibrary.simpleMessage("Aggiungi elemento"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage(
            "Aggiungi categoria di articoli"),
        "addNew": MessageLookupByLibrary.simpleMessage("Aggiungi Nuovo"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Aggiungi Nuovo Utente"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Aggiungi prodotto"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Aggiunta Riuscita"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Aggiungi fornitore"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Aggiungi unità"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Aggiungi/Aggiorna elenco spese"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Aggiungi/Aggiorna l\'elenco dei redditi"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Aggiungi Ruolo Utente"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Stai aggiungendo un numero di serie?"),
        "address": MessageLookupByLibrary.simpleMessage("Indirizzo"),
        "all": MessageLookupByLibrary.simpleMessage("Tutto"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Tutte le funzionalità di base"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Hai già un account?"),
        "amount": MessageLookupByLibrary.simpleMessage("Importo"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Supporto App Android e iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Vuoi creare questa quotazione?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Vuoi eliminare questo cliente?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Vuoi eliminare questo prodotto"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Vuoi eliminare questo preventivo?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Vuoi restituire questa vendita?"),
        "balance": MessageLookupByLibrary.simpleMessage("Equilibrio"),
        "bankAccountingCurrecny": MessageLookupByLibrary.simpleMessage(
            "Valuta dell\'Account Bancario"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Conti bancari"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Informazioni Bancarie"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nome della Banca"),
        "between": MessageLookupByLibrary.simpleMessage("Tra"),
        "billTo": MessageLookupByLibrary.simpleMessage("Fattura a:"),
        "branchName":
            MessageLookupByLibrary.simpleMessage("Nome della Filiale"),
        "brand": MessageLookupByLibrary.simpleMessage("Marchio"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nome del marchio"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Categoria di attività"),
        "buy": MessageLookupByLibrary.simpleMessage("Acquista"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Acquista Piano Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Acquista sms"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calcolatrice:"),
        "camera": MessageLookupByLibrary.simpleMessage("Macchina fotografica"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annulla"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacità"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Contanti e banca"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Contanti in cassa"),
        "categories": MessageLookupByLibrary.simpleMessage("Categorie"),
        "category": MessageLookupByLibrary.simpleMessage("Categoria"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Nome della categoria"),
        "changeAmount":
            MessageLookupByLibrary.simpleMessage("Importo di resto"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Importo modificabile"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Controlla la garanzia"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Scegli un piano"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Raccogli il dovuto >"),
        "color": MessageLookupByLibrary.simpleMessage("Colore"),
        "comapnyName":
            MessageLookupByLibrary.simpleMessage("Nome dell\'azienda"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Indirizzo aziendale"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Descrizione dell\'azienda"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Indirizzo email aziendale"),
        "companyName":
            MessageLookupByLibrary.simpleMessage("Nome dell\'azienda"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Numero di telefono aziendale"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL del sito web aziendale"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Conferma password"),
        "continu": MessageLookupByLibrary.simpleMessage("Continua"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Converti in vendita"),
        "create": MessageLookupByLibrary.simpleMessage("Crea"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Crea pagamento"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Creato da"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Hub creativo"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Piano corrente"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Marchio Fattura Personalizzato"),
        "customer": MessageLookupByLibrary.simpleMessage("Cliente"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Credito cliente"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Fatture dei clienti"),
        "customerList": MessageLookupByLibrary.simpleMessage("Elenco clienti"),
        "customerName":
            MessageLookupByLibrary.simpleMessage("Nome del cliente"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Cliente del mese"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tipo di cliente"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Cliente: Cliente che entra"),
        "customers": MessageLookupByLibrary.simpleMessage("Clienti"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Raccolta giornaliera"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Vendite giornaliere"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transazione giornaliera"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "date": MessageLookupByLibrary.simpleMessage("Data"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Data e ora"),
        "dealer": MessageLookupByLibrary.simpleMessage("Rivenditori"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Prezzo del rivenditore"),
        "delete": MessageLookupByLibrary.simpleMessage("Elimina"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Costo di consegna"),
        "description": MessageLookupByLibrary.simpleMessage("Descrizione"),
        "details": MessageLookupByLibrary.simpleMessage(">Dettagli"),
        "discount": MessageLookupByLibrary.simpleMessage("Sconto"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Prezzo scontato"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Scarica PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Dovere"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Importo dovuto"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "L\'importo dovuto verrà visualizzato qui se disponibile"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Riscossione crediti"),
        "dueList": MessageLookupByLibrary.simpleMessage("Lista dei crediti"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transazione scaduta"),
        "edit": MessageLookupByLibrary.simpleMessage("Modifica"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Modifica/Aggiungi seriale:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Modifica il tuo profilo"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("Inserisci l\'importo"),
        "enterBrandName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome del marchio"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome della categoria"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Inserisci la descrizione dell\'azienda"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Inserisci l\'indirizzo email aziendale"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Inserisci il numero di telefono aziendale"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Inserisci l\'URL del sito web aziendale"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome del cliente"),
        "enterDealePrice": MessageLookupByLibrary.simpleMessage(
            "Inserisci il prezzo del rivenditore"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Inserisci il prezzo scontato"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Inserisci la categoria di spesa"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage(
            "Inserisci la data della spesa"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Inserisci la categoria di reddito"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage(
            "Inserisci la data del reddito"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome del produttore"),
        "enterName": MessageLookupByLibrary.simpleMessage("Inserisci il nome"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Inserisci il nome"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Inserisci una nota"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Inserisci il saldo di apertura"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Inserisci l\'importo pagato"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Inserisci la Password"),
        "enterPayingAmount": MessageLookupByLibrary.simpleMessage(
            "Inserisci l\'importo da pagare"),
        "enterPrice":
            MessageLookupByLibrary.simpleMessage("Inserisci il prezzo"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Inserisci la capacità del prodotto"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage(
            "Inserisci il codice del prodotto"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage(
            "Inserisci il colore del prodotto"),
        "enterProductName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome del prodotto"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Inserisci la quantità del prodotto"),
        "enterProductSize": MessageLookupByLibrary.simpleMessage(
            "Inserisci la dimensione del prodotto"),
        "enterProductType": MessageLookupByLibrary.simpleMessage(
            "Inserisci il tipo di prodotto"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage(
            "Inserisci l\'unità di prodotto"),
        "enterProductWeight": MessageLookupByLibrary.simpleMessage(
            "Inserisci il peso del prodotto"),
        "enterPurchasePrice": MessageLookupByLibrary.simpleMessage(
            "Inserisci il prezzo di acquisto"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Inserisci il numero di riferimento"),
        "enterSalePrice": MessageLookupByLibrary.simpleMessage(
            "Inserisci il prezzo di vendita"),
        "enterSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Inserisci il numero di serie"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Inserisci il contenuto del messaggio"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Inserisci l\'importo delle scorte"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Inserisci l\'ID della Transazione"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome dell\'unità"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il Nome del Ruolo Utente"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Inserisci il titolo dell\'utente"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Inserisci la garanzia"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "Inserisci il prezzo all\'ingrosso"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Inserisci l\'importo"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Inserisci il tuo indirizzo"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Inserisci il tuo indirizzo aziendale"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome della tua azienda"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome della tua azienda"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Inserisci il tuo indirizzo email"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Inserisci la tua password"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Inserisci di nuovo la password"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Inserisci il tuo numero di telefono"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome del tuo negozio"),
        "entercategoryName": MessageLookupByLibrary.simpleMessage(
            "Inserisci il nome della categoria"),
        "expense": MessageLookupByLibrary.simpleMessage("Spesa"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Data della spesa"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Dettagli della spesa"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Spesa per"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista delle categorie di spesa"),
        "expenses": MessageLookupByLibrary.simpleMessage("Spese"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top cinque prodotti più acquistati del mese"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Per Utilizzi Illimitati"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Password dimenticata?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Backup Dati Gratuito"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Aggiornamento Gratuito a Vita"),
        "freePackage":
            MessageLookupByLibrary.simpleMessage("Pacchetto Gratuito"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Piano Gratuito"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Inizia"),
        "govermentId": MessageLookupByLibrary.simpleMessage("ID del governo"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Totale lordo"),
        "hold": MessageLookupByLibrary.simpleMessage("In sospeso"),
        "holdNumber":
            MessageLookupByLibrary.simpleMessage("Numero di prenotazione"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verifica dell\'identità"),
        "inc": MessageLookupByLibrary.simpleMessage("Entrate"),
        "income": MessageLookupByLibrary.simpleMessage("Entrate"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Categoria di reddito"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Elenco categorie di reddito"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Data del reddito"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Dettagli del reddito"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Reddito per"),
        "incomeList":
            MessageLookupByLibrary.simpleMessage("Elenco dei redditi"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Aumenta le Scorte"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Privacy immediata"),
        "invoice": MessageLookupByLibrary.simpleMessage("Fattura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Fattura:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Numero di fattura..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Numero di fattura"),
        "item": MessageLookupByLibrary.simpleMessage("Articolo"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nome dell\'articolo"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("Verifica KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Dettagli del libro mastro"),
        "ledger": MessageLookupByLibrary.simpleMessage("Registro"),
        "left": MessageLookupByLibrary.simpleMessage("Sinistra"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Conti di prestito"),
        "logOut": MessageLookupByLibrary.simpleMessage("Esci"),
        "login": MessageLookupByLibrary.simpleMessage("Accesso"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Posizione del logo in fattura?"),
        "loss": MessageLookupByLibrary.simpleMessage("Perdita"),
        "lossOrProfit":
            MessageLookupByLibrary.simpleMessage("Perdita/Profitto"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Perdita(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Scorta bassa"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Magazzino basso"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Lascia un\'impressione duratura sui tuoi clienti con fatture personalizzate. Il nostro Aggiornamento Illimitato offre il vantaggio unico di personalizzare le tue fatture, aggiungendo un tocco professionale che rafforza l\'identità del tuo brand e favorisce la fedeltà dei clienti."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Produttore"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pannello di login Pos Saas"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Pannello di registrazione Pos Saas"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("App Mobile\n+\nDesktop"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Ricevuta di denaro"),
        "nam": MessageLookupByLibrary.simpleMessage("Nome*"),
        "name": MessageLookupByLibrary.simpleMessage("Nome"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nome, codice o categoria"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nuovi clienti"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nuovi clienti"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nuovo reddito"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Nessuna Connessione"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nessun cliente trovato"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nessuna transazione dovuta trovata"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nessuna categoria di spesa trovata"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nessuna categoria di reddito trovata"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nessun reddito trovato"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nessuna fattura trovata"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nessun prodotto trovato"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nessuna transazione di acquisto trovata"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nessun preventivo trovato"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Nessun report trovato"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nessuna transazione di vendita trovata"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nessun numero di serie trovato"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Nessun fornitore trovato"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nessuna transazione trovata"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Nessun Utente Trovato"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Nessun Ruolo Utente Trovato"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nessun numero di serie trovato"),
        "note": MessageLookupByLibrary.simpleMessage("Nota"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Assegni aperti"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo di apertura"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "o trascina e rilascia PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Ordini"),
        "other": MessageLookupByLibrary.simpleMessage("Altro"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Altre entrate"),
        "packageFeature": MessageLookupByLibrary.simpleMessage(
            "Caratteristiche del Pacchetto"),
        "paid": MessageLookupByLibrary.simpleMessage("Pagato"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Importo pagato"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nome del partito"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tipo di partito"),
        "password": MessageLookupByLibrary.simpleMessage("Parola d\'ordine"),
        "payCash": MessageLookupByLibrary.simpleMessage("Paga in Contanti"),
        "payable": MessageLookupByLibrary.simpleMessage("Da pagare"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Importo pagato"),
        "payment": MessageLookupByLibrary.simpleMessage("Pagamento"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Pagamento in"),
        "paymentOut":
            MessageLookupByLibrary.simpleMessage("Pagamento in uscita"),
        "paymentType":
            MessageLookupByLibrary.simpleMessage("Tipo di pagamento"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Tipo di pagamento"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefono"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Numero di telefono"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verifica del telefono"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Aggiungi una vendita"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Aggiungi cliente"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Controlla la tua connessione Internet"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Si prega di scaricare la nostra app mobile e iscriversi a un pacchetto per utilizzare la versione desktop"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Inserisci le scorte di prodotti"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Inserisci dati validi"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Seleziona un cliente"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Inserisci dati validi"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Pannello di Registrazione Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Pratiche"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Supporto Clienti Premium"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Piano Premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Anteprima"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Scadenza precedente:"),
        "price": MessageLookupByLibrary.simpleMessage("Prezzo"),
        "print": MessageLookupByLibrary.simpleMessage("Stampa"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Stampa fattura"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Stampa PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Politica sulla Privacy"),
        "product": MessageLookupByLibrary.simpleMessage("Prodotto"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Categoria di prodotto"),
        "productCod": MessageLookupByLibrary.simpleMessage("Codice prodotto*"),
        "productColor":
            MessageLookupByLibrary.simpleMessage("Colore del prodotto"),
        "productList": MessageLookupByLibrary.simpleMessage("Elenco prodotti"),
        "productNam":
            MessageLookupByLibrary.simpleMessage("Nome del prodotto*"),
        "productName":
            MessageLookupByLibrary.simpleMessage("Nome del prodotto"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Dimensione del prodotto"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Scorte di Prodotti"),
        "productType": MessageLookupByLibrary.simpleMessage("Tipo di prodotto"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Unità di prodotto"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garanzia del prodotto"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Peso del prodotto"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Capacità del prodotto"),
        "prof": MessageLookupByLibrary.simpleMessage("Profilo"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Modifica Profilo"),
        "profit": MessageLookupByLibrary.simpleMessage("Profitto"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profitto (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profitto (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Acquisto"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Lista di acquisto"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Acquista Piano Premium"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Prezzo di acquisto"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transazione di acquisto"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantità"),
        "quotation": MessageLookupByLibrary.simpleMessage("Quotazione"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Lista di preventivi"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Vendite recenti"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Importo ricevuto"),
        "referenceNo":
            MessageLookupByLibrary.simpleMessage("Numero di riferimento"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Numero di riferimento"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrazione"),
        "remaining": MessageLookupByLibrary.simpleMessage("Rimanenti: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo residuo"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Credito residuo"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapporti"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Resetta la password"),
        "retailer": MessageLookupByLibrary.simpleMessage("Rivenditori"),
        "revenue": MessageLookupByLibrary.simpleMessage("Ricavi"),
        "right": MessageLookupByLibrary.simpleMessage("Destra"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Importo vendita"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Proteggi facilmente i dati della tua azienda. Il nostro Aggiornamento Illimitato di Pos Saas POS include un backup gratuito dei dati, garantendo la protezione delle tue informazioni preziose da eventuali eventi imprevisti. Concentrati su ciò che conta veramente: la crescita della tua azienda."),
        "sale": MessageLookupByLibrary.simpleMessage("Vendita"),
        "saleAmount":
            MessageLookupByLibrary.simpleMessage("Importo delle vendite"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Dettagli della vendita"),
        "saleList": MessageLookupByLibrary.simpleMessage("Elenco di vendita"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Prezzo di vendita"),
        "salePrices":
            MessageLookupByLibrary.simpleMessage("Prezzo di vendita*"),
        "saleReturn":
            MessageLookupByLibrary.simpleMessage("Ritorno di vendita"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transazione di vendita"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transazioni di vendita (Storia delle quotazioni di vendita)"),
        "sales": MessageLookupByLibrary.simpleMessage("Vendite"),
        "salesList": MessageLookupByLibrary.simpleMessage("Elenco Vendite"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Salva e pubblica"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Salva e pubblica"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Salva le modifiche"),
        "search": MessageLookupByLibrary.simpleMessage("Cerca......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Cerca qualcosa..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Cerca per fattura..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Cerca per fattura o nome"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Cerca per nome"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Cerca per nome o telefono..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Cerca numero di serie"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Seleziona parti"),
        "selectProductBrand": MessageLookupByLibrary.simpleMessage(
            "Seleziona il marchio del prodotto"),
        "selectSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Seleziona il numero di serie"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Seleziona varianti:"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage(
            "Seleziona il periodo di garanzia"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Seleziona la tua lingua"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Invia messaggio"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Numero di serie"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("Numero di serie"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Tassa di servizio"),
        "setting": MessageLookupByLibrary.simpleMessage("Impostazioni"),
        "share": MessageLookupByLibrary.simpleMessage("Condividi"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Spedizione/Altro"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nome del negozio"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Saldo di apertura del negozio"),
        "show": MessageLookupByLibrary.simpleMessage(">Mostra"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Mostra logo in fattura?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Spedizione/Servizio"),
        "size": MessageLookupByLibrary.simpleMessage("Dimensione"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistica"),
        "status": MessageLookupByLibrary.simpleMessage("Stato"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Rimani all\'avanguardia delle innovazioni tecnologiche senza costi aggiuntivi. Il nostro Aggiornamento Illimitato di Pos Saas POS assicura che tu abbia sempre gli strumenti e le funzionalità più recenti a portata di mano, garantendo che la tua azienda rimanga all\'avanguardia."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Rimani all\'avanguardia delle innovazioni tecnologiche senza costi aggiuntivi. Il nostro Aggiornamento Illimitato di Pos Sass POS assicura che tu abbia sempre gli strumenti e le funzionalità più recenti a portata di mano, garantendo che la tua azienda rimanga all\'avanguardia."),
        "stock": MessageLookupByLibrary.simpleMessage("Azione"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventario di magazzino"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Report di magazzino"),
        "stockValue":
            MessageLookupByLibrary.simpleMessage("Valore del magazzino"),
        "stockValues":
            MessageLookupByLibrary.simpleMessage("Valori di magazzino"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Totale parziale"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abbonamento"),
        "submit": MessageLookupByLibrary.simpleMessage("Invia"),
        "supplier": MessageLookupByLibrary.simpleMessage("Fornitori"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Debito fornitore"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Fattura del fornitore"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Elenco fornitori"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Codice SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Vendite totali"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Scatta una foto di una patente di guida, carta d\'identità nazionale o passaporto"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Termini di Utilizzo"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Il nome dice tutto. Con Pos Saas POS Unlimited, non ci sono limiti d\'uso. Che tu stia elaborando un numero limitato di transazioni o che ti trovi di fronte a un afflusso di clienti, puoi operare con fiducia, sapendo di non essere limitato da restrizioni."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Questo cliente non ha crediti"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Questo cliente ha un precedente debito"),
        "to": MessageLookupByLibrary.simpleMessage("A"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Prodotto più venduto"),
        "total": MessageLookupByLibrary.simpleMessage("Totale"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Importo totale"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Totale sconto"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Totale dovuto"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Totale crediti"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Spese totali"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Reddito totale"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Totale articoli: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Perdita totale"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Totale pagato"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Totale pagabile"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Totale pagamento in uscita"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Prezzo totale"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Totale prodotti"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Profitto totale"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Totale acquisti"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Importo totale del ritorno"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Ritorni totali"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Totale vendite"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Totale vendite"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Totale IVA"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Totale pagamento in"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transazione"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID Transazione"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Rapporto di transazione"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Riprova"),
        "type": MessageLookupByLibrary.simpleMessage("Tipo"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Non pagato"),
        "unit": MessageLookupByLibrary.simpleMessage("Unità"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nome dell\'unità"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Prezzo unitario"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Illimitato"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Fatture illimitate"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Utilizzo Illimitato"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Sblocca il pieno potenziale di Pos Saas POS con sessioni di formazione personalizzate condotte dal nostro team di esperti. Dalle basi alle tecniche avanzate, ti assicuriamo di essere ben preparato nell\'utilizzare ogni aspetto del sistema per ottimizzare i tuoi processi aziendali."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Aggiorna Ora"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Aggiorna il tuo piano prima \\ nIl limite di vendita è esaurito."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Aggiorna sull\'app mobile"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Carica un\'immagine"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Carica un logo di fattura"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Carica Documento"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Carica File"),
        "userName": MessageLookupByLibrary.simpleMessage("Nome Utente"),
        "userRole": MessageLookupByLibrary.simpleMessage("Ruolo Utente"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nome del Ruolo Utente"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Titolo Utente"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("IVA/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifica numero di telefono"),
        "view": MessageLookupByLibrary.simpleMessage("Visualizza"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Cliente che entra"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garanzia"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garanzia"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Dobbiamo registrare il tuo telefono prima di iniziare!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Comprendiamo l\'importanza di operazioni senza soluzione di continuità. Ecco perché il nostro supporto attivo 24/7 è disponibile per aiutarti, che si tratti di una semplice domanda o di una questione complessa. Collegati con noi in qualsiasi momento, ovunque, tramite chiamata o WhatsApp per vivere un servizio clienti imbattibile."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Prezzo all\'ingrosso"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Grossista"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Ingrosso"),
        "wight": MessageLookupByLibrary.simpleMessage("Peso"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Sì, restituisci"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Devi RIESEGUIRE il LOGIN sul tuo account."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Devi verificare la tua identità prima di acquistare i messaggi"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Elenco di tutte le tue vendite"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Tutte le tue vendite"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Stai utilizzando"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("I tuoi crediti"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Devi verificare la tua identità prima di acquistare i messaggi"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Il Tuo Pacchetto"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "Il tuo pagamento è stato cancellato"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Il tuo pagamento è stato completato con successo"),
        "yourPaymentIscancelled": MessageLookupByLibrary.simpleMessage(
            "Il tuo pagamento è stato cancellato")
      };
}
