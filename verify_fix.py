#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت للتحقق من إصلاح مشكلة withOpacity في مشروع Flutter
يتحقق من عدم وجود استخدامات withOpacity متبقية ويعرض إحصائيات

المؤلف: مساعد الذكي
التاريخ: 2025-07-15
"""

import os
import re
import sys
from pathlib import Path
import argparse

class WithOpacityVerifier:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.remaining_withopacity = []
        self.color_fromrgbo_count = 0
        
    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")
        
        for file_path in self.project_path.rglob("*.dart"):
            # تجاهل ملفات البناء والملفات المولدة
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")
        
    def check_file(self, file_path):
        """فحص ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # البحث عن withOpacity المتبقية
            withopacity_pattern = r'\.withOpacity\('
            withopacity_matches = re.findall(withopacity_pattern, content)
            
            # البحث عن Color.fromRGBO
            fromrgbo_pattern = r'Color\.fromRGBO\('
            fromrgbo_matches = re.findall(fromrgbo_pattern, content)
            
            if withopacity_matches:
                # العثور على السطور التي تحتوي على withOpacity
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if '.withOpacity(' in line:
                        self.remaining_withopacity.append({
                            'file': file_path.name,
                            'line': i,
                            'content': line.strip()
                        })
                        
            self.color_fromrgbo_count += len(fromrgbo_matches)
            
        except Exception as e:
            print(f"❌ خطأ في فحص {file_path}: {e}")
            
    def run(self):
        """تشغيل التحقق"""
        print("🔍 التحقق من إصلاح مشكلة withOpacity...")
        print("=" * 50)
        
        self.find_dart_files()
        
        if not self.dart_files:
            print("❌ لم يتم العثور على ملفات Dart")
            return
            
        print(f"📝 فحص {len(self.dart_files)} ملف...")
        print("-" * 30)
        
        for file_path in self.dart_files:
            self.check_file(file_path)
            
        print("-" * 30)
        print(f"📊 نتائج التحقق:")
        print(f"   - إجمالي ملفات Dart: {len(self.dart_files)}")
        print(f"   - استخدامات Color.fromRGBO: {self.color_fromrgbo_count}")
        print(f"   - استخدامات withOpacity المتبقية: {len(self.remaining_withopacity)}")
        
        if self.remaining_withopacity:
            print(f"\n⚠️  تم العثور على {len(self.remaining_withopacity)} استخدام withOpacity متبقي:")
            print("-" * 40)
            for item in self.remaining_withopacity[:10]:  # أول 10 فقط
                print(f"📁 {item['file']} - السطر {item['line']}")
                print(f"   {item['content']}")
                print()
                
            if len(self.remaining_withopacity) > 10:
                print(f"... و {len(self.remaining_withopacity) - 10} استخدام آخر")
        else:
            print(f"\n🎉 ممتاز! لا توجد استخدامات withOpacity متبقية!")
            print(f"✅ تم إصلاح جميع المشاكل بنجاح")
            
        # إنشاء تقرير التحقق
        self.generate_verification_report()
        
    def generate_verification_report(self):
        """إنشاء تقرير التحقق"""
        report_path = self.project_path / "withopacity_verification_report.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("تقرير التحقق من إصلاح withOpacity\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"التاريخ: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"إجمالي ملفات Dart: {len(self.dart_files)}\n")
            f.write(f"استخدامات Color.fromRGBO: {self.color_fromrgbo_count}\n")
            f.write(f"استخدامات withOpacity المتبقية: {len(self.remaining_withopacity)}\n\n")
            
            if self.remaining_withopacity:
                f.write("استخدامات withOpacity المتبقية:\n")
                f.write("-" * 30 + "\n")
                for item in self.remaining_withopacity:
                    f.write(f"الملف: {item['file']} - السطر: {item['line']}\n")
                    f.write(f"المحتوى: {item['content']}\n\n")
            else:
                f.write("🎉 تم إصلاح جميع استخدامات withOpacity بنجاح!\n")
                
        print(f"📄 تم إنشاء تقرير التحقق: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='التحقق من إصلاح مشكلة withOpacity في مشروع Flutter')
    parser.add_argument('--path', default='.', help='مسار المشروع (افتراضي: المجلد الحالي)')
    
    args = parser.parse_args()
    
    verifier = WithOpacityVerifier(args.path)
    verifier.run()

if __name__ == "__main__":
    main()
