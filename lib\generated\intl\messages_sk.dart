// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a sk locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'sk';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("PRIDAŤ PREDAJ"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGÓRIA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTÚRA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Predaj na TP"),
        "PRICE": MessageLookupByLibrary.simpleMessage("CENA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NÁZOV PRODUKTU"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Prihlasovací panel Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("MNOŽSTVO"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STAV"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("CELKOVÁ HODNOTA"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Titul používateľa"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("O aplikácii"),
        "accountName": MessageLookupByLibrary.simpleMessage("Názov účtu"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Číslo účtu"),
        "action": MessageLookupByLibrary.simpleMessage("Akcia"),
        "add": MessageLookupByLibrary.simpleMessage("Pridať"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Pridať značku"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Pridať kategóriu"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Pridať zákazníka"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Pridať popis..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Pridať dokumenty"),
        "addItem": MessageLookupByLibrary.simpleMessage("Pridať položku"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Pridať kategóriu položky"),
        "addNew": MessageLookupByLibrary.simpleMessage("Pridať nové"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Pridať nového používateľa"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Pridať produkt"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Úspešne pridané"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Pridať dodávateľa"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Pridať jednotku"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Pridať/aktualizovať zoznam výdavkov"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Pridať / Aktualizovať zoznam príjmov"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Pridať rolu používateľa"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pridávanie sériového čísla?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresa"),
        "all": MessageLookupByLibrary.simpleMessage("Všetko"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Všetky základné funkcie"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Už máte účet?"),
        "amount": MessageLookupByLibrary.simpleMessage("Suma"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Podpora pre aplikácie Android a iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Chcete vytvoriť túto cenovú ponuku?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Chcete odstrániť tohto zákazníka?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Chcete tento produkt vymazať"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Chcete túto cenovú ponuku odstrániť?"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("Chcete vrátiť tento predaj?"),
        "balance": MessageLookupByLibrary.simpleMessage("Zostatok"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Mena bankového účtu"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankové účty"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankské informácie"),
        "bankName": MessageLookupByLibrary.simpleMessage("Názov banky"),
        "between": MessageLookupByLibrary.simpleMessage("Medzi"),
        "billTo": MessageLookupByLibrary.simpleMessage("Adresa:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Názov pobočky"),
        "brand": MessageLookupByLibrary.simpleMessage("Značka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Názov značky"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Odbor podnikania"),
        "buy": MessageLookupByLibrary.simpleMessage("Kúpiť"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kúpiť prémiový plán"),
        "buySms": MessageLookupByLibrary.simpleMessage("Kúpiť SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulačka:"),
        "camera": MessageLookupByLibrary.simpleMessage("Fotoaparát"),
        "cancel": MessageLookupByLibrary.simpleMessage("Zrušiť"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacita"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Hotovosť a banka"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Hotovosť v ruke"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategórie"),
        "category": MessageLookupByLibrary.simpleMessage("Kategória"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Názov kategórie"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Zmeniť sumu"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Zmeniteľná suma"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Skontrolovať záruku"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Vyberte si plán"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Zbierajte splatné >"),
        "color": MessageLookupByLibrary.simpleMessage("Farba"),
        "comapnyName":
            MessageLookupByLibrary.simpleMessage("Názov spoločnosti"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Adresa spoločnosti"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Popis spoločnosti"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "E-mailová adresa spoločnosti"),
        "companyName":
            MessageLookupByLibrary.simpleMessage("Názov spoločnosti"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefónne číslo spoločnosti"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "URL webovej stránky spoločnosti"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Potvrďte heslo"),
        "continu": MessageLookupByLibrary.simpleMessage("Pokračovať"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konvertovať na predaj"),
        "create": MessageLookupByLibrary.simpleMessage("Vytvoriť"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Vytvoriť platbu"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Vytvoril(a)"),
        "creativeHub":
            MessageLookupByLibrary.simpleMessage("Kreatívne centrum"),
        "currency": MessageLookupByLibrary.simpleMessage("Mena"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Aktuálny plán"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Vlastné označenie faktúr"),
        "customer": MessageLookupByLibrary.simpleMessage("Zákazník"),
        "customerDue":
            MessageLookupByLibrary.simpleMessage("Záväzky zákazníka"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Faktúry zákazníka"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Zoznam zákazníkov"),
        "customerName": MessageLookupByLibrary.simpleMessage("Meno zákazníka"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Zákazník mesiaca"),
        "customerType": MessageLookupByLibrary.simpleMessage("Typ zákazníka"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "Zákazník: Zákazník bez objednávky"),
        "customers": MessageLookupByLibrary.simpleMessage("Zákazníci"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("Denné zber"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Denný predaj"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Denná transakcia"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Nástenka"),
        "date": MessageLookupByLibrary.simpleMessage("Dátum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Dátum a čas"),
        "dealer": MessageLookupByLibrary.simpleMessage("Predajca"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Cena pre predajcov"),
        "delete": MessageLookupByLibrary.simpleMessage("Odstrániť"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Poplatok za doručenie"),
        "description": MessageLookupByLibrary.simpleMessage("Popis"),
        "details": MessageLookupByLibrary.simpleMessage("Podrobnosti >"),
        "discount": MessageLookupByLibrary.simpleMessage("Zľava"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Zľavnená cena"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Stiahnuť PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Dlh"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Splatná suma"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Zostatková suma sa tu zobrazí, ak je k dispozícii"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Zbierka pohľadávok"),
        "dueList": MessageLookupByLibrary.simpleMessage("Zoznam záväzkov"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcia na úhradu"),
        "edit": MessageLookupByLibrary.simpleMessage("Upraviť"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Upraviť/Pridať sériové číslo:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Upravte svoj profil"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Zadajte sumu"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov značky"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov kategórie"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Zadajte popis spoločnosti"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Zadajte e-mailovú adresu spoločnosti"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Zadajte telefónne číslo spoločnosti"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Zadajte URL webovej stránky spoločnosti"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Zadajte meno zákazníka"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Zadajte cenu pre predajcov"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Zadajte zľavnenú cenu"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Zadajte kategóriu výdavkov"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Zadajte dátum výdavku"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Zadajte kategóriu príjmov"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Zadajte dátum príjmu"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov výrobcu"),
        "enterName": MessageLookupByLibrary.simpleMessage("Zadajte meno"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Zadajte mená"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Zadajte poznámku"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Zadajte úvodný zostatok"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Zadajte zaplatenú sumu"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Zadajte heslo"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Zadajte sumu na zaplatenie"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Zadajte cenu"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Zadajte kapacitu produktu"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Zadajte kód produktu"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Zadajte farbu produktu"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov produktu"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Zadajte množstvo produktu"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Zadajte veľkosť produktu"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Zadajte typ produktu"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Zadajte jednotku produktu"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Zadajte hmotnosť produktu"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Zadajte nákupnú cenu"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Zadajte referenčné číslo"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Zadajte predajnú cenu"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Zadajte sériové číslo"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Zadajte obsah správy"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Zadajte množstvo na sklade"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Zadajte ID transakcie"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov jednotky"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Zadajte názov roly používateľa"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Zadajte titul používateľa"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Zadajte záruku"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Zadajte veľkoobchodnú cenu"),
        "enterYOurAmount": MessageLookupByLibrary.simpleMessage("Zadajte sumu"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Zadajte vašu adresu"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Zadajte adresu svojej spoločnosti"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Zadajte názov vašej spoločnosti"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Zadajte názov vašej spoločnosti"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Zadajte svoju e-mailovú adresu"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Zadajte svoje heslo"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Zadajte svoje heslo znova"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Zadajte vaše telefónne číslo"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov vášho obchodu"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Zadajte názov kategórie"),
        "expense": MessageLookupByLibrary.simpleMessage("Výdavok"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Dátum výdavku"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detaily výdavkov"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Výdavok pre"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Zoznam kategórií výdavkov"),
        "expenses": MessageLookupByLibrary.simpleMessage("Náklady"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top päť najpredávanejších produktov mesiaca"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Pre neobmedzené použitie"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Zabudli ste heslo?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Zálohovanie dát zdarma"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Bezplatná aktualizácia po celý život"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Bezplatný balík"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Bezplatný plán"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Začať"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Identifikačný doklad"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Celková suma"),
        "hold": MessageLookupByLibrary.simpleMessage("Podržať"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Číslo podržania"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Overenie identity"),
        "inc": MessageLookupByLibrary.simpleMessage("Príjmy"),
        "income": MessageLookupByLibrary.simpleMessage("Príjem"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategória príjmu"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Zoznam kategórií príjmov"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Dátum príjmu"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Podrobnosti o príjme"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Príjem pre"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Zoznam príjmov"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Zvýšiť sklad"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Okamžité súkromie"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktúra"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Faktúra:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Číslo faktúry.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Číslo faktúry"),
        "item": MessageLookupByLibrary.simpleMessage("Položka"),
        "itemName": MessageLookupByLibrary.simpleMessage("Názov položky"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("Overenie KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Podrobnosti o účte"),
        "ledger": MessageLookupByLibrary.simpleMessage("Účtovník"),
        "left": MessageLookupByLibrary.simpleMessage("Vľavo"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Účty pôžičiek"),
        "logOut": MessageLookupByLibrary.simpleMessage("Odhlásiť sa"),
        "login": MessageLookupByLibrary.simpleMessage("Prihlásiť sa"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Poloha loga na faktúre?"),
        "loss": MessageLookupByLibrary.simpleMessage("Strata"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Strata / Zisk"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Strata(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Nízky Stav Zásob"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Nízke zásoby"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Urobte trvalý dojem na svojich zákazníkov s označenými faktúrami. Naša neobmedzená aktualizácia ponúka jedinečnú výhodu vlastného prispôsobenia faktúr, čím pridáva profesionálny dotyk, ktorý posilňuje vašu značku a podporuje zákaznícku vernosť."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Výrobca"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Prihlasovací panel Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Registračný panel Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Mobilná aplikácia\n+\nPlocha"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Účtenka"),
        "nam": MessageLookupByLibrary.simpleMessage("Meno*"),
        "name": MessageLookupByLibrary.simpleMessage("Meno"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Meno, kód alebo kategória"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Noví zákazníci"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Noví zákazníci"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nový príjem"),
        "no": MessageLookupByLibrary.simpleMessage("Nie"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Žiadne pripojenie"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nenašli sa žiadni zákazníci"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Žiadna splatná transakcia nenájdená"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Žiadna kategória výdavkov nenájdená"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Žiadna kategória príjmu nebola nájdená"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Žiadny príjem nebol nájdený"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nenašla sa žiadna faktúra"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Žiadny produkt nenájdený"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Žiadna transakcia nákupu nenájdená"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage(
            "Žiadna cenová ponuka nenájdená"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Žiadna správa nenájdená"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nenašla sa žiadna predajná transakcia"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nenašlo sa žiadne sériové číslo"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "Nenašiel sa žiadny dodávateľ"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Žiadna transakcia nebola nájdená"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Používateľ nenájdený"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Rola používateľa nenájdená"),
        "note": MessageLookupByLibrary.simpleMessage("Poznámka"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Otvorené šeky"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Úvodný zostatok"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "alebo presuňte a pustite PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Objednávky"),
        "other": MessageLookupByLibrary.simpleMessage("Ostatné"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Iné príjmy"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Funkcie balíka"),
        "paid": MessageLookupByLibrary.simpleMessage("Zaplatené"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Zaplatená suma"),
        "partyName": MessageLookupByLibrary.simpleMessage("Názov strany"),
        "partyType": MessageLookupByLibrary.simpleMessage("Typ strany"),
        "password": MessageLookupByLibrary.simpleMessage("Heslo"),
        "payCash": MessageLookupByLibrary.simpleMessage("Zaplatiť hotovosťou"),
        "payable": MessageLookupByLibrary.simpleMessage("Suma na úhradu"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Splatná suma"),
        "payment": MessageLookupByLibrary.simpleMessage("Platba"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Platba dovnútra"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Platba von"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Typ platby"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Typy platby"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefón"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefónne číslo"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Overenie telefónneho čísla"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Pridajte prosím predaj"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Prosím, pridajte zákazníka"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Skontrolujte svoje pripojenie k internetu"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Prosím, stiahnite si našu mobilnú aplikáciu a prihláste sa na balík, aby ste mohli používať desktopovú verziu."),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Prosím, zadajte sklad produktov"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Prosím, zadajte platné údaje"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Vyberte prosím zákazníka"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Prosím, zadajte platné údaje"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Registrácia Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Praktizovať"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Prémiová zákaznícka podpora"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Prémiový plán"),
        "preview": MessageLookupByLibrary.simpleMessage("Náhľad"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Predchádzajúci dlh:"),
        "price": MessageLookupByLibrary.simpleMessage("Cena"),
        "print": MessageLookupByLibrary.simpleMessage("Tlačiť"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Tlačiť faktúru"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Tlačiť PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Zásady ochrany súkromia"),
        "product": MessageLookupByLibrary.simpleMessage("Produkt"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategória produktu"),
        "productColor": MessageLookupByLibrary.simpleMessage("Farba produktu"),
        "productList": MessageLookupByLibrary.simpleMessage("Zoznam produktov"),
        "productName": MessageLookupByLibrary.simpleMessage("Názov produktu"),
        "productSize": MessageLookupByLibrary.simpleMessage("Veľkosť produktu"),
        "productStock": MessageLookupByLibrary.simpleMessage("Sklad produktov"),
        "productType": MessageLookupByLibrary.simpleMessage("Typ produktu"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Jednotka produktu"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Hmotnosť produktu"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapacita produktu"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Upraviť profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Zisk"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Zisk (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Zisk (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Nákup"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Zoznam nákupov"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kúpiť prémiový plán"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Kupná cena"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcia nákupu"),
        "quantity": MessageLookupByLibrary.simpleMessage("Množstvo*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Cenová ponuka"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Zoznam cenových ponúk"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Nedávne Predaje"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Prijatá suma"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referenčné číslo"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referenčné číslo"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrácia"),
        "remaining": MessageLookupByLibrary.simpleMessage("Zostáva: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Zostávajúci zostatok"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Zostávajúce záväzky"),
        "reports": MessageLookupByLibrary.simpleMessage("Správy"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Obnovte svoje heslo"),
        "retailer": MessageLookupByLibrary.simpleMessage("Maloobchodník"),
        "revenue": MessageLookupByLibrary.simpleMessage("Výnos"),
        "right": MessageLookupByLibrary.simpleMessage("Vpravo"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Čiastka Predaja"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Zabezpečte svoje podnikateľské údaje bez problémov. Naša neobmedzená aktualizácia Pos Saas POS obsahuje zálohu dát zdarma, čím zabezpečuje ochranu vašich cenných informácií pred nečakanými udalosťami. Sústredte sa na to, čo je skutočne dôležité - rast vášho podnikania."),
        "sale": MessageLookupByLibrary.simpleMessage("Predaj"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Suma predaja"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Podrobnosti o predaji"),
        "saleList": MessageLookupByLibrary.simpleMessage("Zoznam predajov"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Predajná cena"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Predajná cena*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Vrátenie predaja"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Predajná transakcia"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Predajné transakcie (História predaja citácií)"),
        "sales": MessageLookupByLibrary.simpleMessage("Predaje"),
        "salesList": MessageLookupByLibrary.simpleMessage("Zoznam predaja"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Uložiť a zverejniť"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Uložiť a publikovať"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Uložiť zmeny"),
        "search": MessageLookupByLibrary.simpleMessage("Hľadať......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Hľadať čokoľvek..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Hľadať podľa faktúry..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Vyhľadávať podľa faktúry alebo mena"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Vyhľadávanie podľa názvu"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Vyhľadať podľa mena alebo telefónu..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Vyhľadať sériové číslo"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Vybrať strany"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Vyberte značku produktu"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Vyberte sériové číslo"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Vyberte varianty:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Vyberte dobu záruky"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Vyberte si jazyk"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Odoslať správu"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Sériové číslo"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Sériové číslo"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Poplatok za služby"),
        "setting": MessageLookupByLibrary.simpleMessage("Nastavenie"),
        "share": MessageLookupByLibrary.simpleMessage("Zdieľať"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Doprava/Iné"),
        "shopName": MessageLookupByLibrary.simpleMessage("Názov obchodu"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Zostatok pri otvorení obchodu"),
        "show": MessageLookupByLibrary.simpleMessage("Zobraziť >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Zobraziť logo na faktúre?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Doprava / Služby"),
        "size": MessageLookupByLibrary.simpleMessage("Veľkosť"),
        "statistic": MessageLookupByLibrary.simpleMessage("Štatistika"),
        "status": MessageLookupByLibrary.simpleMessage("Stav"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Buďte na čele technologického pokroku bez dodatočných nákladov. Naša neobmedzená aktualizácia Pos Saas POS zabezpečuje, že budete mať vždy najnovšie nástroje a funkcie pri ruke, čím sa zaručuje, že váš podnik zostane v popredí."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Buďte na čele technologických pokrokov bez dodatočných nákladov. Naša neobmedzená aktualizácia Pos Sass POS zabezpečuje, že budete mať vždy najnovšie nástroje a funkcie pri ruke, čím sa zaručuje, že váš podnik zostane v popredí."),
        "stock": MessageLookupByLibrary.simpleMessage("Sklad"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventár zásob"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Správa o stave zásob"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Hodnota zásob"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Hodnota Zásob"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Medzisúčet"),
        "subciption": MessageLookupByLibrary.simpleMessage("Predplatné"),
        "submit": MessageLookupByLibrary.simpleMessage("Odoslať"),
        "supplier": MessageLookupByLibrary.simpleMessage("Dodávatelia"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Záväzky dodávateľa"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Dodávateľská faktúra"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Zoznam dodávateľov"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT kód"),
        "tSale":
            MessageLookupByLibrary.simpleMessage("Celkový Počet Predaných"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Odfotografovať vodičský preukaz, občiansky preukaz alebo cestovný pas"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Podmienky používania"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Názov hovorí sám za seba. S Pos Saas POS Unlimited neexistuje žiadna obmedzená prevádzka. Nech už spracovávate iba niekoľko transakcií alebo máte nával zákazníkov, môžete konať s istotou, že nie ste obmedzení limítmi."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Tento zákazník nemá žiadne záväzky"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Tento zákazník má predchádzajúci dlh"),
        "to": MessageLookupByLibrary.simpleMessage("Do"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Najpredávanejší produkt"),
        "total": MessageLookupByLibrary.simpleMessage("celkom"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Celková suma"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Celková zľava"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Celková suma"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Celkové záväzky"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Celkové výdavky"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Celkový príjem"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Celkom položiek: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Celková strata"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Celkovo zaplatené"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Celková suma na zaplatenie"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Celková platba von"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Celková cena"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Celkový počet produktov"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Celkový zisk"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Celkový nákup"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Celková suma vrátenia"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Celkové vrátenia"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Celkový predaj"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Celkový predaj"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Celková DPH"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Celková platba dovnútra"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transakcia"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID transakcie"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Správa o transakciách"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Skúsiť znova"),
        "type": MessageLookupByLibrary.simpleMessage("Typ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Nezaplatené"),
        "unit": MessageLookupByLibrary.simpleMessage("Jednotka"),
        "unitName": MessageLookupByLibrary.simpleMessage("Názov jednotky"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Jednotková cena"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Neobmedzený"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Neobmedzené faktúry"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Neobmedzené používanie"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Odomknite plný potenciál systému Pos Saas POS s personalizovanými školeniami, ktoré vedie náš odborný tím. Od základov po pokročilé techniky sa uistíme, že dobre ovládate využívanie každej časti systému na optimalizáciu svojich obchodných procesov."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Aktualizovať teraz"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Najskôr aktualizujte svoj plán. Predaný limit bol prekročený."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Aktualizujte cez mobilnú aplikáciu"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Nahrajte obrázok"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Nahrať logo faktúry"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Nahrať dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Nahrať súbor"),
        "userName": MessageLookupByLibrary.simpleMessage("Meno používateľa"),
        "userRole": MessageLookupByLibrary.simpleMessage("Rola používateľa"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Názov roly používateľa"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Titul používateľa"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("DPH / GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Overiť telefónne číslo"),
        "view": MessageLookupByLibrary.simpleMessage("Zobraziť"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Zákazník bez objednávky"),
        "warranty": MessageLookupByLibrary.simpleMessage("Záruka"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Záruka"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Pred začiatkom potrebujeme zaregistrovať vaše telefónne číslo!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Rozumieme dôležitosti bezproblémového fungovania. Preto je naša nepretržitá podpora k dispozícii na pomoc, či už ide o rýchlu otázku alebo komplexný problém. Kontaktujte nás kedykoľvek a kdekoľvek prostredníctvom hovoru alebo WhatsApp a zažite neprekonateľný zákaznícky servis."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Veľkoobchodná cena"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Veľkoobchodník"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Veľkoobchod"),
        "wight": MessageLookupByLibrary.simpleMessage("Hmotnosť"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Áno, vrátiť"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Musíte sa znova PRIHLÁSIŤ na svoj účet."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Pred kúpou správ musíte overiť svoju identitu"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Zoznam všetkých vašich predajov"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Všetky vaše predaje"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Používate"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Vaše záväzné predaje"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Pred kúpou správ musíte overiť svoju identitu"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Váš balík"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Vaša platba bola zrušená"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Vaša platba prebehla úspešne"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Vaša platba bola zrušená")
      };
}
