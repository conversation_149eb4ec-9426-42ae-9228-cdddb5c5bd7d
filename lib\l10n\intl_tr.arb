{"addNewUser": "<PERSON><PERSON>", "userRole": "Kullanıcı Rolü", "addNew": "<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revenue": "<PERSON><PERSON><PERSON>", "userName": "Kullanıcı Adı", "noUserFound": "Kullanıcı Bulunamadı", "all": "<PERSON><PERSON><PERSON>", "profileEdit": "<PERSON><PERSON>", "practies": "<PERSON><PERSON><PERSON><PERSON>", "salesList": "Satış <PERSON>esi", "enterPassword": "<PERSON><PERSON><PERSON>", "noUserRoleFound": "Kullanıcı Rolü Bulunamadı", "addUserRole": "Kullanıcı Rolü Ekle", "UserTitle": "Kullanıcı Başlığı", "enterUserTitle": "Kullanıcı başlığı girin", "userTitle": "Kullanıcı Başlığı", "addSuccessful": "Başarıyla Eklendi", "youHaveToRelogin": "Hesabınıza TEKRAR GİRİŞ yapmalısınız.", "ok": "<PERSON><PERSON>", "payCash": "<PERSON><PERSON><PERSON>", "freeLifeTimeUpdate": "Ücretsiz Ömür <PERSON>", "androidIOSAppSupport": "Android ve iOS U<PERSON>gulam<PERSON>", "premiumCustomerSupport": "Premium Müşteri Desteği", "customInvoiceBranding": "<PERSON><PERSON>", "unlimitedUsage": "Sınırs<PERSON><PERSON>", "freeDataBackup": "Ücretsiz Veri Yedekleme", "stayAtTheForFront": "Herhangi bir ek maliyet olmadan teknolojik ilerlemenin önünde kalın. Pos Saas POS Sınırsız Yükseltme işletmenizin her zaman en son araç<PERSON>a ve özelliklere sahip olduğunu garanti eder.", "weUnderStand": "Sorunsuz işlemlerin önemini anlıyoruz. Bu nedenle gece gündüz destek ekibimiz, hızlı bir soru veya kapsamlı bir endişe olup olmadığına bakılmaksızın size yardımcı olmaya hazır. Olağanüstü müşteri hizmeti deneyimi yaşamak için bize her zaman her yerden çağrı veya WhatsApp aracılığıyla ulaşın.", "unlockTheFull": "Pos Saas POS'un tam potansiyelini uzman ekibimizin liderliğindeki kişiselleştirilmiş eğitim oturumları ile açın. Temellerden ileri tekniklere kadar her yönünü optimize etmek için iyi bir şekilde bilgilendiğinizden emin oluyoruz.", "makeALastingImpression": "Markalı faturalarınızla müşterileriniz üzerinde kalıcı bir izlenim bırakın. Sınırsız Yükseltme, faturalarınızı özelleştirmenin benzersiz avantajını sunar ve marka kimliğinizi güçlendiren ve müşteri sadakati oluşturan profesyonel bir dokunuş ekler.", "theNameSysIt": "İsim her şeyi söylüyor. Pos Saas POS Unlimited ile kullanımınızda bir sınırlama yok. Birkaç işlemi işliyor olsanız da veya müşterilerin akınına uğruyorsanız, sınırlamalar tarafından kısıtlanmadığınızı bilerek güvenle işlem yapabilirsiniz.", "safegurardYourBusinessDate": "İşletmenizin verilerini kolayca koruyun. Pos Saas POS Unlimited Yükseltmesi, değerli bilgilerinizin beklenmedik olaylara karşı korunduğunu ücretsiz veri yedeklemeyi içerir. Gerçekten önemli olan şeye odaklanın - işletme büyümenize.", "buy": "Satın Al", "bankInformation": "Banka Bilgileri", "bankName": "Banka Adı", "branchName": "Şube Adı", "accountName": "<PERSON><PERSON><PERSON>", "accountNumber": "<PERSON><PERSON><PERSON>", "bankAccountingCurrecny": "Banka Hesap Para Birimi", "swiftCode": "SWIFT Kodu", "enterTransactionId": "İşlem Kimliği Girin", "uploadDocument": "<PERSON><PERSON>", "uploadFile": "<PERSON><PERSON><PERSON>", "aboutApp": "Uygulama Hakkında", "termsOfUse": "Kullanım Koşulları", "privacyPolicy": "Gizlilik Politikası", "userRoleName": "Kullanıcı Rol Adı", "enterUserRoleName": "Kullanıcı Rol Adı Girin", "yourPackage": "Paketiniz", "freePlan": "Ücretsiz Plan", "yourAreUsing": "Kullanıyorsunuz", "freePackage": "Ücretsiz Paket", "premiumPlan": "Premium Plan", "packageFeature": "<PERSON>et <PERSON>", "remaining": "Kalan: ", "unlimited": "Sınırsız", "forUnlimitedUses": "Sınırs<PERSON><PERSON>çin", "updateNow": "<PERSON><PERSON><PERSON>", "purchasePremiumPlan": "Premium Plan Satın Al", "stayAtTheForeFrontOfTechnological": "Herhangi bir ek maliyet olmadan teknolojik ilerlemenin önünde kalın. Pos Sass POS Sınırsız Yükseltme, işletmenizin her zaman en son ara<PERSON><PERSON><PERSON> ve özelliklere sahip olduğunu garanti eder.", "buyPremiumPlan": "Premium Plan Satın Al", "mobilePlusDesktop": "Mobil <PERSON>\n+\nMasaüstü", "transactionId": "İşlem Kimliği", "productStock": "<PERSON><PERSON><PERSON><PERSON>", "pleaseEnterProductStock": "Lütfen ürün stokunu girin", "increaseStock": "<PERSON><PERSON><PERSON><PERSON>", "areYouWantToDeleteThisProduct": "<PERSON>u <PERSON><PERSON><PERSON><PERSON><PERSON> silmek istiyor musunuz", "noConnection": "Bağlantı Yok", "pleaseCheckYourInternetConnectivity": "Lütfen İnternet Bağlantınızı Kontrol Edin", "tryAgain": "<PERSON><PERSON><PERSON>", "currency": "Para Birimi", "PosSaasLoginPanel": "Pos Saas Giriş Paneli", "posSaasSingUpPanel": "Pos Saas Kayıt Paneli", "businessCategory": "İş Kategorisi", "companyName": "Şirket Adı", "enterYourCompanyName": "Şirket Adınızı Girin", "phoneNumber": "Telefon Numarası", "enterYourPhoneNumber": "Telefon Numaranızı Girin", "shopOpeningBalance": "Mağaza Açılış Bakiyesi", "enterYOurAmount": "Tutarı Giriniz", "continu": "<PERSON><PERSON> et", "resetYourPassword": "Şifrenizi Sıfırlayın", "email": "E-posta", "enterYourEmailAddress": "E-posta Adresinizi <PERSON>", "pleaseDownloadOurMobileApp": "Lütfen mobil uygulamamızı indirin ve masaüstü sürümünü kullanmak için bir pakete abone olun", "mobiPosLoginPanel": "Pos Saas Giriş Paneli", "enterYourPassword": "Şifrenizi Girin", "login": "Oturum aç", "password": "Şifre", "forgotPassword": "Parolanızı mı unuttunuz?", "registration": "<PERSON><PERSON><PERSON>", "editYourProfile": "Profilinizi <PERSON>", "uploadAImage": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "orDragAndDropPng": "<PERSON><PERSON>, JPG sürükleyip bırakın", "comapnyName": "Şirket Adı", "enterYourCompanyNames": "Şirket Adınızı Girin", "address": "<PERSON><PERSON>", "enterYourAddress": "<PERSON><PERSON><PERSON><PERSON>", "mobiPosSignUpPane": "Pos Saas Kayıt Paneli", "confirmPassword": "<PERSON><PERSON><PERSON>", "enterYourPasswordAgain": "Şifrenizi Tekrar Girin", "alreadyHaveAnAccounts": "Zaten bir hesabınız var mı?", "choseAplan": "Bir plan seçin", "allBasicFeatures": "<PERSON><PERSON><PERSON>", "unlimitedInvoice": "Sınırsız Fatura", "getStarted": "Başla", "currentPlan": "Geçerli Plan", "selectYourLanguage": "<PERSON><PERSON><PERSON>", "shopName": "Mağaza Adı", "enterYourShopName": "Mağaza Adınızı Girin", "phoneVerification": "Telefon Doğrulama", "weNeedToRegisterYourPhone": "Başlamadan önce telefonunuzu kaydetmemiz gerekiyor!", "verifyPhoneNumber": "Telefon Numarasını Doğrula", "customerName": "Müşteri Adı", "enterCustomerName": "Müşteri Adını Girin", "openingBalance": "Açılış Bakiyesi", "enterOpeningBalance": "Açılış Bakiyesini Girin", "type": "Tip", "cancel": "İptal", "saveAndPublish": "<PERSON><PERSON> ve Yayınla", "customerList": "Müşteri <PERSON>esi", "searchByNameOrPhone": "Ad veya Telefonla Ara...", "addCustomer": "Müşter<PERSON>", "partyName": "Parti Adı", "partyType": "Parti Tipi", "phone": "Telefon", "due": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "areYouWantToDeleteThisCustomer": "Bu Müşteriyi Silmek İstiyor musunuz?", "thisCustomerHavepreviousDue": "<PERSON>u müş<PERSON>inin önceki bor<PERSON> var", "noCustomerFound": "Müşteri Bulunamadı", "totalDue": "Toplam Borç", "customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supplier": "<PERSON><PERSON><PERSON>", "collectDue": "Toplam Borç >", "noDueTransantionFound": "Borç İşlemi Bulunamadı", "createPayment": "Ö<PERSON>me <PERSON>", "grandTotal": "<PERSON><PERSON>", "payingAmount": "Ödenen <PERSON>", "enterPaidAmount": "Ödenen tutarı girin", "changeAmount": "Para Üstü", "dueAmount": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "enterExpanseCategory": "Gider Kategorisi Giriniz", "pleaseEnterValidData": "Lütfen geçerli veri girin", "categoryName": "<PERSON><PERSON><PERSON>", "entercategoryName": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "addDescription": "Açıklama Ekle....", "expensecategoryList": "<PERSON><PERSON>", "searchByInvoice": "Fatura ile Ara....", "addCategory": "<PERSON><PERSON><PERSON>", "action": "Aksiyon", "noExpenseCategoryFound": "Gider Kategorisi Bulunamadı", "expenseDetails": "Gider Detayları", "date": "<PERSON><PERSON><PERSON>", "name": "Ad", "category": "<PERSON><PERSON><PERSON>", "referenceNo": "Referans No.", "amount": "<PERSON><PERSON>", "note": "Not", "nam": "Ad*", "income": "<PERSON><PERSON><PERSON>", "addUpdateExpenseList": "Gider Listesini Ekle/Gü<PERSON>lle", "expenseDate": "<PERSON><PERSON>", "enterExpenseDate": "<PERSON><PERSON>", "expenseFor": "Gider İçin", "enterName": "<PERSON>", "referenceNumber": "<PERSON><PERSON><PERSON>", "enterReferenceNumber": "Referans Numarası Girin", "enterNote": "Not Girin", "between": "Arasında", "to": "İçine", "totalExpense": "Toplam Gider", "totalSales": "Toplam Satış", "purchase": "Alış", "newCustomers": "<PERSON><PERSON>", "dailySales": "Günlük Satışlar", "dailyCollection": "Günlük Tahsilât", "instantPrivacy": "Anında Giz<PERSON>", "stockInventory": "St<PERSON> Envanter", "stockValue": "Stok Değeri", "lowStocks": "Düşük Stoklar", "other": "<PERSON><PERSON><PERSON>", "otherIncome": "<PERSON><PERSON><PERSON>", "MOBIPOS": "<PERSON><PERSON>", "newCusotmers": "<PERSON><PERSON>", "enterIncomeCategory": "<PERSON><PERSON><PERSON>", "pleaseentervaliddata": "Lütfen geçerli veri girin", "saveAndPublished": "<PERSON><PERSON> ve Yayınla", "incomeCategoryList": "<PERSON><PERSON><PERSON>", "noIncomeCategoryFound": "<PERSON><PERSON><PERSON>ulunamadı", "incomeDetails": "<PERSON><PERSON><PERSON>", "paymentTypes": "<PERSON><PERSON><PERSON>", "totalIncome": "<PERSON><PERSON>", "incomeList": "<PERSON><PERSON><PERSON>", "incomeCategory": "<PERSON><PERSON><PERSON>", "newIncome": "<PERSON><PERSON>", "createdBy": "Oluşturan", "view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "noIncomeFound": "<PERSON><PERSON><PERSON>", "addUpdateIncomeList": "<PERSON><PERSON><PERSON>/Gü<PERSON>", "incomeDate": "<PERSON><PERSON><PERSON>", "enterIncomeDate": "<PERSON><PERSON><PERSON>", "incomeFor": "<PERSON><PERSON><PERSON>", "enterNames": "<PERSON><PERSON><PERSON>", "enterAmount": "Tutarı Giriniz", "printInvoice": "<PERSON><PERSON>", "moneyReciept": "Para Makbuzu", "billTo": "Fatura:", "invoiceNo": "Fatura No.", "totalDues": "Toplam Borç", "paidAmount": "Ödenen <PERSON>", "remainingDue": "<PERSON><PERSON>", "deliveryCharge": "<PERSON><PERSON><PERSON><PERSON>", "INVOICE": "FATURA", "product": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "unitPrice": "<PERSON><PERSON><PERSON>", "totalPrice": "Toplam Fiyat", "subTotal": "<PERSON>", "totalVat": "Toplam KDV", "totalDiscount": "Toplam İndirim", "payable": "Ödenecek", "paid": "Ödendi", "serviceCharge": "<PERSON>z<PERSON>", "totalSale": "Toplam Satış", "totalPurchase": "Toplam Alış", "recivedAmount": "Alınan Tutar", "customerDue": "Müşteri Borcu", "supplierDue": "Tedarikçi Borcu", "selectParties": "Tarafları Seç", "details": "Detaylar >", "show": "Göster >", "noTransactionFound": "İşlem Bulunamadı", "ledgeDetails": "<PERSON><PERSON><PERSON>", "status": "Durum", "itemName": "<PERSON><PERSON><PERSON><PERSON>", "purchasePrice": "Alış Fiyatı", "salePrice": "Satış Fiyatı", "profit": "<PERSON><PERSON>", "loss": "Zara<PERSON>", "total": "toplam", "totalProfit": "<PERSON><PERSON>", "totalLoss": "<PERSON>lam Zarar", "unPaid": "Ödenmemiş", "lossOrProfit": "Kâr/Zarar", "saleAmount": "Satış Tutarı", "profitPlus": "Kar(+)", "profitMinus": "<PERSON><PERSON>(-)", "yourPaymentIsCancelled": "Ödemeniz i<PERSON>l edildi", "yourPaymentIsSuccessfully": "Ödemeniz başarıyla gerçekleşti", "hold": "<PERSON><PERSON>", "holdNumber": "<PERSON><PERSON>", "selectSerialNumber": "Seri Numarasını Seç", "serialNumber": "Ser<PERSON> Numa<PERSON>ı", "searchSerialNumber": "Seri Numarasını Ara", "noSerialNumberFound": "Seri Numarası Bulunamadı", "nameCodeOrCateogry": "Ad, Kod veya Kategori", "vatOrgst": "KDV/GST", "discount": "İndirim", "areYouWantToCreateThisQuation": "<PERSON>u <PERSON>kli<PERSON>mak İstiyor musunuz?", "updateYourPlanFirst": "Planınızı önce güncelleyin\\nSatış Limiti aşıldı.", "quotation": "<PERSON><PERSON><PERSON><PERSON>", "addProduct": "<PERSON><PERSON><PERSON><PERSON>", "totalProduct": "Toplam Ürün", "shpingOrServices": "Nakliye/Hizmet", "addItemCategory": "<PERSON><PERSON><PERSON><PERSON>", "selectVariations": "Varyasyonları Seç:", "size": "<PERSON><PERSON>", "color": "Renk", "wight": "Ağırlık", "capacity": "Kapasite", "warranty": "<PERSON><PERSON><PERSON>", "addBrand": "<PERSON><PERSON>", "brandName": "<PERSON><PERSON>", "enterBrandName": "Marka Adını Girin", "addUnit": "<PERSON><PERSON><PERSON>", "unitName": "<PERSON><PERSON><PERSON>", "enterUnitName": "<PERSON><PERSON><PERSON>", "productNam": "<PERSON><PERSON><PERSON><PERSON>*", "enterProductName": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "productType": "<PERSON><PERSON><PERSON><PERSON>", "enterProductType": "<PERSON><PERSON><PERSON><PERSON>", "productWaranty": "<PERSON><PERSON><PERSON><PERSON>", "enterWarranty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warrantys": "<PERSON><PERSON><PERSON>", "selectWarrantyTime": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "selectProductBrand": "<PERSON><PERSON>ün <PERSON>ını Seç", "productCod": "<PERSON><PERSON><PERSON><PERSON>*", "enterProductCode": "<PERSON><PERSON><PERSON><PERSON>", "enterProductQuantity": "<PERSON>rün Miktarını Girin", "Quantity": "<PERSON><PERSON><PERSON>*", "productUnit": "<PERSON><PERSON><PERSON><PERSON>", "enterPurchasePrice": "Alış Fiyatını Girin", "salePrices": "Satış Fiyatı*", "dealerPrice": "<PERSON><PERSON>", "enterDealePrice": "<PERSON><PERSON> Fi<PERSON>tını Girin", "wholeSaleprice": "Toptan Satış Fiyatı", "enterPrice": "Fiyatı Girin", "manufacturer": "Üretici", "enterManufacturerName": "Üretici Adı Girin", "serialNumbers": "Ser<PERSON> Numa<PERSON>ı", "enterSerialNumber": "Seri Numarasını Girin", "nosSerialNumberFound": "Seri Numarası Bulunamadı", "productList": "<PERSON><PERSON><PERSON><PERSON>", "searchByName": "<PERSON><PERSON><PERSON>", "retailer": "Perakende<PERSON>", "dealer": "Bay<PERSON>", "wholesale": "Toptan", "expense": "<PERSON><PERSON>", "totalPayable": "Toplam Ödenecek", "totalAmount": "Toplam Tutar", "searchByInvoiceOrName": "<PERSON>ura veya isim ile ara", "invoice": "<PERSON><PERSON>", "lossminus": "<PERSON><PERSON><PERSON>(-)", "yourPaymentIscancelled": "Ödemeniz i<PERSON>l edildi", "previousDue": "<PERSON><PERSON><PERSON>:", "calculator": "<PERSON><PERSON><PERSON>:", "dashBoard": "Gösterge Paneli", "price": "<PERSON><PERSON><PERSON>", "create": "Oluştur", "payment": "Ödeme", "enterPayingAmount": "Ödenecek Tutarı Giriniz", "enterCategoryName": "Kategori Adını Giriniz", "productSize": "<PERSON><PERSON><PERSON><PERSON>", "enterProductSize": "<PERSON><PERSON><PERSON><PERSON>", "productColor": "<PERSON><PERSON><PERSON><PERSON>", "enterProductColor": "<PERSON><PERSON><PERSON><PERSON>", "productWeight": "Ürün <PERSON>ı", "enterProductWeight": "Ürün Ağırlığını Giriniz", "productcapacity": "<PERSON><PERSON><PERSON><PERSON>", "enterProductCapacity": "<PERSON><PERSON><PERSON><PERSON>", "enterSalePrice": "Satış Fiyatını Giriniz", "add": "<PERSON><PERSON>", "productCategory": "<PERSON><PERSON><PERSON><PERSON>", "enterProductUnit": "<PERSON><PERSON><PERSON><PERSON>", "productName": "<PERSON><PERSON><PERSON><PERSON>", "noProductFound": "Ürün Bulunamadı", "addingSerialNumber": "Seri Numarası Ekleniyor mu?", "unit": "<PERSON><PERSON><PERSON>", "editOrAddSerial": "<PERSON><PERSON>/Ekle:", "enterWholeSalePrice": "Toptan Fiyatını Giriniz", "invoiceCo": "Fatura:", "categories": "<PERSON><PERSON><PERSON>", "purchaseList": "<PERSON><PERSON><PERSON>", "print": "Yazdır", "noPurchaseTransactionFound": "Satın alma işlemi bulunamadı", "quotationList": "<PERSON><PERSON><PERSON><PERSON>", "areYouWantToDeleteThisQuotion": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ek İstiyor musunuz?", "convertToSale": "Satışa Dönüştür", "noQuotionFound": "<PERSON><PERSON><PERSON><PERSON>", "stockReport": "Stok Raporu", "PRODUCTNAME": "ÜRÜN ADI", "CATEGORY": "KATEGORİ", "PRICE": "FİYAT", "QTY": "ADET", "STATUS": "DURUMU", "TOTALVALUE": "TOPLAM DEĞER", "noReportFound": "<PERSON><PERSON>", "remainingBalance": "<PERSON><PERSON>", "totalpaymentIn": "Toplam Ödeme <PERSON>", "totalPaymentOut": "Toplam Ödeme Çıkışı", "dailyTransaction": "Günlük İşlem", "paymentIn": "<PERSON><PERSON><PERSON>", "paymentOut": "Ödeme Ç<PERSON>ışı", "balance": "Bakiye", "totalPaid": "Toplam Ödenen", "dueTransaction": "<PERSON><PERSON>", "downloadPDF": "PDF İndir", "customerType": "Müşteri Tipi", "pleaseAddCustomer": "Lütfen Müşteri Ekleyin", "purchaseTransaction": "Satın <PERSON>", "printPdf": "PDF Yazdır", "saleTransactionQuatationHistory": "Satış İşlemleri (Teklif Satış Geçmişi)", "ADDSALE": "SATIŞ EKLE", "search": "Ara....", "transactionReport": "İşlem Raporu", "saleTransaction": "Satış İşlemi", "totalReturns": "<PERSON><PERSON>", "totalReturnAmount": "Toplam İade Tutarı", "saleReturn": "Satış İadesi", "noSaleTransaactionFound": "Satış İşlemi Bulunamadı", "saleList": "Satış <PERSON>esi", "reports": "<PERSON><PERSON><PERSON>", "areYouWantToReturnThisSale": "Bu satışı iade etmek istiyor musunuz?", "no": "Hay<PERSON><PERSON>", "yesReturn": "<PERSON><PERSON>", "setting": "<PERSON><PERSON><PERSON>", "uploadAnInvoiceLogo": "<PERSON><PERSON>", "showLogoInInvoice": "Faturada Logo Gösterilsin mi?", "logoPositionInInvoice": "Faturada Logo Pozisyonu?", "left": "Sol", "right": "Sağ", "companyAddress": "Şirket Adresi", "enterYourCompanyAddress": "Şirket Adresinizi Girin", "companyPhoneNumber": "Şirket Telefon Numarası", "companyEmailAddress": "Şirket E-posta Adresi", "enterCompanyPhoneNumber": "Şirket Telefon Numaranızı Girin", "enterCompanyEmailAddress": "Şirket E-posta Adresinizi Girin", "companyWebsiteUrl": "Şirket Web Sitesi URL'si", "enterCompanyWebsiteUrl": "Şirket Web Sitesi URL'nizi Girin", "companyDescription": "Şirket Tanımı", "enterCompanyDesciption": "Şirket Tanımını Girin", "saveChanges": "Değişiklikleri Kaydet", "kycVerification": "KYC Doğrulama", "identityVerify": "Kimlik Doğrulama", "yourNeedToIdentityVerify": "Mesaj satın almadan önce kimlik doğrulama yapmanız gerekir", "govermentId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "takeADriveLisense": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ulusal kimlik kartı veya pasaport fotoğrafı çekin", "addDucument": "<PERSON><PERSON>", "youNeedToIdentityVerifySms": "Mesaj satın almadan önce kimlik doğrulama yapmanız gerekir", "wholeSeller": "Toptan Satıcı", "enterSmsContent": "<PERSON><PERSON>", "sendMessage": "<PERSON><PERSON>", "buySms": "SMS Satın Al", "supplierList": "Tedarikçi Listesi", "addSupplier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noSupplierFound": "Tedarikçi Bulunamadı", "checkWarranty": "Garantiye Bak", "customerInvoices": "Müşteri Faturaları", "supplierInvoice": "Tedarikçi Faturası", "addItem": "<PERSON><PERSON><PERSON>", "noInvoiceFound": "Fatura Bulunamadı", "stock": "Stok", "enterStockAmount": "Stok Miktarını Girin", "discountPrice": "İndirim Fiyatı", "enterDiscountPrice": "İndirim Fiyatını Girin", "dateTime": "<PERSON><PERSON><PERSON> ve <PERSON>", "walkInCustomer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş Müşteri", "saleDetails": "Satış Detayları", "customerWalkIncostomer": "Müşteri: <PERSON><PERSON><PERSON><PERSON><PERSON>n Müşteri", "item": "<PERSON><PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "totalItem2": "Toplam Öğe: 2", "shipingOrOther": "Nakliye/Diğer", "yourDueSales": "Mevcut Satışlarınız", "yourAllSales": "<PERSON><PERSON>m Satışlarınız", "invoiceHint": "Fatura NO..", "customer": "Müş<PERSON>i", "dueAmountWillShowHere": "Mevcut tutar burada gösterilecek", "thisCustmerHasNoDue": "<PERSON><PERSON> mü<PERSON><PERSON><PERSON><PERSON> borcu yok", "pleaseSelectACustomer": "Lütfen bir müşteri seçiniz", "pleaseAddASale": "Lütfen bir satış e<PERSON>in", "yourAllSaleList": "<PERSON><PERSON><PERSON>", "changeableAmount": "Değiştirileb<PERSON><PERSON>", "sales": "Satışlar", "dueList": "Mevcut Liste", "ledger": "<PERSON><PERSON><PERSON>", "transaction": "İşlem", "subciption": "Abonelik", "upgradeOnMobileApp": "Mobil Uygulamada Yükseltme", "POSSale": "POS Satış", "searchAnyThing": "Herhangi Bir Şeyi Ara...", "sale": "Satış", "logOut": "<PERSON><PERSON><PERSON><PERSON>", "cashAndBank": "<PERSON><PERSON><PERSON> ve <PERSON>a", "cashInHand": "<PERSON><PERSON><PERSON>", "bankAccounts": "Banka Hesapları", "creativeHub": "Yaratıcı Hub", "openCheques": "Açık <PERSON>", "loanAccounts": "<PERSON><PERSON><PERSON>", "share": "Paylaş", "preview": "<PERSON><PERSON><PERSON><PERSON>", "dueCollection": "Mevcut Tahsilatı", "customerOfTheMonth": "<PERSON><PERSON><PERSON><PERSON>", "topSellingProduct": "En Çok Satan Ürün", "statistic": "İstatistik", "stockValues": "Stok Değerleri", "lowStock": "Düşük Stok", "fivePurchase": "Ayın en iyi beş satın alınan ürünü", "recentSale": "<PERSON>", "tSale": "Toplam Satış", "sAmount": "Satış Tutarı", "expenses": "<PERSON><PERSON><PERSON>", "inc": "<PERSON><PERSON><PERSON>", "prof": "Profil"}