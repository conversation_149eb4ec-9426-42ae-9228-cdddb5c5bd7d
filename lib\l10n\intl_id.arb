{"addNewUser": "Tambah Pengguna Baru", "userRole": "<PERSON><PERSON>", "addNew": "Tambah Baru", "orders": "<PERSON><PERSON><PERSON>", "revenue": "Pendapatan", "userName": "<PERSON><PERSON>", "noUserFound": "Tidak Ada Pengguna Ditemukan", "all": "<PERSON><PERSON><PERSON>", "profileEdit": "Sunting Profil", "practies": "Praktik", "salesList": "<PERSON><PERSON><PERSON>", "enterPassword": "<PERSON><PERSON><PERSON><PERSON>", "noUserRoleFound": "Tidak Ada Peran <PERSON> Ditemukan", "addUserRole": "Tambah Peran <PERSON>", "UserTitle": "<PERSON><PERSON><PERSON>", "enterUserTitle": "Ma<PERSON>kkan judul pengguna", "userTitle": "<PERSON><PERSON><PERSON>", "addSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "youHaveToRelogin": "<PERSON><PERSON> harus LOGOUT dan LOGIN kembali ke akun Anda.", "ok": "<PERSON>e", "payCash": "<PERSON>ar <PERSON>", "freeLifeTimeUpdate": "<PERSON><PERSON><PERSON><PERSON> G<PERSON>", "androidIOSAppSupport": "Dukungan Aplikasi Android & iOS", "premiumCustomerSupport": "Dukungan Pelanggan Premium", "customInvoiceBranding": "Pembranding Faktur Kustom", "unlimitedUsage": "<PERSON><PERSON><PERSON><PERSON>", "freeDataBackup": "Pencadangan Data Gratis", "stayAtTheForFront": "Tetap di garis depan perkembangan teknologi tanpa biaya tambahan. Pembaruan Pos Saas POS Tanpa Batas kami memastikan Anda selalu memiliki alat dan fitur terbaru di ujung jari <PERSON>, menjamin bisnis Anda tetap berada di garis depan.", "weUnderStand": "<PERSON>mi memahami pentingnya operasi yang lancar. <PERSON><PERSON><PERSON> mengapa dukungan kami sepanjang waktu tersedia untuk membantu Anda, baik itu pertanyaan cepat atau masalah komprehensif. <PERSON><PERSON><PERSON><PERSON> kami kapan saja, di mana saja melalui panggilan atau WhatsApp untuk mengalami layanan pelanggan yang tak tertandingi.", "unlockTheFull": "<PERSON><PERSON> potensi penuh Pos Saas POS dengan sesi pelatihan yang dipersonalisasi yang dipimpin oleh tim ahli kami. <PERSON><PERSON> dari dasar hingga teknik lanjutan, kami memastikan Anda terampil dalam memanfaatkan setiap aspek sistem untuk mengoptimalkan proses bisnis Anda.", "makeALastingImpression": "Buat kesan yang abadi pada pelanggan Anda dengan faktur bermerk. Pembaruan Tanpa Batas kami menawarkan keuntungan unik dalam menyesuaikan faktur <PERSON>, menambahkan sentuhan profesional yang memperkuat identitas merek Anda dan memupuk loyalitas pelanggan.", "theNameSysIt": "<PERSON>a men<PERSON> semua. Dengan Pos Saas POS Tanpa Batas, tidak ada batasan penggunaan. Baik Anda memproses sejumlah transaksi atau menghadapi lonja<PERSON> p<PERSON>, <PERSON><PERSON> da<PERSON>t beroperasi dengan percaya diri, tahu Anda tidak dibatasi oleh batasan.", "buy": "Bel<PERSON>", "bankInformation": "Informasi Bank", "bankName": "Nama Bank", "branchName": "<PERSON><PERSON>", "accountName": "<PERSON><PERSON>", "accountNumber": "<PERSON><PERSON>", "bankAccountingCurrecny": "Mata Uang Akun Bank", "swiftCode": "Kode SWIFT", "enterTransactionId": "Masukkan ID Transaksi", "uploadDocument": "Unggah Dokumen", "uploadFile": "<PERSON><PERSON><PERSON>", "aboutApp": "Tentang Aplikasi", "termsOfUse": "<PERSON><PERSON><PERSON>", "privacyPolicy": "<PERSON><PERSON><PERSON><PERSON>", "userRoleName": "<PERSON><PERSON>", "enterUserRoleName": "<PERSON><PERSON><PERSON><PERSON>", "yourPackage": "<PERSON><PERSON>", "freePlan": "<PERSON><PERSON>", "yourAreUsing": "<PERSON><PERSON>", "freePackage": "<PERSON><PERSON>", "premiumPlan": "Paket Premium", "packageFeature": "<PERSON><PERSON>", "remaining": "Sisa: ", "unlimited": "Tak Terbatas", "forUnlimitedUses": "Untuk Penggunaan Tak Terbatas", "updateNow": "<PERSON><PERSON><PERSON>", "purchasePremiumPlan": "Beli Paket Premium", "stayAtTheForeFrontOfTechnological": "Tetap di garis depan perkembangan teknologi tanpa biaya tambahan. Pembaruan Pos Saas POS Tanpa Batas kami memastikan Anda selalu memiliki alat dan fitur terbaru di ujung jari <PERSON>, menjamin bisnis Anda tetap berada di garis depan.", "buyPremiumPlan": "Beli Paket Premium", "mobilePlusDesktop": "Aplikasi Seluler\n+\nDesktop", "transactionId": "ID Transaksi", "productStock": "Stok Produk", "pleaseEnterProductStock": "<PERSON><PERSON><PERSON> masukkan stok produk", "increaseStock": "Tambah Stok", "areYouWantToDeleteThisProduct": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghapus produk ini", "noConnection": "Tidak Ada Koneksi", "pleaseCheckYourInternetConnectivity": "Harap Perik<PERSON>i Internet Anda", "tryAgain": "<PERSON><PERSON>", "currency": "<PERSON>", "PosSaasLoginPanel": "<PERSON><PERSON> Sa<PERSON> Login panel", "posSaasSingUpPanel": "Pos Saas SingUp Panel", "safegurardYourBusinessDate": "Lindungi data bisnis Anda dengan mudah. Peningkatan Tidak Terbatas Pos Saas POS kami mencakup pencadangan data gratis, memastikan informasi berharga Anda terlindungi dari kejadian tak terduga. Fokus pada hal yang benar-benar penting – pertumbuhan bisnis Anda.", "businessCategory": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "enterYourCompanyName": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Nomor Telepon", "enterYourPhoneNumber": "Ma<PERSON>kkan nomor telepon Anda", "shopOpeningBalance": "<PERSON><PERSON>", "enterYOurAmount": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "continu": "Lanjutkan", "resetYourPassword": "Atur Ulang Kata Sandi Anda", "email": "<PERSON><PERSON>", "enterYourEmailAddress": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "pleaseDownloadOurMobileApp": "Harap unduh aplikasi seluler kami dan berlangganan paket untuk menggunakan versi desktop", "mobiPosLoginPanel": "Panel Masuk <PERSON> Sa<PERSON>", "enterYourPassword": "<PERSON><PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "forgotPassword": "Lupa Kata Sandi?", "registration": "Pendaftaran", "editYourProfile": "Edit profil <PERSON><PERSON>", "uploadAImage": "Unggah gambar", "orDragAndDropPng": " atau seret & letakkan PNG, JPG", "comapnyName": "<PERSON><PERSON>", "enterYourCompanyNames": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "enterYourAddress": "<PERSON><PERSON><PERSON><PERSON>", "mobiPosSignUpPane": "Panel Pendaftaran Pos Saas", "confirmPassword": "Konfirmasi kata sandi", "enterYourPasswordAgain": "<PERSON><PERSON><PERSON>n kata sandi Anda lagi", "alreadyHaveAnAccounts": "Sudah memiliki akun?", "choseAplan": "<PERSON><PERSON><PERSON>", "allBasicFeatures": "<PERSON><PERSON><PERSON>", "unlimitedInvoice": "<PERSON><PERSON><PERSON>", "getStarted": "<PERSON><PERSON>", "currentPlan": "Rencana Sa<PERSON> Ini", "selectYourLanguage": "<PERSON><PERSON><PERSON> bahasa <PERSON>", "shopName": "<PERSON><PERSON>", "enterYourShopName": "<PERSON><PERSON><PERSON><PERSON>", "phoneVerification": "Verifikasi Nomor Telepon", "weNeedToRegisterYourPhone": "Kami perlu mendaftarkan nomor telepon Anda sebelum memulai!", "verifyPhoneNumber": "Verifikasi Nomor Telepon", "customerName": "<PERSON><PERSON>", "enterCustomerName": "<PERSON><PERSON><PERSON><PERSON>", "openingBalance": "<PERSON><PERSON>", "enterOpeningBalance": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipe", "cancel": "<PERSON><PERSON>", "saveAndPublish": "Simpan & Terbitkan", "customerList": "<PERSON><PERSON><PERSON>", "searchByNameOrPhone": "<PERSON><PERSON> be<PERSON> atau Telepon...", "addCustomer": "Tambah Pelanggan", "partyName": "<PERSON><PERSON>", "partyType": "Tipe Partai", "phone": "Telepon", "due": "Tertunggak", "edit": "Edit", "delete": "Hapus", "areYouWantToDeleteThisCustomer": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghapus <PERSON> ini?", "thisCustomerHavepreviousDue": "Pelanggan ini memiliki tunggakan sebelumnya", "noCustomerFound": "Tidak Ada Pelanggan Ditemukan", "totalDue": "Total Jatuh Tempo", "customers": "Pelanggan", "supplier": "Pemasok", "collectDue": "Pungut Jatuh Tempo >", "noDueTransantionFound": "Tidak Ada Transaksi Jatuh Tempo Ditemukan", "createPayment": "B<PERSON>t <PERSON>", "grandTotal": "Total Keseluruhan", "payingAmount": "<PERSON><PERSON><PERSON>", "enterPaidAmount": "<PERSON><PERSON><PERSON><PERSON> jumlah yang <PERSON>ar", "changeAmount": "<PERSON><PERSON><PERSON>", "dueAmount": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "enterExpanseCategory": "<PERSON><PERSON><PERSON><PERSON>", "pleaseEnterValidData": "Silakan masukkan data yang valid", "categoryName": "<PERSON><PERSON>", "entercategoryName": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "addDescription": "Tambahkan deskripsi....", "expensecategoryList": "<PERSON><PERSON><PERSON>", "searchByInvoice": "<PERSON>i berda<PERSON> faktur....", "addCategory": "Tambah Kategori", "action": "<PERSON><PERSON><PERSON>", "noExpenseCategoryFound": "Tidak Ada Kategori <PERSON>", "expenseDetails": "Det<PERSON>", "date": "Tanggal", "name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "referenceNo": "<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "note": "Catatan", "nam": "<PERSON>a*", "income": "Pendapatan", "addUpdateExpenseList": "Tambah/Perbarui <PERSON>", "expenseDate": "<PERSON><PERSON>", "enterExpenseDate": "<PERSON><PERSON><PERSON><PERSON>", "expenseFor": "Pengel<PERSON><PERSON>", "enterName": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON>", "enterReferenceNumber": "<PERSON><PERSON><PERSON><PERSON>", "enterNote": "Masukkan Catatan", "between": "<PERSON><PERSON>", "to": "<PERSON><PERSON><PERSON>", "totalExpense": "Total Pengeluaran", "totalSales": "Total Penjualan", "purchase": "Pembelian", "newCustomers": "Pelanggan Baru", "dailySales": "<PERSON><PERSON><PERSON>", "dailyCollection": "<PERSON><PERSON><PERSON><PERSON>", "instantPrivacy": "Privasi Instan", "stockInventory": "Inventaris Stok", "stockValue": "<PERSON><PERSON>", "lowStocks": "Stok Rendah", "other": "<PERSON><PERSON><PERSON>", "otherIncome": "Pendapatan <PERSON>", "MOBIPOS": "<PERSON><PERSON>", "newCusotmers": "Pelanggan Baru", "enterIncomeCategory": "<PERSON><PERSON><PERSON><PERSON>", "pleaseentervaliddata": "Silakan masukkan data yang valid", "saveAndPublished": "Simpan & Terbitkan", "incomeCategoryList": "<PERSON>ftar <PERSON>", "noIncomeCategoryFound": "Tidak Ada Kategori Pendapatan Ditemukan", "incomeDetails": "Detail Pendapatan", "paymentTypes": "<PERSON><PERSON>", "totalIncome": "Total Pendapatan", "incomeList": "Daftar Pendapatan", "incomeCategory": "<PERSON><PERSON><PERSON>", "newIncome": "Pendapatan Baru", "createdBy": "Dibuat oleh", "view": "Lihat", "noIncomeFound": "Tidak Ada Pendapatan Ditemukan", "addUpdateIncomeList": "Tambah/Perbarui <PERSON>ftar Pendapatan", "incomeDate": "Tanggal Pendapatan", "enterIncomeDate": "<PERSON><PERSON><PERSON><PERSON>gal Pendapatan", "incomeFor": "Pendapatan Untuk", "enterNames": "<PERSON><PERSON><PERSON><PERSON>", "enterAmount": "<PERSON><PERSON><PERSON><PERSON>", "printInvoice": "Cetak Faktur", "moneyReciept": "<PERSON><PERSON><PERSON>", "billTo": "Tagihan kepada:", "invoiceNo": "<PERSON><PERSON>", "totalDues": "Total Hutang", "paidAmount": "<PERSON><PERSON><PERSON>", "remainingDue": "<PERSON><PERSON>", "deliveryCharge": "<PERSON><PERSON><PERSON>", "INVOICE": "FAKTUR", "product": "Produk", "unitPrice": "<PERSON><PERSON>", "totalPrice": "<PERSON><PERSON>", "subTotal": "Subtotal", "totalVat": "Total PPN", "totalDiscount": "Total Diskon", "payable": "<PERSON><PERSON><PERSON> ya<PERSON>", "paid": "<PERSON><PERSON><PERSON>", "serviceCharge": "<PERSON><PERSON><PERSON>", "totalSale": "Total Penjualan", "totalPurchase": "Total Pembelian", "receivedAmount": "<PERSON><PERSON><PERSON>", "customerDue": "<PERSON><PERSON><PERSON>", "supplierDue": "<PERSON><PERSON><PERSON>", "selectParties": "<PERSON><PERSON><PERSON>", "details": "Detail >", "show": "<PERSON><PERSON><PERSON><PERSON> >", "noTransactionFound": "Tidak Ada Transaksi Ditemukan", "ledgeDetails": "Detail Buku Besar", "status": "Status", "itemName": "<PERSON><PERSON>", "purchasePrice": "<PERSON><PERSON>", "salePrice": "<PERSON><PERSON>", "profit": "<PERSON><PERSON><PERSON><PERSON>", "loss": "Kerugian", "total": "total", "totalProfit": "Total Keuntungan", "totalLoss": "Total Kerugian", "unPaid": "<PERSON><PERSON>", "lossOrProfit": "Kerugian/Keuntungan", "saleAmount": "<PERSON><PERSON><PERSON>", "profitPlus": "<PERSON><PERSON><PERSON><PERSON>(+)", "profitMinus": "<PERSON><PERSON><PERSON><PERSON>(-)", "yourPaymentIsCancelled": "Pembayaran <PERSON>", "yourPaymentIsSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "hold": "<PERSON><PERSON>", "holdNumber": "<PERSON><PERSON>", "selectSerialNumber": "<PERSON><PERSON><PERSON>", "serialNumber": "<PERSON><PERSON>", "searchSerialNumber": "<PERSON><PERSON>", "nameCodeOrCategory": "<PERSON><PERSON> atau <PERSON> atau Kategori", "vatOrgst": "Pa<PERSON> (VAT)/Pajak Penjualan Barang dan <PERSON> (GST)", "discount": "Diskon", "areYouWantToCreateThisQuation": "<PERSON><PERSON><PERSON><PERSON> Anda ingin membuat <PERSON> ini?", "updateYourPlanFirst": "<PERSON><PERSON><PERSON> rencana Anda terlebih dahulu.\\nBatas Penjualan habis.", "quotation": "Penawaran", "addProduct": "Tambah Produk", "totalProduct": "Total Produk", "shpingOrServices": "Pengiriman/Layanan", "addItemCategory": "Tambah Kategori Barang", "selectVariations": "<PERSON><PERSON><PERSON>:", "size": "Ukuran", "color": "<PERSON><PERSON>", "wight": "<PERSON><PERSON>", "capacity": "Kapasitas", "warranty": "<PERSON><PERSON><PERSON>", "addBrand": "Tambah Merek", "brandName": "<PERSON><PERSON>", "enterBrandName": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "Tambah Satuan", "unitName": "<PERSON><PERSON>", "enterUnitName": "<PERSON><PERSON><PERSON><PERSON>", "enterProductName": "<PERSON><PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON>", "enterProductType": "<PERSON><PERSON><PERSON><PERSON>", "productWarranty": "<PERSON><PERSON><PERSON>", "enterWarranty": "<PERSON><PERSON><PERSON><PERSON>", "warrantys": "<PERSON><PERSON><PERSON>", "selectWarrantyTime": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "selectProductBrand": "<PERSON><PERSON><PERSON>", "productCode": "Kode Produk*", "enterProductCode": "Masukkan Kode Produk", "enterProductQuantity": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>*", "productUnit": "<PERSON><PERSON><PERSON>", "enterPurchasePrice": "<PERSON><PERSON><PERSON><PERSON>", "salePrices": "<PERSON><PERSON>*", "dealerPrice": "<PERSON><PERSON>", "enterDealerPrice": "<PERSON><PERSON><PERSON><PERSON>", "wholeSaleprice": "<PERSON><PERSON>", "enterPrice": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Produsen", "enterManufacturerName": "<PERSON><PERSON><PERSON><PERSON>", "serialNumbers": "<PERSON><PERSON>", "enterSerialNumber": "<PERSON><PERSON><PERSON><PERSON>", "noSerialNumberFound": "Tidak Ada Nomor Seri Ditemukan", "productList": "Daftar Produk", "searchByName": "<PERSON><PERSON>", "retailer": "<PERSON><PERSON><PERSON>", "dealer": "Pedagang", "wholesale": "Grosir", "expense": "<PERSON><PERSON><PERSON><PERSON>", "totalPayable": "Total Dibayar", "totalAmount": "<PERSON><PERSON><PERSON>", "searchByInvoiceOrName": "Cari berdasarkan invoice atau nama", "invoice": "Invoice", "lossminus": "Kekurangan(-)", "yourPaymentIscancelled": "Pembayaran <PERSON>", "previousDue": "Saldo <PERSON>:", "calculator": "Kalkulator:", "dashBoard": "<PERSON><PERSON>", "price": "<PERSON><PERSON>", "create": "Buat", "payment": "Pembayaran", "enterPayingAmount": "<PERSON><PERSON><PERSON><PERSON>", "enterCategoryName": "<PERSON><PERSON><PERSON><PERSON>", "productSize": "Ukuran Produk", "enterProductSize": "Ma<PERSON>kkan <PERSON> Produk", "productColor": "<PERSON><PERSON>", "enterProductColor": "<PERSON><PERSON><PERSON><PERSON>", "productWeight": "<PERSON><PERSON>", "enterProductWeight": "<PERSON><PERSON><PERSON><PERSON>", "productcapacity": "Kapasitas Produk", "enterProductCapacity": "<PERSON><PERSON><PERSON><PERSON> Produk", "enterSalePrice": "<PERSON><PERSON><PERSON><PERSON>", "add": "Tambah", "productCategory": "<PERSON><PERSON><PERSON>", "enterProductUnit": "Masukkan Unit Produk", "productName": "<PERSON><PERSON>", "noProductFound": "Tidak Ada Produk Ditemukan", "addingSerialNumber": "Tambah Nomor Seri?", "unit": "Unit", "editOrAddSerial": "Edit/Tambah Serial:", "enterWholeSalePrice": "<PERSON><PERSON><PERSON><PERSON>", "invoiceCo": "Invoice:", "categories": "<PERSON><PERSON><PERSON>", "purchaseList": "<PERSON><PERSON><PERSON>", "print": "Cetak", "noPurchaseTransactionFound": "Tidak ada transaksi pem<PERSON> ditemukan", "quotationList": "<PERSON><PERSON><PERSON>", "areYouWantToDeleteThisQuotion": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghapus <PERSON> ini?", "convertToSale": "Konversi ke Penjualan", "noQuotionFound": "Tidak ada <PERSON>", "stockReport": "<PERSON><PERSON><PERSON>", "PRODUCTNAME": "NAMA PRODUK", "CATEGORY": "KATEGORI", "PRICE": "HARGA", "QTY": "KUANTITAS", "STATUS": "STATUS", "TOTALVALUE": "NILAI TOTAL", "noReportFound": "Tidak Ada Laporan Ditemukan", "remainingBalance": "<PERSON><PERSON>", "totalpaymentIn": "Total Pembayaran Masuk", "totalPaymentOut": "Total Pembayaran Keluar", "dailyTransaction": "<PERSON><PERSON><PERSON>", "paymentIn": "Pembayaran Masuk", "paymentOut": "<PERSON><PERSON><PERSON><PERSON>", "balance": "<PERSON><PERSON>", "totalPaid": "Total Dibayar", "dueTransaction": "Transaksi <PERSON>", "downloadPDF": "Unduh PDF", "customerType": "<PERSON><PERSON>", "pleaseAddCustomer": "Harap <PERSON> Pelanggan", "purchaseTransaction": "Transaksi <PERSON>", "printPdf": "Cetak PDF", "saleTransactionQuatationHistory": "Transaksi <PERSON>jualan (Riwayat Penjualan Penawaran)", "ADDSALE": "TAMBAHKAN PENJUALAN", "search": "Cari.......", "transactionReport": "<PERSON><PERSON><PERSON>", "saleTransaction": "Transaksi <PERSON>", "totalReturns": "Total Pengembalian", "totalReturnAmount": "Jumlah Total Pengembalian", "saleReturn": "Pengemba<PERSON>", "noSaleTransaactionFound": "Tidak Ada Transaksi Penjualan Ditemukan", "saleList": "<PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "areYouWantToReturnThisSale": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mengembalikan penjualan ini?", "no": "Tidak", "yesReturn": "Ya, Kembalikan", "setting": "<PERSON><PERSON><PERSON><PERSON>", "uploadAnInvoiceLogo": "Unggah Logo Faktur", "showLogoInInvoice": "<PERSON><PERSON><PERSON><PERSON> Lo<PERSON> dalam Faktur?", "logoPositionInInvoice": "Posisi Logo dalam Faktur?", "left": "<PERSON><PERSON>", "right": "<PERSON><PERSON>", "companyAddress": "<PERSON><PERSON><PERSON>", "enterYourCompanyAddress": "<PERSON><PERSON><PERSON><PERSON>", "companyPhoneNumber": "Nomor Telepon <PERSON>", "companyEmailAddress": "<PERSON><PERSON><PERSON>", "enterCompanyPhoneNumber": "<PERSON><PERSON><PERSON><PERSON>mor Telepon Perusahaan", "enterCompanyEmailAddress": "<PERSON><PERSON><PERSON><PERSON>", "companyWebsiteUrl": "URL Website Perusahaan", "enterCompanyWebsiteUrl": "Masukkan URL website perusahaan", "companyDescription": "<PERSON><PERSON><PERSON><PERSON>", "enterCompanyDesciption": "<PERSON><PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON><PERSON>", "kycVerification": "Verifikasi KYC", "identityVerify": "Verifikasi Identitas", "yourNeedToIdentityVerify": "Anda perlu verifikasi identitas sebelum membeli pesan", "govermentId": "Identitas Pemerintah", "takeADriveLisense": "Ambil foto SIM, kartu identitas nasional, atau paspor", "addDucument": "Tambahkan Dokumen", "youNeedToIdentityVerifySms": "Anda perlu verifikasi identitas sebelum membeli pesan", "wholeSeller": "Pedagang <PERSON>", "enterSmsContent": "Masukkan Ko<PERSON>n <PERSON>", "sendMessage": "<PERSON><PERSON>", "buySms": "Beli SMS", "supplierList": "<PERSON><PERSON><PERSON>", "addSupplier": "Tambah Pemasok", "noSupplierFound": "Tidak Ada Pemasok Ditemukan", "checkWarranty": "<PERSON><PERSON><PERSON>", "customerInvoices": "Fak<PERSON>", "supplierInvoice": "<PERSON><PERSON><PERSON>", "addItem": "Tambahkan Item", "noInvoiceFound": "Tidak Ada Faktur Ditemukan", "stock": "Stok", "enterStockAmount": "<PERSON><PERSON><PERSON><PERSON>", "discountPrice": "<PERSON><PERSON>", "enterDiscountPrice": "<PERSON><PERSON><PERSON><PERSON>", "dateTime": "Tanggal W<PERSON>", "walkInCustomer": "Pelanggan Datang Langsung", "saleDetails": "Detail <PERSON>", "customerWalkIncostomer": "Pelanggan: Pelanggan Datang Langsung", "item": "Barang", "camera": "<PERSON><PERSON><PERSON>", "totalItem2": "Total Barang : 2", "shipingOrOther": "Pengiriman/Lainnya", "yourDueSales": "Penjualan Tertunda Anda", "yourAllSales": "<PERSON><PERSON><PERSON><PERSON>", "invoiceHint": "Nomor <PERSON>..", "customer": "Pelanggan", "dueAmountWillShowHere": "<PERSON><PERSON><PERSON> yang harus dibayar akan ditampilkan di sini jika tersedia", "thisCustmerHasNoDue": "Pelanggan ini tidak memiliki tunggakan", "pleaseSelectACustomer": "<PERSON><PERSON><PERSON>", "pleaseAddASale": "<PERSON><PERSON><PERSON>", "yourAllSaleList": "<PERSON><PERSON><PERSON>ju<PERSON> Anda", "changeableAmount": "<PERSON><PERSON><PERSON>", "sales": "Penjualan", "dueList": "Daftar Tung<PERSON>", "ledger": "<PERSON><PERSON>", "transaction": "Transaksi", "subscription": "<PERSON><PERSON><PERSON>", "upgradeOnMobileApp": "Upgrade Melalui Aplik<PERSON>", "POSSale": "Penjualan POS", "searchAnyThing": "<PERSON>i <PERSON>n...", "sale": "Penjualan", "logOut": "<PERSON><PERSON><PERSON>", "cashAndBank": "Tunai & Bank", "cashInHand": "<PERSON><PERSON>", "bankAccounts": "Rekening Bank", "creativeHub": "<PERSON><PERSON><PERSON>", "openCheques": "Cek Terbuka", "loanAccounts": "<PERSON><PERSON><PERSON>", "share": "Bagikan", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dueCollection": "Pengumpulan Tunggakan", "customerOfTheMonth": "Pelanggan <PERSON>", "topSellingProduct": "<PERSON><PERSON><PERSON>", "statistic": "Statistik", "stockValues": "<PERSON><PERSON>", "lowStock": "Stok Rendah", "fivePurchase": "Lima Produk Terlaris Bulan Ini", "recentSale": "Penjualan Terbaru", "tSale": "Total Penjualan", "sAmount": "<PERSON><PERSON><PERSON>", "expenses": "<PERSON><PERSON><PERSON><PERSON>", "inc": "Pendapatan", "prof": "Profil"}