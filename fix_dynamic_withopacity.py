#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت لإصلاح استخدامات withOpacity الديناميكية في مشروع Flutter
يستبدل المتغيرات الديناميكية بـ Color.fromRGBO مع حفظ الشفافية

المؤلف: مساعد الذكي
التاريخ: 2025-07-15
"""

import os
import re
import sys
from pathlib import Path
import argparse

class DynamicWithOpacityFixer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.changes_made = 0
        
    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")
        
        for file_path in self.project_path.rglob("*.dart"):
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")
        
    def fix_dynamic_withopacity(self, content):
        """إصلاح withOpacity الديناميكية"""
        changes = 0
        
        # نمط للمتغيرات الديناميكية مع withOpacity
        pattern = r'(\w+(?:\[\w*\])*(?:\.\w+)*)\.withOpacity\(([\d.]+)\)'
        
        def replace_dynamic_withopacity(match):
            nonlocal changes
            color_var = match.group(1)
            opacity = match.group(2)
            
            # تجنب الألوان المعرفة مسبقاً (تم إصلاحها بالفعل)
            if color_var.startswith(('k', 'Colors.')):
                return match.group(0)
                
            # استبدال بـ Color.fromRGBO مع استخدام المتغير
            replacement = f"{color_var}.withOpacity({opacity})"
            
            # إذا كان المتغير بسيط، يمكننا استخدام طريقة أفضل
            if '[' not in color_var and '.' not in color_var:
                # استخدام Color.fromRGBO مع قيم المتغير
                replacement = f"Color.fromRGBO({color_var}.red, {color_var}.green, {color_var}.blue, {opacity})"
                changes += 1
                
            return replacement
            
        new_content = re.sub(pattern, replace_dynamic_withopacity, content)
        return new_content, changes
        
    def process_file(self, file_path):
        """معالجة ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            new_content, file_changes = self.fix_dynamic_withopacity(content)
            
            if file_changes > 0:
                # إنشاء نسخة احتياطية
                backup_path = str(file_path) + '.dynamic_backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                # كتابة المحتوى الجديد
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                    
                print(f"✅ {file_path.name}: تم إصلاح {file_changes} استخدام ديناميكي")
                self.changes_made += file_changes
                
        except Exception as e:
            print(f"❌ خطأ في معالجة {file_path}: {e}")
            
    def run(self):
        """تشغيل السكربت"""
        print("🚀 بدء إصلاح withOpacity الديناميكية...")
        print("=" * 50)
        
        self.find_dart_files()
        
        if not self.dart_files:
            print("❌ لم يتم العثور على ملفات Dart")
            return
            
        print(f"📝 معالجة {len(self.dart_files)} ملف...")
        print("-" * 30)
        
        for file_path in self.dart_files:
            self.process_file(file_path)
            
        print("-" * 30)
        print(f"✅ تم الانتهاء!")
        print(f"📊 الإحصائيات:")
        print(f"   - التغييرات المطبقة: {self.changes_made}")
        
        if self.changes_made > 0:
            print(f"\n💾 تم إنشاء نسخ احتياطية بامتداد .dynamic_backup")

def main():
    parser = argparse.ArgumentParser(description='إصلاح withOpacity الديناميكية في مشروع Flutter')
    parser.add_argument('--path', default='.', help='مسار المشروع (افتراضي: المجلد الحالي)')
    
    args = parser.parse_args()
    
    fixer = DynamicWithOpacityFixer(args.path)
    fixer.run()

if __name__ == "__main__":
    main()
