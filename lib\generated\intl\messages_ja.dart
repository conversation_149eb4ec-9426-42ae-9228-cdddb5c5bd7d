// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ja locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ja';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("売上を追加"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("カテゴリ"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("請求書"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS 売上"),
        "PRICE": MessageLookupByLibrary.simpleMessage("価格"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("商品名"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saasログインパネル"),
        "QTY": MessageLookupByLibrary.simpleMessage("数量"),
        "Quantity": MessageLookupByLibrary.simpleMessage("数量*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("ステータス"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("合計値"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("ユーザータイトル"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("アプリについて"),
        "accountName": MessageLookupByLibrary.simpleMessage("口座名"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("口座番号"),
        "action": MessageLookupByLibrary.simpleMessage("アクション"),
        "add": MessageLookupByLibrary.simpleMessage("追加"),
        "addBrand": MessageLookupByLibrary.simpleMessage("ブランドを追加"),
        "addCategory": MessageLookupByLibrary.simpleMessage("カテゴリを追加"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("顧客を追加"),
        "addDescription": MessageLookupByLibrary.simpleMessage("説明を追加...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("ドキュメントを追加"),
        "addItem": MessageLookupByLibrary.simpleMessage("アイテムを追加"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage("商品カテゴリを追加"),
        "addNew": MessageLookupByLibrary.simpleMessage("新しいものを追加"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("新しいユーザーを追加"),
        "addProduct": MessageLookupByLibrary.simpleMessage("商品を追加"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("追加に成功"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("サプライヤーを追加"),
        "addUnit": MessageLookupByLibrary.simpleMessage("単位を追加"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("経費リストの追加/更新"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("収入リストの追加/更新"),
        "addUserRole": MessageLookupByLibrary.simpleMessage("ユーザーロールを追加"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("シリアル番号を追加しますか？"),
        "address": MessageLookupByLibrary.simpleMessage("住所"),
        "all": MessageLookupByLibrary.simpleMessage("すべて"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage("すべての基本機能"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("アカウントをお持ちですか？"),
        "amount": MessageLookupByLibrary.simpleMessage("金額"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("AndroidおよびiOSアプリサポート"),
        "areYouWantToCreateThisQuation":
            MessageLookupByLibrary.simpleMessage("この見積書を作成しますか？"),
        "areYouWantToDeleteThisCustomer":
            MessageLookupByLibrary.simpleMessage("この顧客を削除しますか？"),
        "areYouWantToDeleteThisProduct":
            MessageLookupByLibrary.simpleMessage("この製品を削除しますか"),
        "areYouWantToDeleteThisQuotion":
            MessageLookupByLibrary.simpleMessage("この見積書を削除しますか？"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("この売上を返品しますか？"),
        "balance": MessageLookupByLibrary.simpleMessage("残高"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("銀行口座の通貨"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("銀行口座"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("銀行情報"),
        "bankName": MessageLookupByLibrary.simpleMessage("銀行名"),
        "between": MessageLookupByLibrary.simpleMessage("間"),
        "billTo": MessageLookupByLibrary.simpleMessage("請求先："),
        "branchName": MessageLookupByLibrary.simpleMessage("支店名"),
        "brand": MessageLookupByLibrary.simpleMessage("ブランド"),
        "brandName": MessageLookupByLibrary.simpleMessage("ブランド名"),
        "businessCategory": MessageLookupByLibrary.simpleMessage("ビジネスカテゴリ"),
        "buy": MessageLookupByLibrary.simpleMessage("購入"),
        "buyPremiumPlan": MessageLookupByLibrary.simpleMessage("プレミアムプランを購入"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMSを購入"),
        "calculator": MessageLookupByLibrary.simpleMessage("計算機:"),
        "camera": MessageLookupByLibrary.simpleMessage("カメラ"),
        "cancel": MessageLookupByLibrary.simpleMessage("キャンセル"),
        "capacity": MessageLookupByLibrary.simpleMessage("容量"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("現金と銀行"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("手元現金"),
        "categories": MessageLookupByLibrary.simpleMessage("カテゴリ"),
        "category": MessageLookupByLibrary.simpleMessage("カテゴリ"),
        "categoryName": MessageLookupByLibrary.simpleMessage("カテゴリ名"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("釣り銭"),
        "changeableAmount": MessageLookupByLibrary.simpleMessage("変更可能金額"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("保証を確認する"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("プランを選択"),
        "collectDue": MessageLookupByLibrary.simpleMessage("未払い集計"),
        "color": MessageLookupByLibrary.simpleMessage("色"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("会社名"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("会社住所"),
        "companyDescription": MessageLookupByLibrary.simpleMessage("会社概要"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("会社電子メールアドレス"),
        "companyName": MessageLookupByLibrary.simpleMessage("会社名"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage("会社電話番号"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("会社ウェブサイトURL"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("パスワードの確認"),
        "continu": MessageLookupByLibrary.simpleMessage("続行"),
        "convertToSale": MessageLookupByLibrary.simpleMessage("販売に変換"),
        "create": MessageLookupByLibrary.simpleMessage("作成"),
        "createPayment": MessageLookupByLibrary.simpleMessage("支払い作成"),
        "createdBy": MessageLookupByLibrary.simpleMessage("作成者"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("クリエイティブハブ"),
        "currency": MessageLookupByLibrary.simpleMessage("通貨"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("現在のプラン"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("カスタム請求書のブランディング"),
        "customer": MessageLookupByLibrary.simpleMessage("顧客"),
        "customerDue": MessageLookupByLibrary.simpleMessage("顧客未払い"),
        "customerInvoices": MessageLookupByLibrary.simpleMessage("顧客請求書"),
        "customerList": MessageLookupByLibrary.simpleMessage("顧客リスト"),
        "customerName": MessageLookupByLibrary.simpleMessage("顧客名"),
        "customerOfTheMonth": MessageLookupByLibrary.simpleMessage("今月の顧客"),
        "customerType": MessageLookupByLibrary.simpleMessage("顧客タイプ"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("顧客: 来店客"),
        "customers": MessageLookupByLibrary.simpleMessage("顧客"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("日集計"),
        "dailySales": MessageLookupByLibrary.simpleMessage("日売上"),
        "dailyTransaction": MessageLookupByLibrary.simpleMessage("日次取引"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("ダッシュボード"),
        "date": MessageLookupByLibrary.simpleMessage("日付"),
        "dateTime": MessageLookupByLibrary.simpleMessage("日時"),
        "dealer": MessageLookupByLibrary.simpleMessage("ディーラー"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("ディーラー価格"),
        "delete": MessageLookupByLibrary.simpleMessage("削除"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("配送料"),
        "description": MessageLookupByLibrary.simpleMessage("説明"),
        "details": MessageLookupByLibrary.simpleMessage("詳細>"),
        "discount": MessageLookupByLibrary.simpleMessage("割引"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("割引価格"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDFをダウンロード"),
        "due": MessageLookupByLibrary.simpleMessage("未払い"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("未払い額"),
        "dueAmountWillShowHere":
            MessageLookupByLibrary.simpleMessage("未払い金額がここに表示されます"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("未払い回収"),
        "dueList": MessageLookupByLibrary.simpleMessage("未払いリスト"),
        "dueTransaction": MessageLookupByLibrary.simpleMessage("未払い取引"),
        "edit": MessageLookupByLibrary.simpleMessage("編集"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage("シリアルを編集/追加:"),
        "editYourProfile": MessageLookupByLibrary.simpleMessage("プロフィールを編集"),
        "email": MessageLookupByLibrary.simpleMessage("Eメール"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("金額を入力してください"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("ブランド名を入力してください"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("カテゴリ名を入力してください"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("会社概要を入力してください"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("会社電子メールアドレスを入力してください"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("会社電話番号を入力してください"),
        "enterCompanyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("会社ウェブサイトURLを入力してください"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("顧客名を入力してください"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("ディーラー価格を入力してください"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("割引価格を入力してください"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("経費カテゴリを入力してください"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("経費日を入力してください"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("収入カテゴリを入力してください"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage("収入日を入力してください"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("メーカー名を入力してください"),
        "enterName": MessageLookupByLibrary.simpleMessage("名前を入力してください"),
        "enterNames": MessageLookupByLibrary.simpleMessage("名前を入力してください"),
        "enterNote": MessageLookupByLibrary.simpleMessage("メモを入力してください"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("期首残高を入力してください"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("支払い金額を入力してください"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("パスワードを入力"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("支払い金額を入力してください"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("価格を入力してください"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("商品容量を入力してください"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("商品コードを入力してください"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("商品の色を入力してください"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("商品名を入力してください"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("商品数量を入力してください"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("商品サイズを入力してください"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("商品タイプを入力してください"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("商品単位を入力してください"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("商品重量を入力してください"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("購入価格を入力してください"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("参照番号を入力してください"),
        "enterSalePrice": MessageLookupByLibrary.simpleMessage("販売価格を入力してください"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("シリアル番号を入力してください"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("メッセージのコンテンツを入力してください"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("在庫数量を入力してください"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage("取引IDを入力"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage("単位名を入力してください"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("ユーザーロール名を入力"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage("ユーザータイトルを入力"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("保証を入力してください"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("卸売価格を入力してください"),
        "enterYOurAmount": MessageLookupByLibrary.simpleMessage("金額を入力してください"),
        "enterYourAddress": MessageLookupByLibrary.simpleMessage("住所を入力してください"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("会社住所を入力してください"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("会社名を入力してください"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("会社名を入力してください"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("メールアドレスを入力してください"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("パスワードを入力してください"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("パスワードをもう一度入力してください"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("電話番号を入力してください"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("店名を入力してください"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("カテゴリ名を入力してください"),
        "expense": MessageLookupByLibrary.simpleMessage("経費"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("経費日"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("経費の詳細"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("経費用"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("経費カテゴリリスト"),
        "expenses": MessageLookupByLibrary.simpleMessage("経費"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage("今月のトップ5購入商品"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage("無制限の使用のために"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("パスワードをお忘れですか？"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage("無料データバックアップ"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("無制限の無料アップデート"),
        "freePackage": MessageLookupByLibrary.simpleMessage("無料パッケージ"),
        "freePlan": MessageLookupByLibrary.simpleMessage("無料プラン"),
        "getStarted": MessageLookupByLibrary.simpleMessage("始めてみる"),
        "govermentId": MessageLookupByLibrary.simpleMessage("政府ID"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("総計"),
        "hold": MessageLookupByLibrary.simpleMessage("保留"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("保留番号"),
        "identityVerify": MessageLookupByLibrary.simpleMessage("身元確認"),
        "inc": MessageLookupByLibrary.simpleMessage("収入"),
        "income": MessageLookupByLibrary.simpleMessage("収入"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("収入カテゴリ"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage("収入カテゴリリスト"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("収入日"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("収入の詳細"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("収入の対象"),
        "incomeList": MessageLookupByLibrary.simpleMessage("収入リスト"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("在庫を増やす"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage("即時プライバシー"),
        "invoice": MessageLookupByLibrary.simpleMessage("請求書"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("請求書:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("請求書番号..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("請求書番号"),
        "item": MessageLookupByLibrary.simpleMessage("商品"),
        "itemName": MessageLookupByLibrary.simpleMessage("商品名"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC検証"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("台帳の詳細"),
        "ledger": MessageLookupByLibrary.simpleMessage("台帳"),
        "left": MessageLookupByLibrary.simpleMessage("左"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("ローン口座"),
        "logOut": MessageLookupByLibrary.simpleMessage("ログアウト"),
        "login": MessageLookupByLibrary.simpleMessage("ログイン"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("請求書のロゴの位置"),
        "loss": MessageLookupByLibrary.simpleMessage("損失"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("損益"),
        "lossminus": MessageLookupByLibrary.simpleMessage("損失(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("在庫不足"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("低在庫"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "ブランドの請求書を使用して顧客に持続的な印象を与えます。当社の無制限アップグレードは、請求書をカスタマイズし、ブランドのアイデンティティを強化し、顧客の忠誠心を育む独自の利点を提供します。"),
        "manufacturer": MessageLookupByLibrary.simpleMessage("メーカー"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas ログインパネル"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas サインアップパネル"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("モバイルアプリ\n+\nデスクトップ"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("領収書"),
        "nam": MessageLookupByLibrary.simpleMessage("名前*"),
        "name": MessageLookupByLibrary.simpleMessage("名前"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("名前、コード、またはカテゴリ"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("新規顧客"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("新規顧客"),
        "newIncome": MessageLookupByLibrary.simpleMessage("新規収入"),
        "no": MessageLookupByLibrary.simpleMessage("いいえ"),
        "noConnection": MessageLookupByLibrary.simpleMessage("接続なし"),
        "noCustomerFound": MessageLookupByLibrary.simpleMessage("顧客が見つかりません"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("未払い取引が見つかりません"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("経費カテゴリが見つかりません"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("収入カテゴリが見つかりません"),
        "noIncomeFound": MessageLookupByLibrary.simpleMessage("収入が見つかりません"),
        "noInvoiceFound": MessageLookupByLibrary.simpleMessage("請求書が見つかりません"),
        "noProductFound": MessageLookupByLibrary.simpleMessage("商品が見つかりません"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("購入取引が見つかりません"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("見積もりが見つかりませんでした"),
        "noReportFound": MessageLookupByLibrary.simpleMessage("レポートが見つかりません"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("売上取引が見つかりません"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("シリアル番号が見つかりません"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("サプライヤーが見つかりません"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("取引が見つかりません"),
        "noUserFound": MessageLookupByLibrary.simpleMessage("ユーザーが見つかりません"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("ユーザーロールが見つかりません"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("シリアル番号が見つかりません"),
        "note": MessageLookupByLibrary.simpleMessage("メモ"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("開いた小切手"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("期首残高"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("または PNG、JPG をドラッグ＆ドロップ"),
        "orders": MessageLookupByLibrary.simpleMessage("注文"),
        "other": MessageLookupByLibrary.simpleMessage("その他"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("その他収入"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("パッケージの特徴"),
        "paid": MessageLookupByLibrary.simpleMessage("支払済み"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("支払い済み金額"),
        "partyName": MessageLookupByLibrary.simpleMessage("取引先名"),
        "partyType": MessageLookupByLibrary.simpleMessage("取引先の種類"),
        "password": MessageLookupByLibrary.simpleMessage("パスワード"),
        "payCash": MessageLookupByLibrary.simpleMessage("現金支払い"),
        "payable": MessageLookupByLibrary.simpleMessage("支払い"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("支払金額"),
        "payment": MessageLookupByLibrary.simpleMessage("支払い"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("入金"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("出金"),
        "paymentType": MessageLookupByLibrary.simpleMessage("支払い方法"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("支払い方法"),
        "phone": MessageLookupByLibrary.simpleMessage("電話"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("電話番号"),
        "phoneVerification": MessageLookupByLibrary.simpleMessage("電話番号の確認"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage("売上を追加してください"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("顧客を追加してください"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage("インターネット接続を確認してください"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "モバイルアプリをダウンロードして、デスクトップ版を使用するにはパッケージに加入してください"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("製品在庫を入力してください"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("有効なデータを入力してください"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("顧客を選択してください"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("有効なデータを入力してください"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saasサインアップパネル"),
        "practies": MessageLookupByLibrary.simpleMessage("実践"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("プレミアムカスタマーサポート"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("プレミアムプラン"),
        "preview": MessageLookupByLibrary.simpleMessage("プレビュー"),
        "previousDue": MessageLookupByLibrary.simpleMessage("前回支払い:"),
        "price": MessageLookupByLibrary.simpleMessage("価格"),
        "print": MessageLookupByLibrary.simpleMessage("印刷"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("請求書を印刷する"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDFを印刷"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("プライバシーポリシー"),
        "product": MessageLookupByLibrary.simpleMessage("商品"),
        "productCategory": MessageLookupByLibrary.simpleMessage("商品カテゴリ"),
        "productCod": MessageLookupByLibrary.simpleMessage("商品コード*"),
        "productColor": MessageLookupByLibrary.simpleMessage("商品の色"),
        "productList": MessageLookupByLibrary.simpleMessage("商品リスト"),
        "productNam": MessageLookupByLibrary.simpleMessage("商品名*"),
        "productName": MessageLookupByLibrary.simpleMessage("商品名"),
        "productSize": MessageLookupByLibrary.simpleMessage("商品サイズ"),
        "productStock": MessageLookupByLibrary.simpleMessage("製品在庫"),
        "productType": MessageLookupByLibrary.simpleMessage("商品タイプ"),
        "productUnit": MessageLookupByLibrary.simpleMessage("商品単位"),
        "productWaranty": MessageLookupByLibrary.simpleMessage("商品保証"),
        "productWeight": MessageLookupByLibrary.simpleMessage("商品重量"),
        "productcapacity": MessageLookupByLibrary.simpleMessage("商品容量"),
        "prof": MessageLookupByLibrary.simpleMessage("プロフィール"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("プロフィールの編集"),
        "profit": MessageLookupByLibrary.simpleMessage("利益"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("利益(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("利益(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("購入"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("購入リスト"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("プレミアムプランを購入"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("購入価格"),
        "purchaseTransaction": MessageLookupByLibrary.simpleMessage("購入取引"),
        "quantity": MessageLookupByLibrary.simpleMessage("数量"),
        "quotation": MessageLookupByLibrary.simpleMessage("見積書"),
        "quotationList": MessageLookupByLibrary.simpleMessage("見積もりリスト"),
        "recentSale": MessageLookupByLibrary.simpleMessage("最近の販売"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("受領金額"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("参照番号"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("参照番号"),
        "registration": MessageLookupByLibrary.simpleMessage("登録"),
        "remaining": MessageLookupByLibrary.simpleMessage("残り: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("残高"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("残りの未払い"),
        "reports": MessageLookupByLibrary.simpleMessage("レポート"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage("パスワードをリセット"),
        "retailer": MessageLookupByLibrary.simpleMessage("小売業者"),
        "revenue": MessageLookupByLibrary.simpleMessage("収益"),
        "right": MessageLookupByLibrary.simpleMessage("右"),
        "sAmount": MessageLookupByLibrary.simpleMessage("販売金額"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "ビジネスデータを簡単に保護します。当社のPos Saas POS Unlimitedアップグレードには無料のデータバックアップが含まれており、貴重な情報が予期せぬイベントから保護されています。本当に重要なこと、つまりビジネスの成長に集中できます。"),
        "sale": MessageLookupByLibrary.simpleMessage("売上"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("売上高"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("売上詳細"),
        "saleList": MessageLookupByLibrary.simpleMessage("売上リスト"),
        "salePrice": MessageLookupByLibrary.simpleMessage("販売価格"),
        "salePrices": MessageLookupByLibrary.simpleMessage("販売価格*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("売上返品"),
        "saleTransaction": MessageLookupByLibrary.simpleMessage("売上取引"),
        "saleTransactionQuatationHistory":
            MessageLookupByLibrary.simpleMessage("売上取引（見積もり売上履歴）"),
        "sales": MessageLookupByLibrary.simpleMessage("売上"),
        "salesList": MessageLookupByLibrary.simpleMessage("販売リスト"),
        "saveAndPublish": MessageLookupByLibrary.simpleMessage("保存して公開"),
        "saveAndPublished": MessageLookupByLibrary.simpleMessage("保存して公開"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("変更を保存"),
        "search": MessageLookupByLibrary.simpleMessage("検索......"),
        "searchAnyThing": MessageLookupByLibrary.simpleMessage("何でも検索..."),
        "searchByInvoice": MessageLookupByLibrary.simpleMessage("請求書で検索...."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("請求書または名前で検索"),
        "searchByName": MessageLookupByLibrary.simpleMessage("名前で検索"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("名前または電話で検索..."),
        "searchSerialNumber": MessageLookupByLibrary.simpleMessage("シリアル番号を検索"),
        "selectParties": MessageLookupByLibrary.simpleMessage("取引先を選択してください"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("商品ブランドを選択してください"),
        "selectSerialNumber": MessageLookupByLibrary.simpleMessage("シリアル番号を選択"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("バリエーションを選択してください："),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("保証期間を選択してください"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("言語を選択してください"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("メッセージを送信"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("シリアル番号"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("シリアル番号"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("サービス料"),
        "setting": MessageLookupByLibrary.simpleMessage("設定"),
        "share": MessageLookupByLibrary.simpleMessage("共有"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("配送/その他"),
        "shopName": MessageLookupByLibrary.simpleMessage("店名"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage("開店残高"),
        "show": MessageLookupByLibrary.simpleMessage("表示>"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("請求書にロゴを表示しますか？"),
        "shpingOrServices": MessageLookupByLibrary.simpleMessage("配送/サービス"),
        "size": MessageLookupByLibrary.simpleMessage("サイズ"),
        "statistic": MessageLookupByLibrary.simpleMessage("統計"),
        "status": MessageLookupByLibrary.simpleMessage("ステータス"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "追加費用なしで最新のツールと機能が常に手の届くところにあることを確保し、ビジネスが常に最新の状態を維持する、技術革新の最前線にとどまる。"),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "追加の費用なしで技術の最前線にとどまり、最新のツールと機能を常に手の届くところに持つことを確実にするPos Sass POS Unlimited Upgrade。"),
        "stock": MessageLookupByLibrary.simpleMessage("在庫"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("在庫"),
        "stockReport": MessageLookupByLibrary.simpleMessage("在庫レポート"),
        "stockValue": MessageLookupByLibrary.simpleMessage("在庫価値"),
        "stockValues": MessageLookupByLibrary.simpleMessage("在庫価値"),
        "subTotal": MessageLookupByLibrary.simpleMessage("小計"),
        "subciption": MessageLookupByLibrary.simpleMessage("サブスクリプション"),
        "submit": MessageLookupByLibrary.simpleMessage("送信"),
        "supplier": MessageLookupByLibrary.simpleMessage("サプライヤー"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("仕入先未払い"),
        "supplierInvoice": MessageLookupByLibrary.simpleMessage("サプライヤー請求書"),
        "supplierList": MessageLookupByLibrary.simpleMessage("サプライヤーリスト"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFTコード"),
        "tSale": MessageLookupByLibrary.simpleMessage("総販売額"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "運転免許証、身分証明書、またはパスポートの写真を撮ってください"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("利用規約"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "名前がすべてを物語っています。Pos Saas POS Unlimitedを使用すると、利用制限がありません。少数の取引を処理している場合でも、多くの顧客が押し寄せている場合でも、制限に縛られないことを知りながら、自信を持って運用できます。"),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("この顧客は未払いがありません"),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("この顧客は以前の未払いがあります"),
        "to": MessageLookupByLibrary.simpleMessage("に"),
        "topSellingProduct": MessageLookupByLibrary.simpleMessage("トップセラー商品"),
        "total": MessageLookupByLibrary.simpleMessage("合計"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("合計金額"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("合計割引"),
        "totalDue": MessageLookupByLibrary.simpleMessage("合計未払い"),
        "totalDues": MessageLookupByLibrary.simpleMessage("合計未払い"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("総経費"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("合計収入"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("合計商品: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("合計損失"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("合計支払い"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("合計支払い"),
        "totalPaymentOut": MessageLookupByLibrary.simpleMessage("合計出金"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("合計金額"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("合計商品数"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("合計利益"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("合計購入"),
        "totalReturnAmount": MessageLookupByLibrary.simpleMessage("合計返品額"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("合計返品"),
        "totalSale": MessageLookupByLibrary.simpleMessage("合計売上"),
        "totalSales": MessageLookupByLibrary.simpleMessage("総売上"),
        "totalVat": MessageLookupByLibrary.simpleMessage("合計VAT"),
        "totalpaymentIn": MessageLookupByLibrary.simpleMessage("合計入金"),
        "transaction": MessageLookupByLibrary.simpleMessage("取引"),
        "transactionId": MessageLookupByLibrary.simpleMessage("取引ID"),
        "transactionReport": MessageLookupByLibrary.simpleMessage("取引レポート"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("再試行"),
        "type": MessageLookupByLibrary.simpleMessage("タイプ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("未払い"),
        "unit": MessageLookupByLibrary.simpleMessage("単位"),
        "unitName": MessageLookupByLibrary.simpleMessage("単位名"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("単価"),
        "unlimited": MessageLookupByLibrary.simpleMessage("無制限"),
        "unlimitedInvoice": MessageLookupByLibrary.simpleMessage("無制限の請求書"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage("無制限の利用"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "エキスパートチームによる個別のトレーニングセッションでPos Saas POSのフルポテンシャルを引き出します。基本から高度なテクニックまで、システムのあらゆる側面を活用してビジネスプロセスを最適化することを確実にします。"),
        "updateNow": MessageLookupByLibrary.simpleMessage("今すぐ更新"),
        "updateYourPlanFirst":
            MessageLookupByLibrary.simpleMessage("プランを更新してください。\n販売制限を超えました。"),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("モバイルアプリでアップグレード"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("画像をアップロード"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("請求書のログイン画像をアップロード"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("ドキュメントをアップロード"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("ファイルをアップロード"),
        "userName": MessageLookupByLibrary.simpleMessage("ユーザー名"),
        "userRole": MessageLookupByLibrary.simpleMessage("ユーザーロール"),
        "userRoleName": MessageLookupByLibrary.simpleMessage("ユーザーロール名"),
        "userTitle": MessageLookupByLibrary.simpleMessage("ユーザータイトル"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("VAT/GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage("電話番号を検証"),
        "view": MessageLookupByLibrary.simpleMessage("表示"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("来店客"),
        "warranty": MessageLookupByLibrary.simpleMessage("保証"),
        "warrantys": MessageLookupByLibrary.simpleMessage("保証"),
        "weNeedToRegisterYourPhone":
            MessageLookupByLibrary.simpleMessage("開始する前に電話番号を登録する必要があります！"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "シームレスな運用の重要性を理解しています。そのため、24時間体制でサポートが利用可能で、簡単なクエリから包括的な問題までをサポートします。いつでもどこからでも通話またはWhatsAppを介して接続し、比類のないカスタマーサービスを体験してください。"),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("卸売価格"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("卸売業者"),
        "wholesale": MessageLookupByLibrary.simpleMessage("卸売"),
        "wight": MessageLookupByLibrary.simpleMessage("重量"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("はい、返品"),
        "youHaveToRelogin":
            MessageLookupByLibrary.simpleMessage("アカウントを再ログインする必要があります。"),
        "youNeedToIdentityVerifySms":
            MessageLookupByLibrary.simpleMessage("メッセージを購入する前に身元確認が必要です"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage("全売上リスト"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("全売上"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("使用中"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("未払い売上"),
        "yourNeedToIdentityVerify":
            MessageLookupByLibrary.simpleMessage("メッセージを購入する前に身元確認が必要です"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("あなたのパッケージ"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("お支払いがキャンセルされました"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("お支払いが正常に完了しました"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("お支払いがキャンセルされました")
      };
}
