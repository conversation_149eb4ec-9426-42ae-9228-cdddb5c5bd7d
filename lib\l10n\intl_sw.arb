{"addNewUser": "Ongeza Mtumiaj<PERSON>", "userRole": "<PERSON><PERSON><PERSON><PERSON> wa <PERSON>i", "addNew": "Ongeza Mpya", "orders": "<PERSON><PERSON><PERSON>", "revenue": "Mapato", "noUserRoleFound": "<PERSON><PERSON><PERSON>", "addUserRole": "Ongeza Wajibu wa Mtumiaji", "all": "<PERSON><PERSON>", "profileEdit": "<PERSON><PERSON>", "UserTitle": "<PERSON><PERSON> cha <PERSON>", "enterUserTitle": "Ingiza Cheo cha Mtumiaji", "choseASupplier": "Chagua Msambazaji", "choseACustomer": "Chagua Mteja", "create": "<PERSON>da", "userTitle": "<PERSON><PERSON> cha <PERSON>", "addSuccessful": "Umeongeza Kwa Mafanikio", "youHaveToRelogin": "Lazima Uingie tena kwenye akaunti yako.", "ok": "<PERSON><PERSON>", "payCash": "Lipa Kwa Pesa", "freeLifeTimeUpdate": "<PERSON><PERSON><PERSON> za Mai<PERSON>", "androidIOSAppSupport": "<PERSON><PERSON><PERSON> wa Programu za Android na iOS", "premiumCustomerSupport": "<PERSON><PERSON><PERSON> wa <PERSON>ateja wa <PERSON>e", "customInvoiceBranding": "Ubunifu wa Bili <PERSON>binafsi", "unlimitedUsage": "<PERSON><PERSON><PERSON> Kikomo", "freeDataBackup": "<PERSON><PERSON><PERSON> za Uhif<PERSON>hi wa Data Bure", "stayAtTheForFront": "<PERSON><PERSON> mstari wa mbele wa maendeleo ya teknolojia bila gharama za ziada. Sasisho la Pos Saas POS lisilokuwa na kikomo linahakikisha kuwa unakuwa na zana na huduma za hivi karibuni mkononi mwako, kuh<PERSON><PERSON><PERSON>hara yako inabaki kuwa ya kisasa.", "weUnderStand": "<PERSON><PERSON><PERSON><PERSON> umuhimu wa shughuli bila kukwama. Ndio maana msaada wetu wa saa 24 uko tayari kutoa usaidizi, iwe ni swali dogo au suala kubwa. <PERSON><PERSON><PERSON> nasi wakati wowote, mahali popote kupitia simu au WhatsApp ili ujue huduma isiyo na kifani.", "unlockTheFull": "Fungua uwezo kamili wa Pos Saas POS na mafunzo ya kibinafsi yanayoongozwa na timu yetu ya wataalamu. Kutoka kwa misingi hadi mbinu za juu, tunahakikisha una maarifa ya kutosha ya kutumia kila sehemu ya mfumo ili kuboresha shughuli za biashara yako.", "makeALastingImpression": "Tengeneza athari ya kudumu kwa wateja wako na bili zenye nembo yako. Sasisho lisilokuwa na kikomo linatoa faida ya kipekee ya kubinafsisha bili zako, kuongeza umuhimu wa kitaalam ambao unaimarisha utambulisho wako wa chapa na kukuza uaminifu wa wateja.", "theNameSysIt": "Jina linasema yote. Na Pos Saas POS lisilokuwa na kikomo, hakuna kikomo kwa matumizi yako. Iwe unaprocessing idadi ndogo ya manunuzi au unakumbana na msururu wa wateja, unaweza kufanya kazi kwa ujasiri, ukiwa na uhakika kuwa haujazuiwa na kikomo.", "safeguardYourBusinessDate": "Linda data ya biashara yako kwa urahisi. Sasisho lisilokuwa na kikomo la Pos Saas POS linajumuisha nakala za uhifadhi wa data bure, kuhakikisha kuwa taarifa muhimu zinalindwa dhidi ya matukio yoyote ya kushtukiza. Jifunike katika kile kinachomuhimu - ukua<PERSON> wa biashara yako.", "buy": "<PERSON><PERSON><PERSON><PERSON>", "bankInformation": "Taarifa za Benki", "bankName": "<PERSON><PERSON>", "branchName": "<PERSON><PERSON>", "accountName": "<PERSON><PERSON>", "accountNumber": "<PERSON><PERSON> ya <PERSON>", "bankAccountingCurrecny": "<PERSON><PERSON> ya <PERSON>unti ya <PERSON>ki", "swiftCode": "Msimbo wa SWIFT", "enterTransactionId": "<PERSON><PERSON><PERSON> cha He<PERSON>", "uploadDocument": "<PERSON><PERSON>", "uploadFile": "<PERSON><PERSON>", "aboutApp": "<PERSON><PERSON><PERSON>", "termsOfUse": "<PERSON><PERSON>", "privacyPolicy": "<PERSON><PERSON> ya <PERSON>", "userRoleName": "<PERSON><PERSON> Wajibu wa <PERSON>i", "enterUserRoleName": "<PERSON><PERSON> Jina la Wajibu wa Mt<PERSON>aji", "yourPackage": "<PERSON><PERSON><PERSON><PERSON>", "freePlan": "Mpango wa B<PERSON>", "yourAreUsing": "Unatumia", "freePackage": "<PERSON><PERSON><PERSON><PERSON> cha B<PERSON>", "premiumPlan": "Mpango wa Premium", "packageFeature": "<PERSON><PERSON>", "remaining": "Inasalia: ", "unlimited": "Isiyokuwa na kikomo", "forUnlimitedUses": "Kwa Matumizi Yasi<PERSON> na Kikomo", "updateNow": "<PERSON><PERSON><PERSON>", "purchasePremiumPlan": "Nunua <PERSON> wa Premium", "stayAtTheForeFrontOfTechnological": "<PERSON><PERSON> mbele ya maendeleo ya teknolojia bila gharama za ziada. SmartBiashara POS yetu Inayokusanya Bila Kikomo inahakikisha kuwa daima una zana na huduma za hivi karibuni mikononi mwako, i<PERSON><PERSON><PERSON> kuwa biashara yako inaendelea kuwa ya kisasa.", "buyPremiumPlan": "Nunua <PERSON> wa Premium", "mobilePlusDesktop": "<PERSON>u ya <PERSON>\n+\n<PERSON><PERSON>", "transactionId": "<PERSON><PERSON><PERSON><PERSON><PERSON> cha <PERSON>", "productStock": "<PERSON>oo ya <PERSON><PERSON>a", "pleaseEnterProductStock": "<PERSON><PERSON><PERSON><PERSON> ingiza stoo ya bidhaa", "increaseStock": "Ongeza Stoo", "areYouWantToDeleteThisProduct": "Je, unataka kufuta bidhaa hii?", "currency": "sarafu", "businessCategory": "<PERSON><PERSON> ya <PERSON>", "companyName": "<PERSON><PERSON>", "enterYourCompanyName": "Ingiza Jina la Kampuni Yako", "phoneNumber": "<PERSON><PERSON> ya <PERSON>mu", "enterYourPhoneNumber": "Ingiza namba yako ya simu", "shopOpeningBalance": "Salio la Kufungua Duka", "enterYOurAmount": "Ingiza kiasi chako", "continu": "<PERSON><PERSON><PERSON>", "resetYourPassword": "Weka Upya N<PERSON>", "email": "<PERSON><PERSON> pepe", "enterYourEmailAddress": "Ingiza anwani yako ya barua pepe", "pleaseDownloadOurMobileApp": "Ta<PERSON><PERSON><PERSON> pakua programu yetu ya simu na jiandikishe kwenye kifurushi ili utumie toleo la kompyuta", "mobiPosLoginPanel": "Ubao wa Kuingilia Pos Sa<PERSON>", "enterYourPassword": "Ingiza Nenosir<PERSON>", "login": "Ingia", "password": "<PERSON><PERSON><PERSON><PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON>?", "registration": "<PERSON><PERSON><PERSON>", "editYourProfile": "<PERSON><PERSON>", "uploadAImage": "<PERSON><PERSON> picha", "orDragAndDropPng": " au vuta na weka PNG, JPG", "comapnyName": "<PERSON><PERSON>", "enterYourCompanyNames": "Ingiza Jina la Kampuni Yako", "address": "<PERSON><PERSON><PERSON>", "enterYourAddress": "Ingi<PERSON>", "mobiPosSignUpPane": "<PERSON><PERSON> wa Us<PERSON> wa Pos <PERSON>", "confirmPassword": "Thibitisha nenosiri", "enterYourPasswordAgain": "Ingiza tena nenosiri lako", "alreadyHaveAnAccounts": "Tayari una akaunti?", "choseAplan": "Chagua kifurushi", "allBasicFeatures": "<PERSON><PERSON><PERSON><PERSON>", "unlimitedInvoice": "Bilansi Isiyokuwa na Kikomo", "getStarted": "<PERSON><PERSON>", "currentPlan": "<PERSON><PERSON><PERSON><PERSON>", "selectYourLanguage": "Chagua Lugha Ya<PERSON>", "shopName": "<PERSON><PERSON>", "enterYourShopName": "Ingiza Jina la Duka Lako", "phoneVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa Simu", "weNeedToRegisterYourPhone": "Tunah<PERSON>ji kusajili simu yako kabla ya kuanza!", "verifyPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>", "customerName": "<PERSON><PERSON>ej<PERSON>", "enterCustomerName": "Ingiza Jina la Mteja", "openingBalance": "Salio la Kufungua", "enterOpeningBalance": "Ingiza Salio la Kufungua", "type": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "saveAndPublish": "Hi<PERSON><PERSON> na Chapisha", "customerList": "<PERSON><PERSON>", "searchByNameOrPhone": "Ta<PERSON>ta kwa Jina au Simu...", "addCustomer": "Ongeza Mteja", "partyName": "<PERSON><PERSON>", "partyType": "<PERSON><PERSON>", "phone": "<PERSON><PERSON>", "due": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "<PERSON><PERSON>", "areYouWantToDeleteThisCustomer": "Je, unataka kufuta <PERSON> huyu?", "thisCustomerHavepreviousDue": "<PERSON><PERSON><PERSON> huyu ana deni la awali", "noCustomerFound": "<PERSON><PERSON><PERSON>", "totalDue": "<PERSON><PERSON><PERSON>", "customers": "<PERSON><PERSON><PERSON>", "supplier": "<PERSON><PERSON><PERSON><PERSON>", "collectDue": "<PERSON><PERSON><PERSON> >", "noDueTransantionFound": "<PERSON><PERSON><PERSON>wa Iliyopatikana", "createPayment": "Tengeneza Malipo", "grandTotal": "<PERSON><PERSON><PERSON>", "payingAmount": "<PERSON><PERSON> cha <PERSON>", "enterPaidAmount": "Ingiza Kiasi <PERSON>", "changeAmount": "<PERSON><PERSON> cha <PERSON>", "dueAmount": "<PERSON><PERSON>", "paymentType": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "enterExpanseCategory": "Ingiza Aina ya Matumizi", "pleaseEnterValidData": "<PERSON><PERSON><PERSON><PERSON> ingiza data sahihi", "categoryName": "<PERSON><PERSON>", "entercategoryName": "Ingiza Jina <PERSON> Aina", "description": "<PERSON><PERSON><PERSON>", "addDescription": "Ongeza maelezo....", "expensecategoryList": "<PERSON><PERSON> ya <PERSON> za Matumi<PERSON>", "searchByInvoice": "Ta<PERSON>ta kwa ankara....", "addCategory": "Ongeza Aina", "action": "<PERSON><PERSON>", "noExpenseCategoryFound": "<PERSON><PERSON><PERSON> ya Matumizi Iliyopatikana", "expenseDetails": "<PERSON><PERSON><PERSON> ya Matumizi", "date": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "category": "<PERSON><PERSON>", "referenceNo": "<PERSON><PERSON> ya <PERSON>", "amount": "<PERSON><PERSON>", "note": "Kumbukumbu", "nam": "<PERSON><PERSON>*", "income": "Mapato", "addUpdateExpenseList": "Ongeza/<PERSON>resh<PERSON> ya <PERSON>", "expenseDate": "<PERSON><PERSON><PERSON> ya Matumizi", "enterExpenseDate": "Ingiza Tarehe ya Matumizi", "expenseFor": "<PERSON><PERSON><PERSON>", "enterName": "<PERSON><PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON> ya <PERSON>", "enterReferenceNumber": "Ingiza Nambari ya Marejeleo", "enterNote": "Ingiza <PERSON>", "between": "Kati ya", "to": "<PERSON><PERSON>", "totalExpense": "<PERSON><PERSON><PERSON>", "totalSales": "<PERSON><PERSON><PERSON>", "purchase": "Ununuzi", "newCustomers": "<PERSON><PERSON><PERSON>", "dailySales": "<PERSON><PERSON><PERSON> wa <PERSON>", "dailyCollection": "Ukusanyaj<PERSON> wa Kila Si<PERSON>", "instantPrivacy": "<PERSON><PERSON><PERSON>", "stockInventory": "<PERSON><PERSON><PERSON> ya Bidhaa", "stockValue": "<PERSON><PERSON><PERSON> ya <PERSON>a", "lowStocks": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "otherIncome": "<PERSON><PERSON>", "MOBIPOS": "<PERSON><PERSON>", "newCusotmers": "<PERSON><PERSON><PERSON>", "enterIncomeCategory": "Ingiza Aina ya Mapato", "pleaseentervaliddata": "<PERSON><PERSON><PERSON><PERSON> ingiza data sahihi", "saveAndPublished": "Hi<PERSON><PERSON> na Chapisha", "incomeCategoryList": "<PERSON><PERSON> ya <PERSON>ii ya Mapato", "noIncomeCategoryFound": "<PERSON><PERSON><PERSON> ya Mapato Iliyopatikana", "incomeDetails": "<PERSON><PERSON><PERSON> ya Mapato", "paymentTypes": "<PERSON><PERSON>", "totalIncome": "<PERSON><PERSON><PERSON> ya <PERSON>ato", "incomeList": "<PERSON><PERSON>", "incomeCategory": "<PERSON><PERSON> ya Mapato", "newIncome": "<PERSON>ato <PERSON>", "createdBy": "Imetengenezwa na", "view": "<PERSON><PERSON>", "noIncomeFound": "<PERSON><PERSON><PERSON>kan<PERSON>", "addUpdateIncomeList": "Ongeza/Update <PERSON><PERSON> ya Mapato", "incomeDate": "<PERSON><PERSON><PERSON> ya Mapato", "enterIncomeDate": "Ingiza Tarehe ya Mapato", "incomeFor": "Mapato Kwa", "enterNames": "<PERSON><PERSON><PERSON>", "enterAmount": "<PERSON><PERSON><PERSON>", "printInvoice": "<PERSON><PERSON><PERSON>", "moneyReciept": "<PERSON><PERSON><PERSON>", "billTo": "Kwa:", "invoiceNo": "<PERSON><PERSON>", "totalDues": "Ju<PERSON>la ya Malimbikizo", "paidAmount": "<PERSON><PERSON>", "remainingDue": "Salio la Malimbikizo", "deliveryCharge": "<PERSON><PERSON><PERSON> ya <PERSON>", "INVOICE": "HOJA", "product": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON>", "unitPrice": "<PERSON><PERSON> ya <PERSON>", "totalPrice": "<PERSON><PERSON><PERSON>", "subTotal": "<PERSON><PERSON><PERSON>", "totalVat": "Ju<PERSON>la ya VAT", "totalDiscount": "<PERSON><PERSON><PERSON> Punguzo", "payable": "Inalipika", "paid": "LimeLipwa", "serviceCharge": "<PERSON><PERSON><PERSON> ya <PERSON>", "totalSale": "<PERSON><PERSON><PERSON>", "totalPurchase": "<PERSON><PERSON><PERSON>uz<PERSON>", "receivedAmount": "<PERSON><PERSON>", "customerDue": "<PERSON><PERSON><PERSON>", "supplierDue": "<PERSON><PERSON>", "selectParties": "Chagua Wahusika", "details": "<PERSON><PERSON><PERSON> <PERSON>", "show": "<PERSON><PERSON><PERSON> >", "noTransactionFound": "<PERSON><PERSON><PERSON>", "ledgerDetails": "<PERSON><PERSON><PERSON> ya Rejesta", "status": "<PERSON><PERSON>", "itemName": "<PERSON><PERSON>", "purchasePrice": "Bei ya Ununuzi", "salePrice": "<PERSON><PERSON> <PERSON>", "profit": "<PERSON><PERSON><PERSON>", "loss": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "totalProfit": "<PERSON><PERSON><PERSON>", "totalLoss": "<PERSON><PERSON><PERSON>", "unPaid": "<PERSON><PERSON><PERSON><PERSON>", "lossOrProfit": "Hasara/Faida", "saleAmount": "<PERSON><PERSON>", "profitPlus": "<PERSON><PERSON><PERSON>(+)", "profitMinus": "<PERSON><PERSON><PERSON>(-)", "yourPaymentIsCancelled": "<PERSON><PERSON> yako yame<PERSON>wa", "yourPaymentIsSuccessfully": "<PERSON><PERSON> yako ya<PERSON>", "hold": "Kushikilia", "holdNumber": "<PERSON><PERSON> ya Kushikilia", "selectSerialNumber": "Chagua Nambari ya Siri", "serialNumber": "<PERSON><PERSON> ya <PERSON>", "searchSerialNumber": "<PERSON><PERSON><PERSON> ya <PERSON>", "noSerialNumberFound": "<PERSON><PERSON><PERSON> ya <PERSON>i <PERSON>", "nameCodeOrCategory": "Jina au Kanuni au Jamii", "vatOrgst": "VAT/GST", "discount": "Punguzo", "areYouWantToCreateThisQuation": "Je, unataka kuunda Hati ya Makadirio haya?", "updateYourPlanFirst": "<PERSON><PERSON><PERSON> mpango wako kwanza. Kiko<PERSON> cha <PERSON><PERSON> k<PERSON>a.", "quotation": "<PERSON>i ya Makadirio", "addProduct": "Ongeza Bidhaa", "totalProduct": "<PERSON><PERSON><PERSON>", "shpingOrServices": "Us<PERSON><PERSON><PERSON>ji/Huduma", "addItemCategory": "Ongeza Jamii ya Bidhaa", "selectVariations": "Chagua Tofauti:", "size": "Ukubwa", "color": " rangi", "wight": "<PERSON><PERSON><PERSON>", "capacity": "<PERSON><PERSON><PERSON>", "warranty": "<PERSON><PERSON><PERSON>", "addBrand": "Ongeza Chapa", "brandName": "<PERSON><PERSON>", "enterBrandName": "Ingiza Jina la Chapa", "addUnit": "Ongeza Kipimo", "unitName": "<PERSON><PERSON>", "enterUnitName": "Ingiza Jina la Kipimo", "productNam": "<PERSON><PERSON>*", "enterProductName": "Ingiza Jina la Bidhaa", "productType": "<PERSON><PERSON> ya <PERSON>a", "enterProductType": "Ingiza Aina ya Bidhaa", "productWaranty": "<PERSON><PERSON><PERSON> ya <PERSON>a", "enterWarranty": "<PERSON><PERSON><PERSON>", "warrantys": "<PERSON><PERSON><PERSON>", "selectWarrantyTime": "Chagua Muda wa <PERSON>a", "brand": "<PERSON><PERSON>", "selectProductBrand": "Chagua Chapa ya Bidhaa", "productCod": "<PERSON><PERSON> ya <PERSON>*", "enterProductCode": "<PERSON><PERSON>za Nam<PERSON> ya Bid<PERSON>a", "enterProductQuantity": "<PERSON>giza Wingi wa B<PERSON>haa", "Quantity": "Wingi*", "productUnit": "<PERSON><PERSON><PERSON> cha <PERSON>a", "enterPurchasePrice": "Ingiza Bei ya Ununuzi", "salePrices": "<PERSON><PERSON> <PERSON>*", "dealerPrice": "<PERSON><PERSON> <PERSON>", "enterDealePrice": "Ingiza Bei ya Muuzaji", "wholeSaleprice": "<PERSON><PERSON> ya <PERSON>", "enterPrice": "Ingiza Bei", "manufacturer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterManufacturerName": "Ingiza Jina <PERSON> Mzalishaji", "serialNumbers": "<PERSON><PERSON> ya <PERSON>", "enterSerialNumber": "<PERSON><PERSON><PERSON> ya <PERSON>i", "nosSerialNumberFound": "<PERSON><PERSON><PERSON> ya <PERSON>i <PERSON>", "productList": "<PERSON><PERSON> ya <PERSON>a", "searchByName": "<PERSON><PERSON><PERSON>", "retailer": "<PERSON><PERSON>", "dealer": "<PERSON><PERSON><PERSON>", "wholesale": "<PERSON><PERSON><PERSON>", "expense": "<PERSON><PERSON><PERSON>", "totalPayable": "<PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON>", "searchByInvoiceOrName": "<PERSON><PERSON><PERSON> kwa hati au jina", "invoice": "<PERSON><PERSON>", "lossminus": "<PERSON><PERSON>(-)", "yourPaymentIscancelled": "<PERSON><PERSON> yako yame<PERSON>wa", "previousDue": "Salio la Awali:", "calculator": "Kielelezo:", "dashBoard": "<PERSON><PERSON><PERSON><PERSON>", "price": "Bei", "payment": "<PERSON><PERSON>", "enterPayingAmount": "Ingiza Kiasi cha Kulip<PERSON>", "enterCategoryName": "Ingiza Jina <PERSON> Jamii", "productSize": "<PERSON><PERSON><PERSON><PERSON> wa B<PERSON>a", "enterProductSize": "Ingiza Ukubwa wa Bidhaa", "productColor": "<PERSON><PERSON><PERSON> ya <PERSON>a", "enterProductColor": "<PERSON><PERSON><PERSON> ya <PERSON>a", "productWeight": "<PERSON><PERSON><PERSON> wa <PERSON>", "enterProductWeight": "Ingiza Uzito wa Bid<PERSON>a", "productcapacity": "<PERSON><PERSON><PERSON> wa <PERSON>", "enterProductCapacity": "<PERSON><PERSON><PERSON> Uwezo wa B<PERSON>", "enterSalePrice": "Ingiza Bei ya Kuuza", "add": "Ongeza", "productCategory": "<PERSON><PERSON> ya <PERSON>id<PERSON>a", "enterProductUnit": "<PERSON><PERSON><PERSON> cha Bid<PERSON>a", "productName": "<PERSON><PERSON>", "noProductFound": "<PERSON><PERSON><PERSON>", "addingSerialNumber": "<PERSON><PERSON><PERSON> za Sir<PERSON>?", "unit": "<PERSON><PERSON><PERSON>", "editOrAddSerial": "<PERSON><PERSON>/Ongeza Nambari ya <PERSON>:", "enterWholeSalePrice": "Ingiza Bei ya Jumla", "invoiceCo": "Hati:", "categories": "<PERSON><PERSON>", "purchaseList": "<PERSON><PERSON> ya <PERSON>", "print": "<PERSON><PERSON><PERSON>", "noPurchaseTransactionFound": "<PERSON><PERSON>na shughuli ya ununuzi iliyopatikana", "quotationList": "<PERSON><PERSON> ya <PERSON>", "areYouWantToDeleteThisQuotion": "Je, unataka kufuta <PERSON> hi<PERSON>?", "convertToSale": "<PERSON><PERSON><PERSON> Ku<PERSON>", "noQuotionFound": "<PERSON><PERSON><PERSON>", "stockReport": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>a", "PRODUCTNAME": "JINA LA BIDHAA", "CATEGORY": "JAMII", "PRICE": "BEI", "QTY": "KIDATO", "STATUS": "HADHI", "TOTALVALUE": "JUMLA THAMANI", "noReportFound": "<PERSON><PERSON><PERSON>", "remainingBalance": "Salio Lililo<PERSON>", "totalpaymentIn": "<PERSON><PERSON><PERSON>", "totalPaymentOut": "<PERSON><PERSON><PERSON>", "dailyTransaction": "Shughuli za Kila Siku", "paymentIn": "<PERSON><PERSON>", "paymentOut": "<PERSON><PERSON>", "balance": "Salio", "totalPaid": "<PERSON><PERSON><PERSON>", "dueTransaction": "Shughuli Zinazotakiwa", "downloadPDF": "Pakua PDF", "customerType": "<PERSON><PERSON> ya <PERSON>", "pleaseAddCustomer": "<PERSON><PERSON><PERSON><PERSON>", "purchaseTransaction": "Manunuzi ya Miamala", "printPdf": "Chapisha PDF", "saleTransactionQuatationHistory": "<PERSON><PERSON><PERSON> (Historia ya Matoleo ya Nukuu)", "ADDSALE": "ONGEZA UUZAJI", "search": "Tafuta.......", "transactionReport": "R<PERSON>ot<PERSON> ya Miamala", "saleTransaction": "<PERSON><PERSON><PERSON>", "totalReturns": "<PERSON><PERSON><PERSON>", "totalReturnAmount": "<PERSON><PERSON><PERSON>", "saleReturn": "<PERSON><PERSON><PERSON>", "noSaleTransaactionFound": "<PERSON><PERSON><PERSON> ya <PERSON><PERSON>", "saleList": "<PERSON><PERSON>", "reports": "R<PERSON><PERSON><PERSON>", "areYouWantToReturnThisSale": "Je, unataka kurudisha uuzaji huu?", "no": "Hapana", "yesReturn": "Ndio, Rudisha", "setting": "<PERSON><PERSON><PERSON><PERSON>", "uploadAnInvoiceLogo": "Pakia Nembo ya Bili", "showLogoInInvoice": "<PERSON><PERSON><PERSON>?", "logoPositionInInvoice": "<PERSON><PERSON><PERSON> ya Nembo kwenye Bili?", "left": "Kushoto", "right": "<PERSON><PERSON>", "companyAddress": "<PERSON><PERSON><PERSON>", "enterYourCompanyAddress": "Ingiza Anwani ya Kamp<PERSON> Yako", "companyPhoneNumber": "<PERSON><PERSON> ya Simu ya <PERSON>uni", "companyEmailAddress": "<PERSON><PERSON> pepe ya Ka<PERSON>uni", "enterCompanyPhoneNumber": "Ingiza Nambari ya Simu ya <PERSON>uni", "enterCompanyEmailAddress": "Ingiza Barua pepe ya Kampuni", "companyWebsiteUrl": "URL ya Tovuti ya Kampuni", "enterCompanyWebsiteUrl": "Ingiza URL ya Tovuti ya Kampuni", "companyDescription": "<PERSON><PERSON><PERSON>", "enterCompanyDesciption": "Ingiza Maelezo ya Kampuni", "saveChanges": "<PERSON><PERSON><PERSON>", "kycVerification": "Uh<PERSON><PERSON> wa KYC", "identityVerify": "<PERSON><PERSON><PERSON><PERSON>", "yourNeedToIdentityVerify": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>ha kitambulisho kabla ya kununua ujumbe", "govermentId": "<PERSON><PERSON><PERSON><PERSON><PERSON> cha <PERSON>", "takeADriveLisense": "<PERSON><PERSON><PERSON> picha ya leseni ya udereva, kit<PERSON><PERSON>isho cha kitaifa au pasipoti", "addDucument": "<PERSON><PERSON><PERSON>", "youNeedToIdentityVerifySms": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>ha kitambulisho kabla ya kununua ujumbe", "wholeSeller": "<PERSON><PERSON><PERSON>", "enterSmsContent": "<PERSON><PERSON><PERSON>", "sendMessage": "<PERSON><PERSON>", "buySms": "<PERSON><PERSON><PERSON>", "supplierList": "<PERSON><PERSON>", "addSupplier": "<PERSON><PERSON><PERSON>", "noSupplierFound": "<PERSON><PERSON><PERSON>", "checkWarranty": "<PERSON><PERSON>", "customerInvoices": "Bili za Wateja", "supplierInvoice": "<PERSON><PERSON>", "addItem": "Ongeza Bidhaa", "noInvoiceFound": "<PERSON><PERSON><PERSON>", "stock": "<PERSON><PERSON>", "enterStockAmount": "Ingiza <PERSON> cha <PERSON>", "discountPrice": "Bei ya Punguzo", "enterDiscountPrice": "Ingiza Bei ya Punguzo", "dateTime": "<PERSON><PERSON><PERSON> na Wakati", "walkInCustomer": "Mteja Anayeingia", "saleDetails": "<PERSON><PERSON><PERSON>", "customerWalkIncostomer": "Mteja: Mteja Anayeingia", "item": "<PERSON><PERSON><PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "totalItem2": "<PERSON><PERSON><PERSON>: 2", "shipingOrOther": "Us<PERSON>irishaji/Nying<PERSON>zo", "yourDueSales": "<PERSON><PERSON><PERSON>", "yourAllSales": "<PERSON><PERSON><PERSON>", "invoiceHint": "<PERSON><PERSON> ya Ankara...", "customer": "<PERSON><PERSON><PERSON>", "dueAmountWillShowHere": "<PERSON><PERSON> hapa iwa<PERSON>a", "thisCustmerHasNoDue": "<PERSON><PERSON><PERSON> huyu hana deni lolote", "pleaseSelectACustomer": "<PERSON><PERSON><PERSON><PERSON>", "pleaseAddASale": "<PERSON><PERSON><PERSON><PERSON>", "yourAllSaleList": "<PERSON><PERSON> ya <PERSON>o", "changeableAmount": "<PERSON><PERSON> Ku<PERSON>ilis<PERSON>", "sales": "Mauzo", "dueList": "<PERSON><PERSON>", "ledger": "<PERSON><PERSON>", "transaction": "<PERSON><PERSON>", "subscription": "<PERSON><PERSON><PERSON>", "upgradeOnMobileApp": "<PERSON><PERSON><PERSON> ya <PERSON>mu", "POSSale": "<PERSON><PERSON><PERSON> wa POS", "searchAnyThing": "<PERSON><PERSON><PERSON>...", "sale": "<PERSON><PERSON><PERSON>", "logOut": "Toka nje", "cashAndBank": "<PERSON><PERSON>", "cashInHand": "<PERSON><PERSON>", "bankAccounts": "Akaunti za Benki", "creativeHub": "<PERSON><PERSON><PERSON> cha U<PERSON>fu", "openCheques": "Hundi Zilizofunguliwa", "loanAccounts": "Akaunti za Mikopo", "share": "<PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON>", "dueCollection": "U<PERSON><PERSON><PERSON><PERSON> wa Deni", "customerOfTheMonth": "<PERSON><PERSON><PERSON> wa <PERSON>", "topSellingProduct": "<PERSON><PERSON><PERSON><PERSON>", "statistic": "Takwimu", "stockValues": "<PERSON><PERSON><PERSON> ya <PERSON>", "lowStock": "<PERSON><PERSON>", "fivePurchase": "<PERSON><PERSON><PERSON><PERSON>", "recentSale": "Mauzo ya <PERSON>", "tSale": "<PERSON><PERSON><PERSON>uzo", "sAmount": "<PERSON><PERSON> ch<PERSON>", "expenses": "<PERSON><PERSON><PERSON>", "inc": "Mapato", "prof": "<PERSON><PERSON><PERSON>"}