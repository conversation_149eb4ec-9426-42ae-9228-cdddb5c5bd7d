// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a af locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'af';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("VOEG VERKOOP BY"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIE"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTUUR"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS Verkoping"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRYSING"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("PRODUK NAAM"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Aanmeldingspaneel"),
        "QTY": MessageLookupByLibrary.simpleMessage("BTW"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Quantiteit*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("TOTAL VALUE"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Gebruikerstitel"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Oor Toep"),
        "accountName": MessageLookupByLibrary.simpleMessage("Rekeningnaam"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Rekeningnommer"),
        "action": MessageLookupByLibrary.simpleMessage("Aksie"),
        "add": MessageLookupByLibrary.simpleMessage("Voeg by"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Voeg handelsmerk by"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Voeg Kategorie By"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Voeg Kliënt By"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Voeg beskrywing by...."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Voeg Dokumente by"),
        "addItem": MessageLookupByLibrary.simpleMessage("Voeg Item by"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Voeg Itemkategorie by"),
        "addNew": MessageLookupByLibrary.simpleMessage("Voeg Nuwe"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Voeg Nuwe Gebruiker By"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Voeg produk by"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Suksesvol Bygevoeg"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Voeg Verskaffer by"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Voeg eenheid by"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("Voeg/Dateer Uitgawelys By"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("Voeg/Werk inkomstelys by"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Voeg Gebruikersrol By"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Voeg serienommer by?"),
        "address": MessageLookupByLibrary.simpleMessage("Adres"),
        "all": MessageLookupByLibrary.simpleMessage("Alles"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Alle basiese funksies"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Het u reeds \'n rekening?"),
        "amount": MessageLookupByLibrary.simpleMessage("Bedrag"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android en iOS-toep Ondersteuning"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Wil u hierdie kwotasie skep?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Wil jy hierdie Kliënt verwyder?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Wil jy hierdie produk verwyder?"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Wil u hierdie kwotasie verwyder?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Wil jy hierdie uitverkoping teruggee?"),
        "balance": MessageLookupByLibrary.simpleMessage("Balans"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Bankrekeninggeldeenheid"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankrekeninge"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankinligting"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bank Naam"),
        "between": MessageLookupByLibrary.simpleMessage("Tussen"),
        "billTo": MessageLookupByLibrary.simpleMessage("Klant:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Taknaam"),
        "brand": MessageLookupByLibrary.simpleMessage("Merk"),
        "brandName": MessageLookupByLibrary.simpleMessage("Handelsmerknaam"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Bedrijfskategorie"),
        "buy": MessageLookupByLibrary.simpleMessage("Koop"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Koop Premium Plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Koop sms\'e"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Kanselleer"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapasiteit"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Kontant & Bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Kontant In Hand"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorieë"),
        "category": MessageLookupByLibrary.simpleMessage("Kategorie"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategorie Naam"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Verander Bedrag"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Veranderbare Bedrag"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Kontroleer Waarborg"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Kies \'n plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Inkas Verskuldig >"),
        "color": MessageLookupByLibrary.simpleMessage("Kleur"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Bedrijfsnaam"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Maatskappy Adres"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Maatskappy Beskrywing"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Maatskappy-e-posadres"),
        "companyName": MessageLookupByLibrary.simpleMessage("Bedrijfsnaam"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Maatskappy telefoonnommer"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Maatskappy webwerf URL"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Bevestig wagwoord"),
        "continu": MessageLookupByLibrary.simpleMessage("Voortgaan"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Omskep na Verkope"),
        "create": MessageLookupByLibrary.simpleMessage("Skep"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Skep Betaling"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Gemaak deur"),
        "creativeHub":
            MessageLookupByLibrary.simpleMessage("Kreatiewe Sentrum"),
        "currency": MessageLookupByLibrary.simpleMessage("Geld eenheid"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Huidige plan"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Aangepaste Faktuur Branding"),
        "customer": MessageLookupByLibrary.simpleMessage("Kliënt"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Klant se skuld"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Kliëntfakture"),
        "customerList": MessageLookupByLibrary.simpleMessage("Kliënte lys"),
        "customerName": MessageLookupByLibrary.simpleMessage("Klant se naam"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kliënt van die Maand"),
        "customerType": MessageLookupByLibrary.simpleMessage("Kliënt Tipe"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Kliënt: Inloopklant"),
        "customers": MessageLookupByLibrary.simpleMessage("Kliënte"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Daaglikse versameling"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Daaglikse verkope"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Daglikse transaksie"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum Tyd"),
        "dealer": MessageLookupByLibrary.simpleMessage("Handelaar"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Dealer Prys"),
        "delete": MessageLookupByLibrary.simpleMessage("Verwyder"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Afleweringsfooi"),
        "description": MessageLookupByLibrary.simpleMessage("Beskrywing"),
        "details": MessageLookupByLibrary.simpleMessage("Details >"),
        "discount": MessageLookupByLibrary.simpleMessage("Afslag"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Afslagprys"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Laai PDF af"),
        "due": MessageLookupByLibrary.simpleMessage("Verskuldig"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Verskuldig Bedrag"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Verskuldigde bedrag sal hier wys as beskikbaar"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Verskuldigde Inbetalings"),
        "dueList": MessageLookupByLibrary.simpleMessage("Verskuldigde Lys"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Vervalende transaksie"),
        "edit": MessageLookupByLibrary.simpleMessage("Wysig"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Wysig/Voeg reeksnommer by:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Wysig u profiel"),
        "email": MessageLookupByLibrary.simpleMessage("E-pos"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Voer bedrag in"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Voer handelsmerknaam in"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Voer kategorienaam in"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Voer Maatskappybeskrywing in"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Voer die maatskappy se e-posadres in"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Voer die maatskappy se telefoonnommer in"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Voer die URL van die maatskappy se webwerf in"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Voer Klant se naam in"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Voer Dealer Prys In"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Voer Afslagprys in"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Voer Uitgawekategorie in"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Voer Uitgawedatum in"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Voer inkomstekategorie in"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Voer inkomstedatum in"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Voer vervaardiger se naam in"),
        "enterName": MessageLookupByLibrary.simpleMessage("Voer Naam in"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Voer naam in"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Voer Nota in"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Voer Openingsbalans in"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Voer betaalde bedrag in"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Voer Wagwoord In"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Voer betaalbedrag in"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Voer prys in"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Voer produkkapasiteit in"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Voer Produk Kode In"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Voer produkkleur in"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Voer Produk Naam In"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Voer Produk Quantiteit In"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Voer produkgrootte in"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Voer Produk Tipe In"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Voer produkeenheid in"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Voer produkgewig in"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Voer Inkoopprys In"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Voer Verwysingsnommer in"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Voer afslagprys in"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Voer serienommer in"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Voer boodskapinhoud in"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Voer Voorraadbedrag in"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Voer Transaksie-ID in"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Voer eenheidsnaam in"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Voer Gebruikersrolnaam in"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Voer gebruikerstitel in"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Voer Waarborg In"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Voer groothandelsprys in"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Voer u bedrag in"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Voer u adres in"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Voer jou maatskappyadres in"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Voer u Bedrijfsnaam in"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Voer u Bedrijfsnaam in"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Voer u e-posadres in"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Voer u wagwoord in"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Voer u wagwoord weer in"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Voer u telefoonnommer in"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Voer u Winkelnaam in"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Voer Kategorienaam in"),
        "expense": MessageLookupByLibrary.simpleMessage("Onkoste"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Uitgawedatum"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Uitgawedetails"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Uitgawe Vir"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Uitgawekategorie Lys"),
        "expenses": MessageLookupByLibrary.simpleMessage("Expense"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top five purchasing product of the month"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Vir Onbeperkte Gebruik"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Wagwoord vergeet?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Gratis Data Rugsteun"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Gratis Lewenslange Opdatering"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Gratis Pakket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Gratis Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Begin"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Groot Totaal"),
        "hold": MessageLookupByLibrary.simpleMessage("Hou"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Hou Nommer"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Identiteitsverifikasie"),
        "inc": MessageLookupByLibrary.simpleMessage("Income"),
        "income": MessageLookupByLibrary.simpleMessage("Inkome"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("inkomstekategorie"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Inkomstekategorielys"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Inkomstedatum"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Inkomste Besonderhede"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Inkomste vir"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Inkomste Lys"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Verhoog Voorraad"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Onmiddellike privaatheid"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktur"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Kwis:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Faktuur NR.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Fakturenommer"),
        "item": MessageLookupByLibrary.simpleMessage("Item"),
        "itemName": MessageLookupByLibrary.simpleMessage("ItemNaam"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC Verifikasie"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Ledger Details"),
        "ledger": MessageLookupByLibrary.simpleMessage("Grootboek"),
        "left": MessageLookupByLibrary.simpleMessage("Links"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Leningsrekeninge"),
        "logOut": MessageLookupByLibrary.simpleMessage("Teken Uit"),
        "login": MessageLookupByLibrary.simpleMessage("Inloggen"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logo posisie in faktuur?"),
        "loss": MessageLookupByLibrary.simpleMessage("Verlies"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Verlies/wins"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Verlies(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Low Stock"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Lae aandele"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Maak \'n blywende indruk op jou kliënte met gebrandmerkte faktuur. Ons Onbeperkte Opgradering bied die unieke voordeel van die aanpassing van jou faktuur, wat \'n professionele aanraking byvoeg wat jou handelsmerkidentiteit versterk en kliëntetrots kweek."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Vervaardiger"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Intekenpaneel"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Aanmeldingspaneel"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobiele Toep\n+\nRekenaar"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Geldkwitansie"),
        "nam": MessageLookupByLibrary.simpleMessage("Naam*"),
        "name": MessageLookupByLibrary.simpleMessage("Naam"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Naam of Kode of Kategorië"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nuwe kliënte"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nuwe kliënte"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nuwe inkomste"),
        "no": MessageLookupByLibrary.simpleMessage("Geen"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Geen Verbinding"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Geen Kliënt Gevind"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Geen Verskuldigingstransaksie Gevind"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Geen Uitgawekategorie Gevind"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Geen inkomstekategorie gevind nie"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Geen inkomste gevind"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Geen Faktuur Gevind"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Geen produk gevind"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Geen aankooptransaksie gevind nie"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Geen kwotasie gevind nie"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Geen verslag gevind nie"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Geen verkooptransaksie gevind nie"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Geen Seriële Nommer Gevind"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Geen Verskaffer Gevind"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Geen transaksie gevind nie"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Geen Gebruiker Gevind"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Geen Gebruikersrol Gevind"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Geen serienommer gevind"),
        "note": MessageLookupByLibrary.simpleMessage("Nota"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Oop Tjeks"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Openingsbalans"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "of sleep en laat val PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Bestellings"),
        "other": MessageLookupByLibrary.simpleMessage("Ander"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Ander inkomste"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Pakket Funksie"),
        "paid": MessageLookupByLibrary.simpleMessage("Betaald"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Betaalde bedrag"),
        "partyName": MessageLookupByLibrary.simpleMessage("Party Naam"),
        "partyType": MessageLookupByLibrary.simpleMessage("Party Tipe"),
        "password": MessageLookupByLibrary.simpleMessage("Wagwoord"),
        "payCash": MessageLookupByLibrary.simpleMessage("Betaal Kontant"),
        "payable": MessageLookupByLibrary.simpleMessage("Aanspreeklik"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Betalingsbedrag"),
        "payment": MessageLookupByLibrary.simpleMessage("Betaling"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Betaling In"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Betaling Uit"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Betaalmetode"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Tipe van betaling"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefoon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefoonnommer"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefoonverifikasie"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Voeg asseblief \'n Verkoop by"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Voeg asseblief kliënt by"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Kontrolleer asseblief jou internetkonnektiwiteit"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Laai asseblief ons mobiele app af en teken in op \'n pakket om die desktop-weergawe te gebruik"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Voer asseblief produkvoorraad in"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Voer asseblief geldige data in"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Kies asseblief \'n Kliënt"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Voer asseblief geldige data in"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Registrasiepaneel"),
        "practies": MessageLookupByLibrary.simpleMessage("Oefening"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Kliëntondersteuning"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Voorskou"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Vorige verskuldig:"),
        "price": MessageLookupByLibrary.simpleMessage("Prys"),
        "print": MessageLookupByLibrary.simpleMessage("Druk"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Print faktuur"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Druk PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Privaatheidsbeleid"),
        "product": MessageLookupByLibrary.simpleMessage("Produk"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Produkkategorie"),
        "productCod": MessageLookupByLibrary.simpleMessage("Produk Kode*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Produkkleur"),
        "productList": MessageLookupByLibrary.simpleMessage("Produklys"),
        "productNam": MessageLookupByLibrary.simpleMessage("Produk Naam*"),
        "productName": MessageLookupByLibrary.simpleMessage("Produknaam"),
        "productSize": MessageLookupByLibrary.simpleMessage("Produkgrootte"),
        "productStock": MessageLookupByLibrary.simpleMessage("Produkvoorraad"),
        "productType": MessageLookupByLibrary.simpleMessage("Produk Tipe"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Produk Eenheid"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Produk Waarborg"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Produkgewig"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Produkkapasiteit"),
        "prof": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Profiel Wysig"),
        "profit": MessageLookupByLibrary.simpleMessage("wins"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Wins(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Wins(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Aankoop"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Aankope Lys"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Koop Premium Plan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Aankope Prys"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Kooptransaksie"),
        "quantity": MessageLookupByLibrary.simpleMessage("Hoeveelheid"),
        "quotation": MessageLookupByLibrary.simpleMessage("Kwotasie"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Kwotasie Lys"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Recent Sales"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Ontvang bedrag"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Verwysing Nr."),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Verwysingsnommer"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrasie"),
        "remaining": MessageLookupByLibrary.simpleMessage("Resterende: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("Balans"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Remaining Due"),
        "reports": MessageLookupByLibrary.simpleMessage("Berigte"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Herstel u wagwoord"),
        "retailer": MessageLookupByLibrary.simpleMessage("Kleinhandelaar"),
        "revenue": MessageLookupByLibrary.simpleMessage("Inkomste"),
        "right": MessageLookupByLibrary.simpleMessage("Reg"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Sale Amount"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Beskerm jou besigheidsdata sonder enige moeite. Ons Pos Saas POS Onbeperkte Opgradering sluit gratis data-rugsteun in, wat verseker dat jou waardevolle inligting teen enige onverwagse gebeure beskerm is. Fokus op wat werklik saak maak - jou besigheidsgroei."),
        "sale": MessageLookupByLibrary.simpleMessage("Verkoop"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Verkoopsom"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Verkoping Besonderhede"),
        "saleList": MessageLookupByLibrary.simpleMessage("Verkooplys"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Verkoopprys"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Verkoopprys*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Verkoop Terugkeer"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Verkooptransaksie"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Verkooptransaksies (kwotasieverkoopgeskiedenis)"),
        "sales": MessageLookupByLibrary.simpleMessage("Verkope"),
        "salesList": MessageLookupByLibrary.simpleMessage("Verkooptydlyn"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Stoor en publiseer"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Stoor en publiseer"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Veranderings Stoor"),
        "search": MessageLookupByLibrary.simpleMessage("Soek......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Soek Enige Iets..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Soek volgens faktuur...."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Soek na faktuur of naam"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Soek op naam"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("Soek op naam of foon..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Soek Seriële Nommer"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Kies partye"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Kies Produk Merk"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Kies Seriële Nommer"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Kies Variasies:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Kies Waarborg Tyd"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Kies u taal"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Stuur boodskap"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Seriële Nommer"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serienommer"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Diensfooi"),
        "setting": MessageLookupByLibrary.simpleMessage("Instelling"),
        "share": MessageLookupByLibrary.simpleMessage("Deel"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Verskeping/Ander"),
        "shopName": MessageLookupByLibrary.simpleMessage("Winkelnaam"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Winkel Openingsbalans"),
        "show": MessageLookupByLibrary.simpleMessage("Wys >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Wys logo in faktuur?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Versending/Dienste"),
        "size": MessageLookupByLibrary.simpleMessage("Grootte"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistic"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Bly aan die voorpunt van tegnologiese ontwikkelings sonder enige ekstra koste. Ons Pos Saas POS Onbeperkte Opgradering verseker dat jy altyd die nuutste gereedskap en funksies tot jou beskikking het, en waarborg dat jou besigheid altyd voorste gehou word."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Bly aan die voorpunt van tegnologiese ontwikkelings sonder enige ekstra koste. Ons Pos Sass POS Onbeperkte Opgradering verseker dat jy altyd die nuutste gereedskap en funksies tot jou beskikking het, en waarborg dat jou besigheid altyd voorste gehou word."),
        "stock": MessageLookupByLibrary.simpleMessage("Voorraad"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Voorraadvoorraad"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Voorraadverslag"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Voorraadwaarde"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Stock Value"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotaal"),
        "subciption": MessageLookupByLibrary.simpleMessage("Subskripsie"),
        "submit": MessageLookupByLibrary.simpleMessage("Dien In"),
        "supplier": MessageLookupByLibrary.simpleMessage("Verskaffers"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Verskaffer se skuld"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Verskafferfaktuur"),
        "supplierList": MessageLookupByLibrary.simpleMessage("Verskafferlys"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT-kode"),
        "tSale": MessageLookupByLibrary.simpleMessage("Total Sales"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Neem \'n rybewys, nasionale identiteitskaart of paspoortfoto"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Gebruiksvoorwaardes"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Die naam sê alles. Met Pos Saas POS Onbeperkte is daar geen beperking op jou gebruik nie. Of jy nou \'n handjievol transaksies verwerk of \'n stortvloed van kliënte ervaar, kan jy met selfvertroue bedryf, wetende dat jy nie beperk word deur beperkings nie."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Hierdie kliënt het geen verskuldigheid nie"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Hierdie kliënt het vorige skuld"),
        "to": MessageLookupByLibrary.simpleMessage("Om"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Top Verkopende Produk"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Totale bedrag"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Totale afslag"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Totale Skuld"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Totale skuld"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Totale uitgawe"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Totale inkomste"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Totaal Item: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Totale verlies"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Totaal betaal"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Totale betaalbaar"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Totale betaling uit"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Totaalprys"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Totale produk"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Totale wins"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Totale Aankoop"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Totale opbrengsbedrag"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Totale opbrengste"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Totale Verkope"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Totale verkope"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Totale BTW"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Totale betaling in"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaksie"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Transaksie-ID"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Transaksieverslag"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Probeer Weer"),
        "type": MessageLookupByLibrary.simpleMessage("Tipe"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Onbetaald"),
        "unit": MessageLookupByLibrary.simpleMessage("Eenheid"),
        "unitName": MessageLookupByLibrary.simpleMessage("Eenheidsnaam"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Eenheidsprys"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Onbeperk"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Onbeperkte Invooie"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Onbeperkte Gebruik"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Ontgrendel die volle potensiaal van Pos Saas POS met gepersonaliseerde opleidingsessies deur ons kundige span. Van die basiese beginsels tot gevorderde tegnieke verseker ons dat jy goed toegerus is om elke aspek van die stelsel te benut om jou besigheidsprosesse te optimaliseer."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Werk Nou By"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Werk jou plan eers op \\ nVerkooplimiet is oortref."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Opgradering op Mobiele App"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Laai \'n prentjie op"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Laai \'n faktuurlogo op"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Laai Dokument op"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Laai Lêer op"),
        "userName": MessageLookupByLibrary.simpleMessage("Gebruikersnaam"),
        "userRole": MessageLookupByLibrary.simpleMessage("Gebruikersrol"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Gebruikersrolnaam"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Gebruikerstitel"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("BTW/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifieer telefoonnommer"),
        "view": MessageLookupByLibrary.simpleMessage("Bekyk"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Inloopklant"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantie"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Waarborg"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Ons moet u foon registreer voordat u kan begin!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Ons verstaan die belangrikheid van naadlose bedryf. Daarom is ons rondom die klok ondersteuning beskikbaar om jou te help, hetsy dit \'n vinnige navraag of \'n omvattende bekommernis is. Maak enige tyd en enige plek met ons kontak via oproep of WhatsApp om \'n ongeëwenaarde kliëntediens te ervaar."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Groothandel Prys"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Groothandelaar "),
        "wholesale": MessageLookupByLibrary.simpleMessage("Groothandel"),
        "wight": MessageLookupByLibrary.simpleMessage("Gewig"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ja Keer terug"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Jy moet weer aanmeld by jou rekening."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Jy moet jou identiteit verifieer voordat jy boodskappe kan koop"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Jou volledige verkooplys"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Jou Alle Verkope"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Jy gebruik"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Jou Verskuldigde Verkope"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Jy moet jou identiteit verifieer voordat jy boodskappe kan koop"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Jou Pakket"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("U betaal is gekanselleer"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("U betaal is suksesvol"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("U betaling is gekanselleer")
      };
}
