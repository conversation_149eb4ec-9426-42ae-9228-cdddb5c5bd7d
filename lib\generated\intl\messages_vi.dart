// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("THÊM BÁN HÀNG"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("DANH MỤC"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("HÓA ĐƠN"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Bán hàng POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("GIÁ"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("TÊN SẢN PHẨM"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Bảng đăng nhập Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("SỐ LƯỢNG"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Số lượng*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("TRẠNG THÁI"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("TỔNG GIÁ TRỊ"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Tiêu đề người dùng"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Về ứng dụng"),
        "accountName": MessageLookupByLibrary.simpleMessage("Tên tài khoản"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Số tài khoản"),
        "action": MessageLookupByLibrary.simpleMessage("Hành động"),
        "add": MessageLookupByLibrary.simpleMessage("Thêm"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Thêm Thương hiệu"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Thêm danh mục"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Thêm khách hàng"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Thêm mô tả...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Thêm tài liệu"),
        "addItem": MessageLookupByLibrary.simpleMessage("Thêm mặt hàng"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Thêm Danh mục Mục"),
        "addNew": MessageLookupByLibrary.simpleMessage("Thêm mới"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Thêm người dùng mới"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Thêm Sản phẩm"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Thêm thành công"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Thêm nhà cung cấp"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Thêm Đơn vị"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Thêm/Cập nhật danh sách chi phí"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Thêm/Cập nhật Danh sách Thu nhập"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Thêm vai trò người dùng"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Thêm số serial?"),
        "address": MessageLookupByLibrary.simpleMessage("Địa chỉ"),
        "all": MessageLookupByLibrary.simpleMessage("Tất cả"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Tất cả các tính năng cơ bản"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Bạn đã có tài khoản chưa?"),
        "amount": MessageLookupByLibrary.simpleMessage("Số tiền"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Hỗ trợ ứng dụng Android & iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn tạo Báo giá này không?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn xóa khách hàng này không?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn xóa sản phẩm này"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn xóa báo giá này không?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn trả lại giao dịch bán hàng này không?"),
        "balance": MessageLookupByLibrary.simpleMessage("Số dư"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Tiền tệ tài khoản ngân hàng"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Tài khoản ngân hàng"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Thông tin ngân hàng"),
        "bankName": MessageLookupByLibrary.simpleMessage("Tên ngân hàng"),
        "between": MessageLookupByLibrary.simpleMessage("Giữa"),
        "billTo": MessageLookupByLibrary.simpleMessage("Gửi cho:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Tên chi nhánh"),
        "brand": MessageLookupByLibrary.simpleMessage("Thương hiệu"),
        "brandName": MessageLookupByLibrary.simpleMessage("Tên Thương hiệu"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Loại hình kinh doanh"),
        "buy": MessageLookupByLibrary.simpleMessage("Mua"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Mua gói cao cấp"),
        "buySms": MessageLookupByLibrary.simpleMessage("Mua tin nhắn"),
        "calculator": MessageLookupByLibrary.simpleMessage("Máy tính:"),
        "camera": MessageLookupByLibrary.simpleMessage("Máy ảnh"),
        "cancel": MessageLookupByLibrary.simpleMessage("Hủy bỏ"),
        "capacity": MessageLookupByLibrary.simpleMessage("Dung lượng"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("Tiền mặt và ngân hàng"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Tiền mặt"),
        "categories": MessageLookupByLibrary.simpleMessage("Danh mục"),
        "category": MessageLookupByLibrary.simpleMessage("Danh mục"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Tên danh mục"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Số tiền lẻ"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Số tiền có thể thay đổi"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Kiểm tra bảo hành"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Chọn gói"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Thu nợ >"),
        "color": MessageLookupByLibrary.simpleMessage("Màu sắc"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Tên công ty"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Địa chỉ công ty"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Mô tả công ty"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Địa chỉ email công ty"),
        "companyName": MessageLookupByLibrary.simpleMessage("Tên công ty"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Số điện thoại công ty"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL trang web của công ty"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Xác nhận mật khẩu"),
        "continu": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Chuyển đổi sang bán"),
        "create": MessageLookupByLibrary.simpleMessage("Tạo"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Tạo khoản thanh toán"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Được tạo bởi"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Hub sáng tạo"),
        "currency": MessageLookupByLibrary.simpleMessage("Tiền tệ"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Gói hiện tại"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Tạo thương hiệu hóa hóa đơn tùy chỉnh"),
        "customer": MessageLookupByLibrary.simpleMessage("Khách hàng"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Nợ khách hàng"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Hóa đơn của khách hàng"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Danh sách khách hàng"),
        "customerName": MessageLookupByLibrary.simpleMessage("Tên khách hàng"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Khách hàng của tháng"),
        "customerType": MessageLookupByLibrary.simpleMessage("Loại khách hàng"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Khách hàng: Khách vãng lai"),
        "customers": MessageLookupByLibrary.simpleMessage("Khách hàng"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Thu nợ hàng ngày"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Doanh thu hàng ngày"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch hàng ngày"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Bảng điều khiển"),
        "date": MessageLookupByLibrary.simpleMessage("Ngày"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Ngày giờ"),
        "dealer": MessageLookupByLibrary.simpleMessage("Đại lý"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Giá Đại lý"),
        "delete": MessageLookupByLibrary.simpleMessage("Xóa"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("Phí giao hàng"),
        "description": MessageLookupByLibrary.simpleMessage("Mô tả"),
        "details": MessageLookupByLibrary.simpleMessage("Chi tiết >"),
        "discount": MessageLookupByLibrary.simpleMessage("Giảm giá"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Giá giảm"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Tải xuống PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Nợ"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Số tiền còn nợ"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Số tiền phải trả sẽ hiển thị ở đây nếu có"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("Thu nợ"),
        "dueList": MessageLookupByLibrary.simpleMessage("Danh sách phải trả"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch đến hạn"),
        "edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa/Thêm serial:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa hồ sơ của bạn"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Nhập số tiền"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Nhập Tên Thương hiệu"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Nhập tên danh mục"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Nhập mô tả công ty"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Nhập địa chỉ email công ty"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Nhập số điện thoại công ty"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Nhập URL trang web của công ty"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Nhập tên khách hàng"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Nhập Giá Đại lý"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Nhập giá giảm"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Nhập danh mục chi phí"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Nhập ngày chi phí"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Nhập danh mục thu nhập"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Nhập Ngày Thu nhập"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Nhập Tên Nhà sản xuất"),
        "enterName": MessageLookupByLibrary.simpleMessage("Nhập tên"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Nhập tên"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Nhập ghi chú"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Nhập số dư mở cửa"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Nhập số tiền đã thanh toán"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Nhập số tiền thanh toán"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Nhập Giá"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Nhập dung lượng sản phẩm"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Nhập Mã Sản phẩm"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Nhập màu sản phẩm"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Nhập Tên Sản phẩm"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Nhập Số lượng Sản phẩm"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Nhập kích thước sản phẩm"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Nhập Loại Sản phẩm"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Nhập đơn vị sản phẩm"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Nhập trọng lượng sản phẩm"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Nhập Giá Mua"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Nhập số tham chiếu"),
        "enterSalePrice": MessageLookupByLibrary.simpleMessage("Nhập giá bán"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Nhập Số Sê-ri"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Nhập nội dung tin nhắn"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Nhập số lượng tồn kho"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Nhập mã giao dịch"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Nhập Tên Đơn vị"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Nhập tên vai trò người dùng"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Nhập tiêu đề người dùng"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Nhập Bảo hành"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Nhập giá sỉ"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Nhập số tiền của bạn"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Nhập địa chỉ của bạn"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Nhập địa chỉ công ty của bạn"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Nhập tên công ty của bạn"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Nhập tên công ty của bạn"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Nhập địa chỉ email của bạn"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Nhập mật khẩu của bạn"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Nhập lại mật khẩu của bạn"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Nhập số điện thoại của bạn"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Nhập tên cửa hàng của bạn"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Nhập tên danh mục"),
        "expense": MessageLookupByLibrary.simpleMessage("Chi tiêu"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Ngày chi phí"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Chi tiết chi phí"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Chi phí cho"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Danh sách danh mục chi phí"),
        "expenses": MessageLookupByLibrary.simpleMessage("Chi Phí"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Năm sản phẩm mua nhiều nhất trong tháng"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Dành cho việc sử dụng không giới hạn"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Quên mật khẩu?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Sao lưu dữ liệu miễn phí"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Cập nhật miễn phí trọn đời"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Gói miễn phí"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Gói miễn phí"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Bắt đầu"),
        "govermentId": MessageLookupByLibrary.simpleMessage("ID chính phủ"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Tổng cộng"),
        "hold": MessageLookupByLibrary.simpleMessage("Giữ"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Số lượng giữ"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Xác minh danh tính"),
        "inc": MessageLookupByLibrary.simpleMessage("Thu Nhập"),
        "income": MessageLookupByLibrary.simpleMessage("Thu nhập"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Danh mục thu nhập"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Danh sách Danh mục Thu nhập"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Ngày Thu nhập"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Chi tiết Thu nhập"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Thu nhập cho"),
        "incomeList":
            MessageLookupByLibrary.simpleMessage("Danh sách Thu nhập"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Tăng tồn kho"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Bảo mật tức thì"),
        "invoice": MessageLookupByLibrary.simpleMessage("Hóa đơn"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Hóa đơn:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Số hóa đơn.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Số hóa đơn"),
        "item": MessageLookupByLibrary.simpleMessage("Mặt hàng"),
        "itemName": MessageLookupByLibrary.simpleMessage("Tên sản phẩm"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("Xác minh KYC"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Chi tiết sổ cái"),
        "ledger": MessageLookupByLibrary.simpleMessage("Sổ cái"),
        "left": MessageLookupByLibrary.simpleMessage("Trái"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Tài khoản vay"),
        "logOut": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Vị trí logo trong hóa đơn?"),
        "loss": MessageLookupByLibrary.simpleMessage("Tổn thất"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Lỗ/Lợi nhuận"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Mất (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Hàng Tồn Kém"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Hàng tồn kho thấp"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Tạo ấn tượng lâu dài với khách hàng của bạn thông qua các hóa đơn thương hiệu. Gói nâng cấp không giới hạn của chúng tôi cung cấp lợi thế độc đáo của tùy chỉnh hóa đơn của bạn, thêm sự chuyên nghiệp để tạo dấu ấn thương hiệu của bạn và thúc đẩy sự trung thành của khách hàng."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Nhà sản xuất"),
        "mobiPosLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Bảng điều khiển đăng nhập Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Bảng đăng ký Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Ứng dụng di động\n+\nMáy tính để bàn"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Biên nhận tiền"),
        "nam": MessageLookupByLibrary.simpleMessage("Tên*"),
        "name": MessageLookupByLibrary.simpleMessage("Tên"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Tên, Mã hoặc Danh mục"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Khách hàng mới"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Khách hàng mới"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Thu nhập mới"),
        "no": MessageLookupByLibrary.simpleMessage("Không"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Không có kết nối"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy khách hàng"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy giao dịch nợ"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy danh mục chi phí"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy Danh mục Thu nhập"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy Thu nhập"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy hóa đơn nào"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy sản phẩm nào"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy giao dịch mua hàng nào"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy báo giá nào"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy báo cáo nào"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy giao dịch bán hàng nào"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy Số Sê-ri"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy nhà cung cấp nào"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy giao dịch"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy người dùng"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy vai trò người dùng"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy Số Sê-ri"),
        "note": MessageLookupByLibrary.simpleMessage("Ghi chú"),
        "ok": MessageLookupByLibrary.simpleMessage("Đồng ý"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Séc mở"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("Số dư mở cửa"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("hoặc kéo và thả PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Đơn đặt hàng"),
        "other": MessageLookupByLibrary.simpleMessage("Khác"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Thu nhập khác"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("Tính năng gói"),
        "paid": MessageLookupByLibrary.simpleMessage("Đã trả"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Số tiền đã trả"),
        "partyName": MessageLookupByLibrary.simpleMessage("Tên bên"),
        "partyType": MessageLookupByLibrary.simpleMessage("Loại bên"),
        "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
        "payCash":
            MessageLookupByLibrary.simpleMessage("Thanh toán bằng tiền mặt"),
        "payable": MessageLookupByLibrary.simpleMessage("Có thể thanh toán"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Số tiền thanh toán"),
        "payment": MessageLookupByLibrary.simpleMessage("Thanh toán"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Thanh toán vào"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Thanh toán ra"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Loại thanh toán"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Loại thanh toán"),
        "phone": MessageLookupByLibrary.simpleMessage("Điện thoại"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Xác minh điện thoại"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Vui lòng thêm một giao dịch bán hàng"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Vui lòng thêm khách hàng"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng kiểm tra kết nối internet của bạn"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Vui lòng tải xuống ứng dụng di động của chúng tôi và đăng ký gói để sử dụng phiên bản máy tính để bàn"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập tồn kho sản phẩm"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập dữ liệu hợp lệ"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn một khách hàng"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập dữ liệu hợp lệ"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Bảng đăng ký Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Thực hành"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Hỗ trợ khách hàng cao cấp"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Gói cao cấp"),
        "preview": MessageLookupByLibrary.simpleMessage("Xem trước"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Công nợ trước:"),
        "price": MessageLookupByLibrary.simpleMessage("Giá"),
        "print": MessageLookupByLibrary.simpleMessage("In"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("In hóa đơn"),
        "printPdf": MessageLookupByLibrary.simpleMessage("In PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Chính sách bảo mật"),
        "product": MessageLookupByLibrary.simpleMessage("Sản phẩm"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Danh mục sản phẩm"),
        "productCod": MessageLookupByLibrary.simpleMessage("Mã Sản phẩm*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Màu sản phẩm"),
        "productList":
            MessageLookupByLibrary.simpleMessage("Danh sách Sản phẩm"),
        "productNam": MessageLookupByLibrary.simpleMessage("Tên Sản phẩm*"),
        "productName": MessageLookupByLibrary.simpleMessage("Tên sản phẩm"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Kích thước sản phẩm"),
        "productStock": MessageLookupByLibrary.simpleMessage("Hàng tồn kho"),
        "productType": MessageLookupByLibrary.simpleMessage("Loại Sản phẩm"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Đơn vị Sản phẩm"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Bảo hành Sản phẩm"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Trọng lượng sản phẩm"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Dung lượng sản phẩm"),
        "prof": MessageLookupByLibrary.simpleMessage("Hồ Sơ"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa hồ sơ"),
        "profit": MessageLookupByLibrary.simpleMessage("Lợi nhuận"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Lợi nhuận(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Lợi nhuận(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Mua hàng"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Danh sách mua hàng"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Mua gói cao cấp"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Giá mua"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch mua hàng"),
        "quantity": MessageLookupByLibrary.simpleMessage("Số lượng"),
        "quotation": MessageLookupByLibrary.simpleMessage("Báo giá"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Danh sách báo giá"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Bán Hàng Gần Đây"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Số tiền đã nhận"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Số tham chiếu"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Số tham chiếu"),
        "registration": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "remaining": MessageLookupByLibrary.simpleMessage("Còn lại: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Số dư còn lại"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Số dư còn lại"),
        "reports": MessageLookupByLibrary.simpleMessage("Báo cáo"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Đặt lại mật khẩu của bạn"),
        "retailer": MessageLookupByLibrary.simpleMessage("Nhà bán lẻ"),
        "revenue": MessageLookupByLibrary.simpleMessage("Doanh thu"),
        "right": MessageLookupByLibrary.simpleMessage("Phải"),
        "sAmount":
            MessageLookupByLibrary.simpleMessage("Tổng Giá Trị Bán Hàng"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Bảo vệ dữ liệu kinh doanh của bạn một cách dễ dàng. Gói nâng cấp không giới hạn của chúng tôi bao gồm sao lưu dữ liệu miễn phí, đảm bảo thông tin quý báu của bạn được bảo vệ trước bất kỳ sự kiện bất ngờ nào. Tập trung vào những gì thực sự quan trọng - sự phát triển kinh doanh của bạn."),
        "sale": MessageLookupByLibrary.simpleMessage("Bán hàng"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Số tiền bán"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Chi tiết bán hàng"),
        "saleList": MessageLookupByLibrary.simpleMessage("Danh sách bán hàng"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Giá bán"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Giá Bán*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Trả lại bán hàng"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch bán hàng"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Lịch sử giao dịch bán hàng (lịch sử bán hàng theo báo giá)"),
        "sales": MessageLookupByLibrary.simpleMessage("Bán hàng"),
        "salesList": MessageLookupByLibrary.simpleMessage("Danh sách bán hàng"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Lưu và xuất bản"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Lưu và xuất bản"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Lưu thay đổi"),
        "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Tìm kiếm bất cứ thứ gì..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Tìm kiếm theo hóa đơn...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Tìm kiếm theo hóa đơn hoặc tên"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Tìm theo Tên"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Tìm kiếm theo tên hoặc điện thoại..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Tìm kiếm Số Sê-ri"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Chọn các bên"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Chọn Thương hiệu Sản phẩm"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Chọn Số Sê-ri"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Chọn Tùy chọn:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Chọn Thời gian Bảo hành"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Chọn ngôn ngữ của bạn"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Gửi tin nhắn"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Số sê-ri"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Số Sê-ri"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Phí dịch vụ"),
        "setting": MessageLookupByLibrary.simpleMessage("Cài đặt"),
        "share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Vận chuyển/Khác"),
        "shopName": MessageLookupByLibrary.simpleMessage("Tên cửa hàng"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Số dư mở cửa hàng"),
        "show": MessageLookupByLibrary.simpleMessage("Hiển thị >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Hiển thị logo trong hóa đơn?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Vận chuyển/Dịch vụ"),
        "size": MessageLookupByLibrary.simpleMessage("Kích thước"),
        "statistic": MessageLookupByLibrary.simpleMessage("Thống Kê"),
        "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Hãy ở vị trí hàng đầu của sự tiến bộ công nghệ mà không có chi phí bổ sung. Gói nâng cấp Pos Sass POS Unlimited của chúng tôi đảm bảo rằng bạn luôn có các công cụ và tính năng mới nhất ngay tại ngón tay bạn, đảm bảo rằng doanh nghiệp của bạn luôn ở đẳng cấp hàng đầu."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Hãy ở vị trí hàng đầu của sự tiến bộ công nghệ mà không có chi phí bổ sung. Gói nâng cấp Pos Sass POS Unlimited của chúng tôi đảm bảo rằng bạn luôn có các công cụ và tính năng mới nhất ngay tại ngón tay bạn, đảm bảo rằng doanh nghiệp của bạn luôn ở đẳng cấp hàng đầu."),
        "stock": MessageLookupByLibrary.simpleMessage("Tồn kho"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("Tồn kho"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Báo cáo tồn kho"),
        "stockValue":
            MessageLookupByLibrary.simpleMessage("Giá trị hàng tồn kho"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Giá Trị Kho"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Tổng cộng"),
        "subciption": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "submit": MessageLookupByLibrary.simpleMessage("Nộp"),
        "supplier": MessageLookupByLibrary.simpleMessage("Nhà cung cấp"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Nợ nhà cung cấp"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Hóa đơn của nhà cung cấp"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Danh sách nhà cung cấp"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Mã SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Tổng Doanh Số Bán Hàng"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Chụp ảnh giấy phép lái xe, thẻ căn cước quốc gia hoặc hộ chiếu"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Điều khoản sử dụng"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Tên nói lên tất cả. Với Pos Saas POS Unlimited, không có giới hạn về việc sử dụng của bạn. Dù bạn đang xử lý một số giao dịch hoặc đối mặt với một lượng khách hàng đông đảo, bạn có thể hoạt động một cách tự tin, biết rằng bạn không bị ràng buộc bởi giới hạn."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Khách hàng này không có khoản phải trả"),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("Khách hàng này có nợ trước"),
        "to": MessageLookupByLibrary.simpleMessage("Đến"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Sản phẩm bán chạy nhất"),
        "total": MessageLookupByLibrary.simpleMessage("Tổng"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Tổng số tiền"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Tổng giảm giá"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Tổng số dư"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Tổng số dư"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Tổng chi phí"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Tổng thu nhập"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Tổng số lượng: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Tổng lỗ"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Tổng thanh toán"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Tổng phải trả"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Tổng tiền thanh toán ra"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Tổng giá"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Tổng số Sản phẩm"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Tổng lợi nhuận"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Tổng mua hàng"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Tổng số tiền trả lại"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Tổng số trả lại"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Tổng doanh thu"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Tổng doanh thu"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Tổng Thuế GTGT"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Tổng tiền thanh toán vào"),
        "transaction": MessageLookupByLibrary.simpleMessage("Giao dịch"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Mã giao dịch"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Báo cáo giao dịch"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "type": MessageLookupByLibrary.simpleMessage("Loại"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Chưa trả"),
        "unit": MessageLookupByLibrary.simpleMessage("Đơn vị"),
        "unitName": MessageLookupByLibrary.simpleMessage("Tên Đơn vị"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Đơn giá"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Không giới hạn"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Hóa đơn không giới hạn"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Sử dụng không giới hạn"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Mở khóa tiềm năng đầy đủ của Pos Saas POS với các buổi đào tạo cá nhân do đội ngũ chuyên gia của chúng tôi dẫn dắt. Từ cơ bản đến các kỹ thuật tiên tiến, chúng tôi đảm bảo bạn đã hiểu rõ cách sử dụng mọi khía cạnh của hệ thống để tối ưu hóa quy trình kinh doanh của bạn."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Cập nhật ngay"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Hãy cập nhật kế hoạch của bạn trước\\nGiới hạn bán hàng đã vượt quá."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Nâng cấp trên ứng dụng di động"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Tải lên hình ảnh"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Tải lên logo hóa đơn"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Tải tài liệu lên"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Tải tệp lên"),
        "userName": MessageLookupByLibrary.simpleMessage("Tên người dùng"),
        "userRole": MessageLookupByLibrary.simpleMessage("Vai trò người dùng"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Tên vai trò người dùng"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Tiêu đề người dùng"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("VAT/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Xác minh số điện thoại"),
        "view": MessageLookupByLibrary.simpleMessage("Xem"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Khách vãng lai"),
        "warranty": MessageLookupByLibrary.simpleMessage("Bảo hành"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Bảo hành"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi cần đăng ký điện thoại của bạn trước khi bắt đầu!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi hiểu tầm quan trọng của việc vận hành liền mạch. Đó là lý do tại sao chúng tôi có sẵn hỗ trợ suốt ngày đêm để hỗ trợ bạn, cho dù đó là một truy vấn nhanh chóng hoặc một mối quan tâm toàn diện. Kết nối với chúng tôi bất cứ lúc nào, bất cứ nơi đâu qua cuộc gọi hoặc WhatsApp để trải nghiệm dịch vụ khách hàng vô song."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Giá Bán Sỉ"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Đại lý bán buôn"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Sỉ"),
        "wight": MessageLookupByLibrary.simpleMessage("Trọng lượng"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Có, trả lại"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Bạn phải ĐĂNG NHẬP lại vào tài khoản của bạn."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Bạn cần xác minh danh tính trước khi mua tin nhắn"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Danh sách tất cả các giao dịch bán hàng của bạn"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage(
            "Tất cả các giao dịch bán hàng của bạn"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Bạn đang sử dụng"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Các khoản phải thu của bạn"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Bạn cần xác minh danh tính trước khi mua tin nhắn"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Gói của bạn"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "Thanh toán của bạn đã bị hủy"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Thanh toán của bạn đã thành công"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Thanh toán của bạn đã bị hủy")
      };
}
