// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ro locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ro';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("ADAUGĂ VÂNZARE"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATEGORIE"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FACTURĂ"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Vânzare POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRET"),
        "PRODUCTNAME":
            MessageLookupByLibrary.simpleMessage("NUMELE PRODUSULUI"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Panoul de conectare Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("QTY"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Cantitate*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("VALOARE TOTALĂ"),
        "UserTitle":
            MessageLookupByLibrary.simpleMessage("Titlul utilizatorului"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Despre aplicație"),
        "accountName": MessageLookupByLibrary.simpleMessage("Nume cont"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Număr cont"),
        "action": MessageLookupByLibrary.simpleMessage("Acțiune"),
        "add": MessageLookupByLibrary.simpleMessage("Adăuga"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Adăugați marcă"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Adăugați categorie"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Adaugă client"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Adăugați descriere..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Adăugați documente"),
        "addItem": MessageLookupByLibrary.simpleMessage("Adaugă element"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage(
            "Adăugați o categorie de articole"),
        "addNew": MessageLookupByLibrary.simpleMessage("Adăugați nou"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Adăugați un utilizator nou"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Adăugați produs"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Adăugat cu succes"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Adaugă furnizor"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Adăugați unitate"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Adăugați/Actualizați lista de cheltuieli"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Adăugați/Actualizați lista de venituri"),
        "addUserRole": MessageLookupByLibrary.simpleMessage(
            "Adăugați rolul utilizatorului"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Adăugare număr de serie?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresa"),
        "all": MessageLookupByLibrary.simpleMessage("Toate"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Toate funcțiile de bază"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Aveți deja un cont?"),
        "amount": MessageLookupByLibrary.simpleMessage("Sumă"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Suport pentru aplicații Android și iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Doriți să creați această ofertă?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Vreți să ștergeți acest client?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Doriți să ștergeți acest produs"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Vreți să ștergeți această ofertă?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Doriți să returnați această vânzare?"),
        "balance": MessageLookupByLibrary.simpleMessage("Echilibru"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Monedă cont bancar"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Conturi bancare"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Informații bancare"),
        "bankName": MessageLookupByLibrary.simpleMessage("Numele băncii"),
        "between": MessageLookupByLibrary.simpleMessage("Între"),
        "billTo": MessageLookupByLibrary.simpleMessage("Factură către:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Nume filială"),
        "brand": MessageLookupByLibrary.simpleMessage("Marcă"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nume de marcă"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Categorie de afaceri"),
        "buy": MessageLookupByLibrary.simpleMessage("Cumpărați"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Cumpărați planul premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Cumpărați sms"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calculator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Cameră"),
        "cancel": MessageLookupByLibrary.simpleMessage("Anulare"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacitate"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Cash și bancă"),
        "cashInHand":
            MessageLookupByLibrary.simpleMessage("Numerar la îndemână"),
        "categories": MessageLookupByLibrary.simpleMessage("Categorii"),
        "category": MessageLookupByLibrary.simpleMessage("Categorie"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Numele categoriei"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Suma de rest"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Sumă variabilă"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Verifică garanția"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Alegeți un plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Colectarea plăților >"),
        "color": MessageLookupByLibrary.simpleMessage("Culoare"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Numele companiei"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Adresa companiei"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Descrierea companiei"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Adresă de e-mail a companiei"),
        "companyName": MessageLookupByLibrary.simpleMessage("Numele companiei"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Număr de telefon al companiei"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "URL-ul site-ului web al companiei"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmați parola"),
        "continu": MessageLookupByLibrary.simpleMessage("Continua"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Convertiți la vânzare"),
        "create": MessageLookupByLibrary.simpleMessage("Creați"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Creați plată"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Creat de"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Hub creativ"),
        "currency": MessageLookupByLibrary.simpleMessage("Moneda"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Planul curent"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Brand personalizat pentru facturi"),
        "customer": MessageLookupByLibrary.simpleMessage("Client"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Datorie client"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Facturi de client"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Lista de clienți"),
        "customerName":
            MessageLookupByLibrary.simpleMessage("Numele clientului"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Clientul lunii"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tip client"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Client: Client intrare"),
        "customers": MessageLookupByLibrary.simpleMessage("Clienți"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Colectarea zilnică"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Vânzări zilnice"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Tranzacție zilnică"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Tablou de bord"),
        "date": MessageLookupByLibrary.simpleMessage("Data"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Data și ora"),
        "dealer": MessageLookupByLibrary.simpleMessage("Dealer"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Preț de dealer"),
        "delete": MessageLookupByLibrary.simpleMessage("Șterge"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Taxă de livrare"),
        "description": MessageLookupByLibrary.simpleMessage("Descriere"),
        "details": MessageLookupByLibrary.simpleMessage("Detalii >"),
        "discount": MessageLookupByLibrary.simpleMessage("Reducere"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Preț cu reducere"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Descarcă PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Datorată"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Suma restantă"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Suma restantă va fi afișată aici dacă este disponibilă"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Colectarea restantelor"),
        "dueList": MessageLookupByLibrary.simpleMessage("Lista restantelor"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Tranzacție scadentă"),
        "edit": MessageLookupByLibrary.simpleMessage("Editare"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Editare/Adăugare serie:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Editați-vă profilul"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Introduceți suma"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Introduceți numele mărcii"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele categoriei"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Introduceți descrierea companiei"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Introduceți adresa de e-mail a companiei"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Introduceți numărul de telefon al companiei"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Introduceți URL-ul site-ului web al companiei"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele clientului"),
        "enterDealePrice": MessageLookupByLibrary.simpleMessage(
            "Introduceți prețul de dealer"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Introduceți prețul cu reducere"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Introduceți categoria de cheltuieli"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage(
            "Introduceți data cheltuielii"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Introduceți categoria de venit"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Introduceți data venitului"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele producătorului"),
        "enterName": MessageLookupByLibrary.simpleMessage("Introduceți numele"),
        "enterNames":
            MessageLookupByLibrary.simpleMessage("Introduceți numele"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Introduceți nota"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Introduceți soldul de deschidere"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Introduceți suma plătită"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Introduceți parola"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Introduceți suma de plată"),
        "enterPrice":
            MessageLookupByLibrary.simpleMessage("Introduceți prețul"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Introduceți capacitatea produsului"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage(
            "Introduceți codul produsului"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage(
            "Introduceți culoarea produsului"),
        "enterProductName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele produsului"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Introduceți cantitatea produsului"),
        "enterProductSize": MessageLookupByLibrary.simpleMessage(
            "Introduceți dimensiunea produsului"),
        "enterProductType": MessageLookupByLibrary.simpleMessage(
            "Introduceți tipul produsului"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage(
            "Introduceți unitatea de produs"),
        "enterProductWeight": MessageLookupByLibrary.simpleMessage(
            "Introduceți greutatea produsului"),
        "enterPurchasePrice": MessageLookupByLibrary.simpleMessage(
            "Introduceți prețul de achiziție"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Introduceți numărul de referință"),
        "enterSalePrice": MessageLookupByLibrary.simpleMessage(
            "Introduceți prețul de vânzare"),
        "enterSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Introduceți numărul de serie"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Introduceți conținutul mesajului"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Introduceți cantitatea de stoc"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Introduceți ID-ul tranzacției"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Introduceți numele unității"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele rolului utilizatorului"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Introduceți titlul utilizatorului"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage(
            "Introduceți durata de garanție"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Introduceți prețul en-gros"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Introduceți suma dvs."),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Introduceți adresa dvs."),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Introduceți adresa companiei dumneavoastră"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele companiei dvs."),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele companiei dvs."),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Introduceți adresa dvs. de e-mail"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Introduceți parola dvs."),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Introduceți din nou parola dvs."),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Introduceți numărul dvs. de telefon"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele magazinului dvs."),
        "entercategoryName": MessageLookupByLibrary.simpleMessage(
            "Introduceți numele categoriei"),
        "expense": MessageLookupByLibrary.simpleMessage("Cheltuieli"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Data cheltuielii"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detaliile cheltuielilor"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Cheltuire pentru"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista de categorii de cheltuieli"),
        "expenses": MessageLookupByLibrary.simpleMessage("Cheltuieli"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top cinci produse cumpărate în luna curentă"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Pentru utilizări nelimitate"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Ați uitat parola?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Backup gratuit de date"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Actualizare gratuită pe viață"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Pachet gratuit"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Plan gratuit"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Începe"),
        "govermentId": MessageLookupByLibrary.simpleMessage("ID-ul guvernului"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Total general"),
        "hold": MessageLookupByLibrary.simpleMessage("Reținere"),
        "holdNumber":
            MessageLookupByLibrary.simpleMessage("Numărul de reținere"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verifica identitatea"),
        "inc": MessageLookupByLibrary.simpleMessage("Venit"),
        "income": MessageLookupByLibrary.simpleMessage("Venit"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Categorie de venituri"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista de categorii de venituri"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Data venitului"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detaliile veniturilor"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Venit pentru"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Lista de venituri"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Creșteți stocul"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage(
            "Confidențialitate instantanee"),
        "invoice": MessageLookupByLibrary.simpleMessage("Factură"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Factură:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Factură Nr.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Număr factură"),
        "item": MessageLookupByLibrary.simpleMessage("Element"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nume articol"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Verificare KYC"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Detalii sold"),
        "ledger": MessageLookupByLibrary.simpleMessage("Registru"),
        "left": MessageLookupByLibrary.simpleMessage("Stânga"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Conturi de împrumut"),
        "logOut": MessageLookupByLibrary.simpleMessage("Delogare"),
        "login": MessageLookupByLibrary.simpleMessage("Autentificare"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Poziția logo-ului în factură?"),
        "loss": MessageLookupByLibrary.simpleMessage("Pierdere"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Pierdere/Profit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Pierdere (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Stoc scăzut"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Stocuri reduse"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Lasati o impresie de durata asupra clientilor dvs. cu facturi personalizate. Actualizarea nelimitata ofera avantajul unic de a personaliza facturile dvs., adaugand un aspect profesional care consolideaza identitatea brandului dvs. si dezvolta loialitatea clientilor."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Producător"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Panou de conectare Pos Saas"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Panou de înregistrare Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Aplicație mobilă\n+\nDesktop"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Chitanță de bani"),
        "nam": MessageLookupByLibrary.simpleMessage("Nume*"),
        "name": MessageLookupByLibrary.simpleMessage("Nume"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nume, cod sau categorie"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Noi clienți"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Noi clienți"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Venit nou"),
        "no": MessageLookupByLibrary.simpleMessage("Nu"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Fără conexiune"),
        "noCustomerFound": MessageLookupByLibrary.simpleMessage(
            "Nu a fost găsit niciun client"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nicio tranzacție de plată neînregistrată"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nicio categorie de cheltuieli găsită"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nicio categorie de venituri găsită"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Niciun venit găsit"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nicio factură găsită"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Niciun produs găsit"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Niciuna tranzacție de achiziție găsită"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nicio ofertă găsită"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("NU s-a găsit niciun raport"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nicio tranzacție de vânzare găsită"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nici un număr de serie găsit"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Niciun furnizor găsit"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nicio tranzacție găsită"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Niciun utilizator găsit"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Niciun rol de utilizator găsit"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nici un număr de serie găsit"),
        "note": MessageLookupByLibrary.simpleMessage("Notă"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Checuri deschise"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Balanță de deschidere"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "sau glisați și plasați PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Comenzi"),
        "other": MessageLookupByLibrary.simpleMessage("Altele"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Alte venituri"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Caracteristicile pachetului"),
        "paid": MessageLookupByLibrary.simpleMessage("Platit"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Suma plătită"),
        "partyName": MessageLookupByLibrary.simpleMessage("Numele părții"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tipul de parte"),
        "password": MessageLookupByLibrary.simpleMessage("Parola"),
        "payCash": MessageLookupByLibrary.simpleMessage("Plătiți cu numerar"),
        "payable": MessageLookupByLibrary.simpleMessage("De plată"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Suma plătită"),
        "payment": MessageLookupByLibrary.simpleMessage("Plată"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Plată în"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Plată în afară"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Tipul de plată"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Tipuri de plată"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Număr de telefon"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verificare telefonică"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să adăugați o vânzare"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Adaugă client"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Verificați conexiunea la internet"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să descărcați aplicația noastră mobilă și să vă abonați la un pachet pentru a utiliza versiunea desktop"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să introduceți stocul de produse"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să introduceți date valide"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să selectați un client"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să introduceți date valide"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Panoul de înregistrare Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Practică"),
        "premiumCustomerSupport": MessageLookupByLibrary.simpleMessage(
            "Suport premium pentru clienți"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Plan premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Previzualizare"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Datorată anterioară:"),
        "price": MessageLookupByLibrary.simpleMessage("Preț"),
        "print": MessageLookupByLibrary.simpleMessage("Tipărire"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Tipărire factură"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Imprimez PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage(
            "Politica de confidențialitate"),
        "product": MessageLookupByLibrary.simpleMessage("Produs"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Categorie de produse"),
        "productCod": MessageLookupByLibrary.simpleMessage("Codul produsului*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Culoare produs"),
        "productList": MessageLookupByLibrary.simpleMessage("Lista de produse"),
        "productNam":
            MessageLookupByLibrary.simpleMessage("Numele produsului*"),
        "productName":
            MessageLookupByLibrary.simpleMessage("Numele produsului"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Dimensiune produs"),
        "productStock": MessageLookupByLibrary.simpleMessage("Stoc de produse"),
        "productType": MessageLookupByLibrary.simpleMessage("Tip produs"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Unitatea produsului"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garanția produsului"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Greutate produs"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Capacitate produs"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Editare profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profit(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profit(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Achiziție"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Listă de achiziții"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Cumpărați planul premium"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Preț de achiziție"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Tranzacție de achiziție"),
        "quantity": MessageLookupByLibrary.simpleMessage("Cantitate"),
        "quotation": MessageLookupByLibrary.simpleMessage("Ofertă"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Listă de oferte"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Vânzări recente"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Suma primită"),
        "referenceNo":
            MessageLookupByLibrary.simpleMessage("Număr de referință"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Numărul de referință"),
        "registration": MessageLookupByLibrary.simpleMessage("Înregistrare"),
        "remaining": MessageLookupByLibrary.simpleMessage("Rămase: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("Sold rămas"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Datorie restantă"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapoarte"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Resetați parola dvs."),
        "retailer": MessageLookupByLibrary.simpleMessage("Retailer"),
        "revenue": MessageLookupByLibrary.simpleMessage("Venituri"),
        "right": MessageLookupByLibrary.simpleMessage("Dreapta"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Suma vânzărilor"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Protejati datele afacerii dvs. fara efort. Actualizarea nelimitata a Pos Saas POS include backup gratuit de date, asigurand ca informatiile dvs. valoroase sunt protejate impotriva evenimentelor neprevazute. Concentrati-va pe ceea ce conteaza cu adevarat - cresterea afacerii dvs."),
        "sale": MessageLookupByLibrary.simpleMessage("Vânzare"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Suma vânzărilor"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Detalii vânzare"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista de vânzări"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Preț de vânzare"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Preț de vânzare*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Returnare vânzare"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Tranzacție vânzare"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Istoric tranzacții vânzare (Istoric vânzare cotație)"),
        "sales": MessageLookupByLibrary.simpleMessage("Vânzări"),
        "salesList": MessageLookupByLibrary.simpleMessage("Listă de vânzări"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Salvează și publică"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Salvează și publică"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Salvează modificările"),
        "search": MessageLookupByLibrary.simpleMessage("Căutare......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Căutați orice..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Căutați după factură..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Căutare după factură sau nume"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Căutare după nume"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Căutare după nume sau telefon..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Căutare număr de serie"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Selectați părți"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Selectați marca produsului"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Selectați numărul de serie"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Selectați variațiile:"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage(
            "Selectați perioada de garanție"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Selectați limba dvs."),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Trimite mesaj"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Număr de serie"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Număr de serie"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Taxa de serviciu"),
        "setting": MessageLookupByLibrary.simpleMessage("Setări"),
        "share": MessageLookupByLibrary.simpleMessage("Distribuie"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Transport/Alte"),
        "shopName": MessageLookupByLibrary.simpleMessage("Numele magazinului"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Balanta de deschidere a magazinului"),
        "show": MessageLookupByLibrary.simpleMessage("Afișare >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Afișează logo-ul în factură?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Livrare/Servicii"),
        "size": MessageLookupByLibrary.simpleMessage("Dimensiune"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistică"),
        "status": MessageLookupByLibrary.simpleMessage("Stare"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Rămâneți în avangarda avanțărilor tehnologice fără costuri suplimentare. Actualizarea nelimitată a POS-ului nostru Pos Saas vă asigură că aveți întotdeauna cele mai recente instrumente și funcționalități la îndemână, garantând că afacerea dvs. rămâne la ultimele tehnologii."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Rămâneți în avangarda avanțărilor tehnologice fără costuri suplimentare. Actualizarea nelimitată a Pos Sass POS vă asigură că aveți întotdeauna cele mai recente instrumente și funcționalități la îndemână, garantând că afacerea dvs. rămâne la ultimele tehnologii."),
        "stock": MessageLookupByLibrary.simpleMessage("Stoc"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventar de stoc"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Raport de stoc"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Valoarea stocului"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Valoare stoc"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abonament"),
        "submit": MessageLookupByLibrary.simpleMessage("Trimite"),
        "supplier": MessageLookupByLibrary.simpleMessage("Furnizori"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Datorie furnizor"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Factura furnizorului"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lista de furnizori"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Cod SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Vânzări totale"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Faceți o fotografie cu permisul de conducere, cartea de identitate națională sau pașaportul"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Termeni de utilizare"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Numele spune totul. Cu Pos Saas POS Unlimited, nu exista limita pentru utilizarea dvs. Fie ca procesati o mana de tranzactii sau aveti o avalansa de clienti, puteti opera cu incredere, stiind ca nu sunteti limitat de constrangeri."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Acest client nu are restante"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Acest client are o datorie anterioară"),
        "to": MessageLookupByLibrary.simpleMessage("La"),
        "topSellingProduct": MessageLookupByLibrary.simpleMessage(
            "Produsul cel mai bine vândut"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Valoare totală"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Total reducere"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total de plată"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Total datorii"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("Total cheltuieli"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Venit total"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Total elemente: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Pierdere totală"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Plată totală"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total de plată"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Plată totală în afară"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Preț total"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Total produse"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Profit total"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Total achiziții"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Suma totală a returnurilor"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Total returnuri"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total vânzări"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Total vânzări"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total TVA"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Plată totală în"),
        "transaction": MessageLookupByLibrary.simpleMessage("Tranzacție"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID tranzacție"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Raport tranzacție"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Încercați din nou"),
        "type": MessageLookupByLibrary.simpleMessage("Tip"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Neplătit"),
        "unit": MessageLookupByLibrary.simpleMessage("Unitate"),
        "unitName": MessageLookupByLibrary.simpleMessage("Numele unității"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Preț unitar"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Nelimitat"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Facturi nelimitate"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Utilizare nelimitată"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Deblocati intregul potential al Pos Saas POS cu sesiuni personalizate de formare conduse de echipa noastra de experti. De la notiuni de baza la tehnici avansate, ne asiguram ca sunteti bine pregatit pentru a utiliza fiecare aspect al sistemului pentru a va optimiza procesele de afaceri."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Actualizați acum"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Actualizați planul dvs. mai întâi\\nLimita de vânzare este depășită."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Actualizați pe aplicația mobilă"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Încărcați o imagine"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "Încarcă un logo pentru factură"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Încărcați documentul"),
        "uploadFile":
            MessageLookupByLibrary.simpleMessage("Încărcați fișierul"),
        "userName": MessageLookupByLibrary.simpleMessage("Nume utilizator"),
        "userRole":
            MessageLookupByLibrary.simpleMessage("Rolul utilizatorului"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nume rol utilizator"),
        "userTitle":
            MessageLookupByLibrary.simpleMessage("Titlul utilizatorului"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("TVA/GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Verificați numărul de telefon"),
        "view": MessageLookupByLibrary.simpleMessage("Vezi"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Client intrare"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantie"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garanii"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Trebuie să vă înregistrăm telefonul înainte de a începe!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Înțelegem importanța funcționării fără probleme. De aceea, suportul nostru disponibil non-stop este disponibil pentru a vă ajuta, fie că este vorba de o întrebare rapidă sau de o îngrijorare cuprinzătoare. Conectați-vă cu noi oricând și oriunde prin apel sau WhatsApp pentru a experimenta un serviciu de neegalat pentru clienți."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Preț de distribuție"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Engrossist"),
        "wholesale": MessageLookupByLibrary.simpleMessage("En-gros"),
        "wight": MessageLookupByLibrary.simpleMessage("Greutate"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Da, returnează"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Trebuie să vă reconectați la contul dvs."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Trebuie să vă verificați identitatea înainte de a cumpăra mesaje"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Lista dvs. cu toate vânzările"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Toate vânzările"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Folosiți"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Vânzări restante"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Trebuie să vă verificați identitatea înainte de a cumpăra mesaje"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Pachetul dvs."),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "Plata dumneavoastră a fost anulată"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Plata dumneavoastră a fost efectuată cu succes"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Plata dvs. a fost anulată")
      };
}
