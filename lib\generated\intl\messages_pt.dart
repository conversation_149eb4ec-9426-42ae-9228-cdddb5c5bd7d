// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("ADICIONAR VENDA"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATEGORIA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("NOTA FISCAL"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Venda POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PREÇO"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NOME DO PRODUTO"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Painel de Login do Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("QTD"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Quantidade*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("VALOR TOTAL"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Título de Usuário"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Sobre o Aplicativo"),
        "accountName": MessageLookupByLibrary.simpleMessage("Nome da Conta"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Número da Conta"),
        "action": MessageLookupByLibrary.simpleMessage("Ação"),
        "add": MessageLookupByLibrary.simpleMessage("Adicionar"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Adicionar Marca"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Adicionar categoria"),
        "addCustomer":
            MessageLookupByLibrary.simpleMessage("Adicionar cliente"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Adicionar descrição..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Adicionar Documento"),
        "addItem": MessageLookupByLibrary.simpleMessage("Adicionar Item"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Adicionar Categoria de Item"),
        "addNew": MessageLookupByLibrary.simpleMessage("Adicionar Novo"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Adicionar Novo Usuário"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Adicionar Produto"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Adicionado com Sucesso"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Adicionar Fornecedor"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Adicionar Unidade"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Adicionar/Atualizar lista de despesas"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Adicionar/Atualizar Lista de Receitas"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Adicionar Função de Usuário"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Adicionando número de série?"),
        "address": MessageLookupByLibrary.simpleMessage("Endereço"),
        "all": MessageLookupByLibrary.simpleMessage("Todos"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Todas as funcionalidades básicas"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Já tem uma conta?"),
        "amount": MessageLookupByLibrary.simpleMessage("Quantidade"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Suporte para Aplicativos Android e iOS"),
        "areYouWantToCreateThisQuation":
            MessageLookupByLibrary.simpleMessage("Deseja criar esta cotação?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Tem certeza que deseja excluir este cliente?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Você deseja excluir este produto"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Você quer deletar esta cotação?"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("Deseja devolver esta venda?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Moeda da Conta Bancária"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Contas bancárias"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Informações Bancárias"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nome do Banco"),
        "between": MessageLookupByLibrary.simpleMessage("Entre"),
        "billTo": MessageLookupByLibrary.simpleMessage("Fatura para:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Nome da Agência"),
        "brand": MessageLookupByLibrary.simpleMessage("Marca"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nome da Marca"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Categoria de Negócio"),
        "buy": MessageLookupByLibrary.simpleMessage("Comprar"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Comprar Plano Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Comprar SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calculadora:"),
        "camera": MessageLookupByLibrary.simpleMessage("Câmera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacidade"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Dinheiro e banco"),
        "cashInHand":
            MessageLookupByLibrary.simpleMessage("Dinheiro em espécie"),
        "categories": MessageLookupByLibrary.simpleMessage("Categorias"),
        "category": MessageLookupByLibrary.simpleMessage("Categoria"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Nome da categoria"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Troco"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Quantidade mutável"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Verificar Garantia"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Escolha um plano"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Cobrar dívida >"),
        "color": MessageLookupByLibrary.simpleMessage("Cor"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Nome da empresa"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Endereço da Empresa"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Descrição da Empresa"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Endereço de E-mail da Empresa"),
        "companyName": MessageLookupByLibrary.simpleMessage("Nome da Empresa"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Número de Telefone da Empresa"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL do Website da Empresa"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmar senha"),
        "continu": MessageLookupByLibrary.simpleMessage("Continuar"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Converter para Venda"),
        "create": MessageLookupByLibrary.simpleMessage("Criar"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Criar pagamento"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Criado Por"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Hub criativo"),
        "currency": MessageLookupByLibrary.simpleMessage("Moeda"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Plano atual"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Marcação de Faturas Personalizadas"),
        "customer": MessageLookupByLibrary.simpleMessage("Cliente"),
        "customerDue":
            MessageLookupByLibrary.simpleMessage("Débito do Cliente"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Faturas de Clientes"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Lista de clientes"),
        "customerName": MessageLookupByLibrary.simpleMessage("Nome do cliente"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Cliente do mês"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tipo de Cliente"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Cliente: Cliente de balcão"),
        "customers": MessageLookupByLibrary.simpleMessage("Clientes"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Cobrança diária"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Vendas diárias"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transação diária"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Painel"),
        "date": MessageLookupByLibrary.simpleMessage("Data"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Data e Hora"),
        "dealer": MessageLookupByLibrary.simpleMessage("Revendedor"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Preço de Revenda"),
        "delete": MessageLookupByLibrary.simpleMessage("Excluir"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Taxa de Entrega"),
        "description": MessageLookupByLibrary.simpleMessage("Descrição"),
        "details": MessageLookupByLibrary.simpleMessage("Detalhes >"),
        "discount": MessageLookupByLibrary.simpleMessage("Desconto"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Preço com Desconto"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Download PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Devido"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Valor devido"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "O valor devido será mostrado aqui, se disponível"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Cobrança de dívidas"),
        "dueList": MessageLookupByLibrary.simpleMessage("Lista de pendências"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transação pendente"),
        "edit": MessageLookupByLibrary.simpleMessage("Editar"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Editar/Adicionar série:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Editar seu perfil"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Inserir Valor"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Digite o nome da marca"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Digite o nome da categoria"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Insira a Descrição da Empresa"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Insira o Endereço de E-mail da Empresa"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Insira o Número de Telefone da Empresa"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Insira a URL do Website da Empresa"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Insira o nome do cliente"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Digite o preço de revenda"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Insira o Preço com Desconto"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Insira a categoria de despesa"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Insira a data da despesa"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Insira a categoria de receita"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Inserir Data da Receita"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Digite o nome do fabricante"),
        "enterName": MessageLookupByLibrary.simpleMessage("Insira o nome"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Inserir Nomes"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Insira uma nota"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Insira o saldo de abertura"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Insira o valor pago"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Inserir Senha"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Digite o valor pago"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Digite o preço"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Digite a capacidade do produto"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Digite o código do produto"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Digite a cor do produto"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Digite o nome do produto"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Digite a quantidade do produto"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Digite o tamanho do produto"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Digite o tipo de produto"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Digite a unidade do produto"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Digite o peso do produto"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Digite o preço de compra"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Insira o número de referência"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Digite o preço de venda"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Digite o número de série"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Insira o Conteúdo da Mensagem"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Insira a Quantidade em Estoque"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Inserir ID da Transação"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Digite o nome da unidade"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Inserir Nome da Função de Usuário"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Inserir Título de Usuário"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Digite a garantia"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Digite o preço de atacado"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Insira o seu valor"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Insira o seu endereço"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Insira o Endereço da Sua Empresa"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Insira o nome da sua empresa"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Insira o nome da sua empresa"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Insira o seu endereço de e-mail"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Insira a sua senha"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Insira sua senha novamente"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Insira o seu número de telefone"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Insira o nome da sua loja"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Insira o nome da categoria"),
        "expense": MessageLookupByLibrary.simpleMessage("Despesa"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Data da despesa"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detalhes da despesa"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Despesa para"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista de categorias de despesas"),
        "expenses": MessageLookupByLibrary.simpleMessage("Despesas"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Cinco produtos mais comprados do mês"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Para Usos Ilimitados"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Esqueceu a senha?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Backup de Dados Gratuito"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Atualização Vitalícia Gratuita"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Pacote Gratuito"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Plano Gratuito"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Começar"),
        "govermentId": MessageLookupByLibrary.simpleMessage("ID do Governo"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Total geral"),
        "hold": MessageLookupByLibrary.simpleMessage("Aguardando"),
        "holdNumber":
            MessageLookupByLibrary.simpleMessage("Número de Aguardando"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verificar Identidade"),
        "inc": MessageLookupByLibrary.simpleMessage("Renda"),
        "income": MessageLookupByLibrary.simpleMessage("Receita"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Categoria de Receita"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista de Categorias de Receita"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Data da Receita"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detalhes da Receita"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Receita Para"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Lista de Receitas"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Aumentar Estoque"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Privacidade instantânea"),
        "invoice": MessageLookupByLibrary.simpleMessage("Fatura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Fatura:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Número da fatura..."),
        "invoiceNo":
            MessageLookupByLibrary.simpleMessage("Número da Nota Fiscal"),
        "item": MessageLookupByLibrary.simpleMessage("Item"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nome do Item"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Verificação KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Detalhes do Livro Razão"),
        "ledger": MessageLookupByLibrary.simpleMessage("Registro"),
        "left": MessageLookupByLibrary.simpleMessage("Esquerda"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Contas de empréstimo"),
        "logOut": MessageLookupByLibrary.simpleMessage("Sair"),
        "login": MessageLookupByLibrary.simpleMessage("Entrar"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Posição do Logotipo na Fatura?"),
        "loss": MessageLookupByLibrary.simpleMessage("Perda"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Perda/Lucro"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Perda(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Baixo Estoque"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Estoques baixos"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Crie uma impressão duradoura em seus clientes com faturas personalizadas. Nossa Atualização Ilimitada oferece a vantagem única de personalizar suas faturas, adicionando um toque profissional que reforça a identidade da sua marca e promove a fidelidade do cliente."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Fabricante"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Painel de login do Pos Saas"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Painel de inscrição do Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Aplicativo Móvel\n+\nDesktop"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Recibo de Dinheiro"),
        "nam": MessageLookupByLibrary.simpleMessage("Nome*"),
        "name": MessageLookupByLibrary.simpleMessage("Nome"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nome, Código ou Categoria"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Novos clientes"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Novos clientes"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nova Receita"),
        "no": MessageLookupByLibrary.simpleMessage("Não"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Sem Conexão"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nenhum cliente encontrado"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma transação de dívida encontrada"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma categoria de despesa encontrada"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma Categoria de Receita Encontrada"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nenhuma Receita Encontrada"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nenhuma Fatura Encontrada"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nenhum produto encontrado"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma transação de compra encontrada"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nenhuma cotação encontrada"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Nenhum relatório encontrado"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma Transação de Venda Encontrada"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nenhum Número de Série Encontrado"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "Nenhum Fornecedor Encontrado"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma Transação Encontrada"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Nenhum Usuário Encontrado"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Nenhuma Função de Usuário Encontrada"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nenhum número de série encontrado"),
        "note": MessageLookupByLibrary.simpleMessage("Nota"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Cheques abertos"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo de abertura"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("ou arraste e solte PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Pedidos"),
        "other": MessageLookupByLibrary.simpleMessage("Outro"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Outra receita"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Recursos do Pacote"),
        "paid": MessageLookupByLibrary.simpleMessage("Pago"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Valor Pago"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nome da festa"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tipo de festa"),
        "password": MessageLookupByLibrary.simpleMessage("Senha"),
        "payCash": MessageLookupByLibrary.simpleMessage("Pagar em Dinheiro"),
        "payable": MessageLookupByLibrary.simpleMessage("A Pagar"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Valor pago"),
        "payment": MessageLookupByLibrary.simpleMessage("Pagamento"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Pagamento recebido"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Pagamento feito"),
        "paymentType":
            MessageLookupByLibrary.simpleMessage("Tipo de pagamento"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Tipo de Pagamento"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefone"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Número de telefone"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verificação de telefone"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Adicione uma venda"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Adicione um Cliente"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Por favor, verifique sua conectividade com a internet"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Faça o download do nosso aplicativo móvel e assine um pacote para usar a versão desktop"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Por favor, insira o estoque de produtos"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Insira dados válidos"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Selecione um cliente"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Insira dados válidos"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Painel de Cadastro do Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Práticas"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Suporte Premium ao Cliente"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Plano Premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Visualizar"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Devido anterior:"),
        "price": MessageLookupByLibrary.simpleMessage("Preço"),
        "print": MessageLookupByLibrary.simpleMessage("Imprimir"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Imprimir Nota Fiscal"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Imprimir PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Política de Privacidade"),
        "product": MessageLookupByLibrary.simpleMessage("Produto"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Categoria do produto"),
        "productCod":
            MessageLookupByLibrary.simpleMessage("Código do Produto*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Cor do produto"),
        "productList":
            MessageLookupByLibrary.simpleMessage("Lista de Produtos"),
        "productNam": MessageLookupByLibrary.simpleMessage("Nome do Produto*"),
        "productName": MessageLookupByLibrary.simpleMessage("Nome do produto"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Tamanho do produto"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Estoque de Produtos"),
        "productType": MessageLookupByLibrary.simpleMessage("Tipo de Produto"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Unidade do Produto"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garantia do Produto"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Peso do produto"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Capacidade do produto"),
        "prof": MessageLookupByLibrary.simpleMessage("Perfil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Editar Perfil"),
        "profit": MessageLookupByLibrary.simpleMessage("Lucro"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Lucro(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Lucro(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Compra"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Lista de compras"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Comprar Plano Premium"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Preço de Compra"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transação de Compra"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantidade"),
        "quotation": MessageLookupByLibrary.simpleMessage("Cotação"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Lista de cotações"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Vendas Recentes"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Valor Recebido"),
        "referenceNo":
            MessageLookupByLibrary.simpleMessage("Número de referência"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Número de referência"),
        "registration": MessageLookupByLibrary.simpleMessage("Registro"),
        "remaining": MessageLookupByLibrary.simpleMessage("Restante: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo restante"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Débito Restante"),
        "reports": MessageLookupByLibrary.simpleMessage("Relatórios"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Redefinir sua senha"),
        "retailer": MessageLookupByLibrary.simpleMessage("Revendedor"),
        "revenue": MessageLookupByLibrary.simpleMessage("Receita"),
        "right": MessageLookupByLibrary.simpleMessage("Direita"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Valor das Vendas"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Proteja os dados do seu negócio facilmente. Nossa Atualização Ilimitada do Pos Saas POS inclui backup de dados gratuito, garantindo que suas informações valiosas estejam protegidas contra eventos inesperados. Concentre-se no que realmente importa - o crescimento do seu negócio."),
        "sale": MessageLookupByLibrary.simpleMessage("Venda"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Valor de Venda"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Detalhes da venda"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista de Vendas"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Preço de Venda"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Preço de Venda*"),
        "saleReturn":
            MessageLookupByLibrary.simpleMessage("Devolução de Venda"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transação de Venda"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Histórico de Transações de Venda (Histórico de Vendas de Cotação)"),
        "sales": MessageLookupByLibrary.simpleMessage("Vendas"),
        "salesList": MessageLookupByLibrary.simpleMessage("Lista de Vendas"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Salvar e publicar"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Salvar e publicar"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Salvar Alterações"),
        "search": MessageLookupByLibrary.simpleMessage("Procurar......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Pesquisar qualquer coisa..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Pesquisar por fatura... "),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Pesquisar por fatura ou nome"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Pesquisar por Nome"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Pesquisar por nome ou telefone..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pesquisar Número de Série"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Selecionar Partes"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Selecionar Marca do Produto"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Selecionar Número de Série"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Selecionar Variações:"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage(
            "Selecionar Prazo de Garantia"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Selecione o seu idioma"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Enviar Mensagem"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Número de Série"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("Número de Série"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Taxa de Serviço"),
        "setting": MessageLookupByLibrary.simpleMessage("Configuração"),
        "share": MessageLookupByLibrary.simpleMessage("Compartilhar"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Envio/Outro"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nome da loja"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Saldo de abertura da loja"),
        "show": MessageLookupByLibrary.simpleMessage("Exibir >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Mostrar Logotipo na Fatura?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Envio ou Serviços"),
        "size": MessageLookupByLibrary.simpleMessage("Tamanho"),
        "statistic": MessageLookupByLibrary.simpleMessage("Estatística"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Mantenha-se na vanguarda dos avanços tecnológicos sem custos adicionais. Nossa Atualização Ilimitada do Pos Saas POS garante que você sempre tenha as ferramentas e recursos mais recentes ao seu alcance, garantindo que seu negócio permaneça atualizado."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Mantenha-se na vanguarda dos avanços tecnológicos sem custos adicionais. Nossa Atualização Ilimitada do POS Sass POS garante que você sempre tenha as ferramentas e recursos mais recentes ao seu alcance, garantindo que seu negócio permaneça atualizado."),
        "stock": MessageLookupByLibrary.simpleMessage("Estoque"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventário de estoque"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Relatório de estoque"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Valor do estoque"),
        "stockValues":
            MessageLookupByLibrary.simpleMessage("Valores em Estoque"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
        "subciption": MessageLookupByLibrary.simpleMessage("Assinatura"),
        "submit": MessageLookupByLibrary.simpleMessage("Enviar"),
        "supplier": MessageLookupByLibrary.simpleMessage("Fornecedores"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Débito do Fornecedor"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Fatura de Fornecedor"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lista de Fornecedores"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Código SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Vendas Totais"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Tire uma foto da sua carteira de motorista, cartão de identidade nacional ou passaporte"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Termos de Uso"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "O nome diz tudo. Com o Pos Saas POS Unlimited, não há limite para o uso. Esteja processando algumas transações ou enfrentando uma grande quantidade de clientes, você pode operar com confiança, sabendo que não está limitado por restrições."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Este cliente não tem dívidas"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Este cliente tem um débito anterior"),
        "to": MessageLookupByLibrary.simpleMessage("Para"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Produto mais vendido"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Total de valor"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Total de Desconto"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total a pagar"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Total de Débitos"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Despesa total"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Receita Total"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Total de itens: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Perda Total"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Total pago"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total a pagar"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Total de pagamento feito"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Preço Total"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Total de Produtos"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Lucro Total"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Total de Compras"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Total do Valor de Devolução"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Total de Devoluções"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total de Vendas"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Vendas totais"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total de Imposto"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Total de pagamento recebido"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transação"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("ID da Transação"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Relatório de Transações"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Tentar Novamente"),
        "type": MessageLookupByLibrary.simpleMessage("Tipo"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Não Pago"),
        "unit": MessageLookupByLibrary.simpleMessage("Unidade"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nome da Unidade"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Preço Unitário"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Ilimitado"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Faturas ilimitadas"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage("Uso Ilimitado"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Desbloqueie o potencial completo do Pos Saas POS com sessões de treinamento personalizadas lideradas por nossa equipe de especialistas. Desde o básico até técnicas avançadas, garantimos que você esteja bem preparado para utilizar todos os aspectos do sistema e otimizar seus processos de negócios."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Atualizar Agora"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Atualize seu plano primeiro\\nO limite de vendas foi excedido."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Atualize no aplicativo móvel"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Enviar uma imagem"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "Enviar um Logotipo de Fatura"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Carregar Documento"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Carregar Arquivo"),
        "userName": MessageLookupByLibrary.simpleMessage("Nome de Usuário"),
        "userRole": MessageLookupByLibrary.simpleMessage("Função de Usuário"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nome da Função de Usuário"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Título de Usuário"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage(
            "Imposto sobre Valor Agregado (IVA)"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Verificar número de telefone"),
        "view": MessageLookupByLibrary.simpleMessage("Visualizar"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Cliente de balcão"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantia"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garantias"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Precisamos registrar o seu telefone antes de começar!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Nós entendemos a importância das operações sem falhas. Por isso, nosso suporte 24 horas por dia está disponível para ajudá-lo, seja uma pergunta rápida ou uma preocupação abrangente. Conecte-se conosco a qualquer momento, em qualquer lugar, por telefone ou WhatsApp, para experimentar um atendimento ao cliente sem igual."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Preço de Atacado"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Atacado"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Atacado"),
        "wight": MessageLookupByLibrary.simpleMessage("Peso"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Sim, Devolver"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Você precisa FAZER LOGIN NOVAMENTE na sua conta."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Você precisa verificar sua identidade antes de comprar mensagens"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Sua lista de todas as vendas"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Todas as suas vendas"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Você está usando"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Suas vendas pendentes"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Você precisa verificar sua identidade antes de comprar mensagens"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Seu Pacote"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Seu pagamento foi cancelado"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Seu pagamento foi feito com sucesso"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Seu pagamento foi cancelado")
      };
}
