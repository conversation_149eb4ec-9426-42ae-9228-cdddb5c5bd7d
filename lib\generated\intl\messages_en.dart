// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("ADD SALE"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATEGORY"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("INVOICE"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS Sale"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRICE"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("PRODUCT NAME"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Login panel"),
        "QTY": MessageLookupByLibrary.simpleMessage("QTY"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Quantity*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("TOTAL VALUE"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("User Title"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("About App"),
        "accountName": MessageLookupByLibrary.simpleMessage("Account Name"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Account Number"),
        "action": MessageLookupByLibrary.simpleMessage("Action"),
        "add": MessageLookupByLibrary.simpleMessage("Add"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Add Brand"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Add Category"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Add Customer"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Add description...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Add Documents"),
        "addItem": MessageLookupByLibrary.simpleMessage("Add Item"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Add Item Category"),
        "addNew": MessageLookupByLibrary.simpleMessage("Add New"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("Add New User"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Add Product"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Added Successful"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Add Supplier"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Add Unit"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("Add/Update Expense List"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("Add/Update Income List"),
        "addUserRole": MessageLookupByLibrary.simpleMessage("Add User Role"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Adding Serial Number?"),
        "address": MessageLookupByLibrary.simpleMessage("Address"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("All Basic Features"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Already have an account\'s?"),
        "amount": MessageLookupByLibrary.simpleMessage("Amount"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Android & iOS App Support"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Are you want to create this Quotation?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Are you want to delete this Customer?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Are you want to delete this product"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Are you want to delete this Quotation?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Are you want to return this sale?"),
        "balance": MessageLookupByLibrary.simpleMessage("Balance"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Bank Account Currency"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bank Accounts"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bank Information"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bank Name"),
        "between": MessageLookupByLibrary.simpleMessage("Between"),
        "billTo": MessageLookupByLibrary.simpleMessage("Bill to:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Branch Name"),
        "brand": MessageLookupByLibrary.simpleMessage("Brand"),
        "brandName": MessageLookupByLibrary.simpleMessage("Brand Name"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Business Category"),
        "buy": MessageLookupByLibrary.simpleMessage("Buy"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Buy Premium Plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Buy sms"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calculator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacity"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Cash & Bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Cash In Hand"),
        "categories": MessageLookupByLibrary.simpleMessage("Categories"),
        "category": MessageLookupByLibrary.simpleMessage("Category"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Category Name"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Change Amount"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Changeable Amount"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("Check Warranty"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Chose a plan"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Collect Due >"),
        "color": MessageLookupByLibrary.simpleMessage("Color"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Company Name"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Company Address"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Company Description"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Company email address"),
        "companyName": MessageLookupByLibrary.simpleMessage("Company Name"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Company phone number"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Company Website Url"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirm password"),
        "continu": MessageLookupByLibrary.simpleMessage("Continue"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Convert To Sale"),
        "create": MessageLookupByLibrary.simpleMessage("Create"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Create Payment"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Created By"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Creative Hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Currency"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Current plan"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Custom Invoice Branding"),
        "customer": MessageLookupByLibrary.simpleMessage("Customer"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Customer Due"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Customer Invoices"),
        "customerList": MessageLookupByLibrary.simpleMessage("Customer List"),
        "customerName": MessageLookupByLibrary.simpleMessage("Customer Name"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Customer of the month"),
        "customerType": MessageLookupByLibrary.simpleMessage("Customer Type"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Customer: Walk-in Customer"),
        "customers": MessageLookupByLibrary.simpleMessage("Customers"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Daily Collection"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Daily Sales"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Daily Transaction"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("DashBoard"),
        "date": MessageLookupByLibrary.simpleMessage("Date"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Date Time"),
        "dealer": MessageLookupByLibrary.simpleMessage("Dealer"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Dealer Price"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Delivery Charge"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "details": MessageLookupByLibrary.simpleMessage("Details >"),
        "discount": MessageLookupByLibrary.simpleMessage("Discount"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Discount Price"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Download PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Due"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Due Amount"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Due amount will show here if available"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("Due Collection"),
        "dueList": MessageLookupByLibrary.simpleMessage("Due List"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Due Transaction"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Edit/Add Serial:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Edit your profile"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Enter Amount"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Enter Brand Name"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Enter Category Name"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Enter Company Description"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Enter company email address"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter company phone number"),
        "enterCompanyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Enter company website url"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Enter Customer Name"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Enter Dealer Price"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Enter Discount Price"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Enter Expense Category"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Enter Expense Date"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Enter Income Category"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Enter Income Date"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Enter Manufacturer Name"),
        "enterName": MessageLookupByLibrary.simpleMessage("Enter Name"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Enter Name"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Enter Note"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Enter Opening Balance"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Enter paid amounts"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Enter Password"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Enter Paying Amount"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Enter Price"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Enter Product Capacity"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Enter Product Code"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Enter Product Color"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Enter Product Name"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Enter Product Quantity"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Enter Product Size"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Enter Product Type"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Enter Product Unit"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Enter Product Weight"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Enter Purchase Price"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Enter Reference Number"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Enter Sale Price"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Enter Serial Number"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Enter message Content"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Enter Stock Amount"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Enter Transaction Id"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Enter Unit Name"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Enter User Role Name"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Enter user title"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Enter Warranty"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Enter Wholesale Price"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Enter your amount"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Enter Your Address"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Enter Your Company Address"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Enter Your Company Name"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Enter your Company Name"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Enter your email address"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Enter Your Password"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Enter your password again"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter your phone number"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Enter Your Shop Name"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Enter Category Name"),
        "expense": MessageLookupByLibrary.simpleMessage("Expense"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Expense Date"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Expense Details"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Expense For"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Expense CategoryList"),
        "expenses": MessageLookupByLibrary.simpleMessage("Expense"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top 5 purchasing product of the month"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("For Unlimited Uses"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Forgot Password?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Free Data Backup"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Free Lifetime Update"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Free Package"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Free Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Get Started"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Government Id"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Grand Total"),
        "hold": MessageLookupByLibrary.simpleMessage("Hold"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Hold Number"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Identity Verify"),
        "inc": MessageLookupByLibrary.simpleMessage("Income"),
        "income": MessageLookupByLibrary.simpleMessage("Income"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("income Category"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Income Category List"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Income Date"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("Income Details"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Income For"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Income List"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Increase Stock"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Instant Privacy"),
        "inventorySales":
            MessageLookupByLibrary.simpleMessage("Inventory Sales"),
        "invoice": MessageLookupByLibrary.simpleMessage("Invoice"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Invoice:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Invoice NO.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Invoice No."),
        "item": MessageLookupByLibrary.simpleMessage("Item"),
        "itemName": MessageLookupByLibrary.simpleMessage("ItemName"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC Verification"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Ledger Details"),
        "ledger": MessageLookupByLibrary.simpleMessage("Ledger"),
        "left": MessageLookupByLibrary.simpleMessage("Left"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Loan Accounts"),
        "logOut": MessageLookupByLibrary.simpleMessage("Log out"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logo position in invoice?"),
        "loss": MessageLookupByLibrary.simpleMessage("Loss"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Loss/Profit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Loss(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Low Stock"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Low Stocks"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Manufacturer"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Login Panel"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Signup Panel"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobile App\n+\nDesktop"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Money Receipt"),
        "nam": MessageLookupByLibrary.simpleMessage("Name*"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Name or Code or Category"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("New Customers"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("New Customers"),
        "newIncome": MessageLookupByLibrary.simpleMessage("New Income"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noConnection": MessageLookupByLibrary.simpleMessage("No Connection"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("No Customer Found"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("No Due Transaction Found"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("No Expense Category Found"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("No Income Category Found"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("No Income Found"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("No Invoice Found"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("No Product Found"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "No purchase transaction found"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("No Quotation Found"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("NO Report Found"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("No Sale Transaction Found"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("No Serial Number Found"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("No Supplier Found"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("No Transaction Found"),
        "noUserFound": MessageLookupByLibrary.simpleMessage("No User Found"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("No User Role Found"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("No Serial Number Found"),
        "note": MessageLookupByLibrary.simpleMessage("Note"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Open Cheques"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Opening Balance"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage(" or drag & drop PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Orders"),
        "other": MessageLookupByLibrary.simpleMessage("Other"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Other Income"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Package Feature"),
        "paid": MessageLookupByLibrary.simpleMessage("Paid"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Paid amount"),
        "partyName": MessageLookupByLibrary.simpleMessage("Party Name"),
        "partyType": MessageLookupByLibrary.simpleMessage("Party Type"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "payCash": MessageLookupByLibrary.simpleMessage("Pay Cash"),
        "payable": MessageLookupByLibrary.simpleMessage("Payable"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Paying Amount"),
        "payment": MessageLookupByLibrary.simpleMessage("Payment"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Payment In"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Payment Out"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Payment Type"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Payment Type"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Phone Verification"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Please Add A Sale"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Please Add Customer"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Please Check Your Internet Connectivity"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Please download our mobile app and subscribe to a package to use the desktop version"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Please enter product stock"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Please enter valid data"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Please Select A Customer"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Please enter valid data"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas SingUp Panel"),
        "practies": MessageLookupByLibrary.simpleMessage("Practise"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Customer Support"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Preview"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Previous Due:"),
        "price": MessageLookupByLibrary.simpleMessage("Price"),
        "print": MessageLookupByLibrary.simpleMessage("Print"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Print Invoice"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Print PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
        "product": MessageLookupByLibrary.simpleMessage("Product"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Product Category"),
        "productCod": MessageLookupByLibrary.simpleMessage("Product Code*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Product Color"),
        "productList": MessageLookupByLibrary.simpleMessage("Product List"),
        "productNam": MessageLookupByLibrary.simpleMessage("Product Name*"),
        "productName": MessageLookupByLibrary.simpleMessage("Product Name"),
        "productSize": MessageLookupByLibrary.simpleMessage("Product Size"),
        "productStock": MessageLookupByLibrary.simpleMessage("Product Stock"),
        "productType": MessageLookupByLibrary.simpleMessage("ProductType"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Product Unit"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Product Warranty"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Product weight"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Product Capacity"),
        "prof": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Profile Edit"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profit(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profit(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Purchase"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Purchase List"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Purchase Premium Plan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Purchase Price"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Purchase Transaction"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
        "quotation": MessageLookupByLibrary.simpleMessage("Quotation"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Quotation List"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Recent Sales"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Received Amount"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Reference No."),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Reference Number"),
        "registration": MessageLookupByLibrary.simpleMessage("Register"),
        "remaining": MessageLookupByLibrary.simpleMessage("Remaining: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Remaining Balance"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Remaining Due"),
        "reports": MessageLookupByLibrary.simpleMessage("Reports"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Reset Your Password"),
        "retailer": MessageLookupByLibrary.simpleMessage("Retailer"),
        "revenue": MessageLookupByLibrary.simpleMessage("Revenue"),
        "right": MessageLookupByLibrary.simpleMessage("Right"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Sale Amount"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Safeguard your business data effortlessly. Our Pos Saas POS Unlimited Upgrade includes free data backup, ensuring your valuable information is protected against any unforeseen events. Focus on what truly matters - your business growth."),
        "sale": MessageLookupByLibrary.simpleMessage("Sale"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Sale Amount"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Sale Details"),
        "saleList": MessageLookupByLibrary.simpleMessage("Sale List"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Sale Price"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Sale Price*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Sale Return"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Sale Transaction"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Sale Transactions (Quotation Sale History)"),
        "sales": MessageLookupByLibrary.simpleMessage("Sales"),
        "salesList": MessageLookupByLibrary.simpleMessage("Sales List"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Save & Publish"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Save & Publish"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
        "search": MessageLookupByLibrary.simpleMessage("Search......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Search Anythings..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Search by invoice...."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Search by invoice or name"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Search By Name"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("Search by Name or Phone..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Search Serial Number"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Select Parties"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Select Product Brand"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Select Serial Number"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Select Variations:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Select Warranty ATime"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Select your language"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Send message"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serial Number"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serial Number"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Service Charge"),
        "setting": MessageLookupByLibrary.simpleMessage("Setting"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Shipping/Other"),
        "shopName": MessageLookupByLibrary.simpleMessage("Shop Name"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Shop Opening Balance"),
        "show": MessageLookupByLibrary.simpleMessage("Show >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Show Logo in Invoice?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Shipping/Service"),
        "size": MessageLookupByLibrary.simpleMessage("Size"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistic"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Stay at the forefront of technological advancements without any extra costs. Our Pos Saas POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Stay at the forefront of technological advancements without any extra costs. Our Pos Sass POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge."),
        "stock": MessageLookupByLibrary.simpleMessage("Stock"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Stock Inventory"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Stock Report"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Stock Value"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Stock Value"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Sub Total"),
        "subciption": MessageLookupByLibrary.simpleMessage("Subscription"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "supplier": MessageLookupByLibrary.simpleMessage("Suppliers"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Supplier Due"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Supplier Invoice"),
        "supplierList": MessageLookupByLibrary.simpleMessage("Supplier List"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT Code"),
        "tSale": MessageLookupByLibrary.simpleMessage("Total Sales"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Take a driver\'s license, national identity card or passport photo"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Terms of use"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "The name says it all. With Pos Saas POS Unlimited, there\'s no cap on your usage. Whether you\'re processing a handful of transactions or experiencing a rush of customers, you can operate with confidence, knowing you\'re not constrained by limits"),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("This customer has no due"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "This customer have previous due"),
        "to": MessageLookupByLibrary.simpleMessage("To"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Top Selling Product"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Total Amount"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Total Discount"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total Due"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Total Dues"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Total Expense"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Total Income"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Total Item : 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Total Loss"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Total Paid"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total Payable"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Total Payment Out"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Total Product"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Total Profit"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Total Purchase"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Total Return Amount"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Total Returns"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total Sale"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Total Sales"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total Vat"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Total Payment In"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaction"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Transaction Id"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Transaction Report"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Try Again"),
        "type": MessageLookupByLibrary.simpleMessage("Type"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Unpaid"),
        "unit": MessageLookupByLibrary.simpleMessage("Unit"),
        "unitName": MessageLookupByLibrary.simpleMessage("Unit Name"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Unit Price"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Unlimited"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Unlimited Invoices"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Unlimited Usage"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Unlock the full potential of Pos Saas POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you\'re well-versed in utilizing every facet of the system to optimize your business processes."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Update Now"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Update your plan first\\nSale Limit is over."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Upgrade On Mobile App"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Upload an image"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Upload An Invoice Logo"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Upload Document"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Upload File"),
        "userName": MessageLookupByLibrary.simpleMessage("User Name"),
        "userRole": MessageLookupByLibrary.simpleMessage("User Role"),
        "userRoleName": MessageLookupByLibrary.simpleMessage("User Role Name"),
        "userTitle": MessageLookupByLibrary.simpleMessage("User Title"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("Vat/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verify Phone Number"),
        "view": MessageLookupByLibrary.simpleMessage("View"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Walk in Customer"),
        "warranty": MessageLookupByLibrary.simpleMessage("Warranty"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Warranty"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "We need to register your phone before getting started!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            " We understand the importance of seamless operations. That\'s why our round-the-clock support is available to assist you, whether it\'s a quick query or a comprehensive concern. Connect with us anytime, anywhere via call or WhatsApp to experience unrivaled customer service."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("WholeSale Price"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Wholesaler"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Wholesale"),
        "wight": MessageLookupByLibrary.simpleMessage("Weight"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Yes Return"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "You have to RE-LOGIN on your account."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "You need to identity verify before buying messages"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Your all sale list"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Your All Sales"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("You are using"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("Your Due Sales"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "You need to identity verify before buying messages"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Your Package"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Your Payment is canceled"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Your Payment is successfully"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Your Payment is canceled")
      };
}
