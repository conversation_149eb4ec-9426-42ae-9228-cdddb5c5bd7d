// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a hu locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'hu';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE":
            MessageLookupByLibrary.simpleMessage("ÉRTÉKESÍTÉS HOZZÁADÁSA"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGÓRIA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("Számla"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS-eladás"),
        "PRICE": MessageLookupByLibrary.simpleMessage("ÁR"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("TERMÉKNÉV"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Bejelentkezés panel"),
        "QTY": MessageLookupByLibrary.simpleMessage("MENNYISÉG"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Mennyiség*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("ÁLLAPOT"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("ÖSSZES ÉRTÉK"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Felhasználói cím"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Az alkalmazásról"),
        "accountName": MessageLookupByLibrary.simpleMessage("Számla neve"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Számlaszám"),
        "action": MessageLookupByLibrary.simpleMessage("Művelet"),
        "add": MessageLookupByLibrary.simpleMessage("Hozzáad"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Brand hozzáadása"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Kategória hozzáadása"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Vevő hozzáadása"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Leírás hozzáadása..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Dokumentum hozzáadása"),
        "addItem": MessageLookupByLibrary.simpleMessage("Elem hozzáadása"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Tételkategória hozzáadása"),
        "addNew": MessageLookupByLibrary.simpleMessage("Új hozzáadása"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Új felhasználó hozzáadása"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Termék hozzáadása"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Sikeresen hozzáadva"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Beszállító hozzáadása"),
        "addUnit":
            MessageLookupByLibrary.simpleMessage("Mértékegység hozzáadása"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Kiadási lista hozzáadása/módosítása"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Jövedelemlista hozzáadása/módosítása"),
        "addUserRole": MessageLookupByLibrary.simpleMessage(
            "Felhasználói szerep hozzáadása"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Sorozatszám hozzáadása?"),
        "address": MessageLookupByLibrary.simpleMessage("Cím"),
        "all": MessageLookupByLibrary.simpleMessage("Minden"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Minden alapvető funkció"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Már van fiókja?"),
        "amount": MessageLookupByLibrary.simpleMessage("Összeg"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android és iOS alkalmazás támogatás"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Szeretne létrehozni ezt a  quotation?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Biztosan törölni akarja ezt a vevőt?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Biztosan törölni szeretné ezt a terméket"),
        "areYouWantToDeleteThisQuotion":
            MessageLookupByLibrary.simpleMessage("Törli ezt az árajánlatot?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Szeretné visszaadni ezt az értékesítést?"),
        "balance": MessageLookupByLibrary.simpleMessage("Tartalék"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Bank számla valuta"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankszámlák"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bank információ"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bank neve"),
        "between": MessageLookupByLibrary.simpleMessage("Között"),
        "billTo":
            MessageLookupByLibrary.simpleMessage("Számla a következőnek:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Fiók neve"),
        "brand": MessageLookupByLibrary.simpleMessage("Márka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Brandnév"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Üzleti kategória"),
        "buy": MessageLookupByLibrary.simpleMessage("Vásárlás"),
        "buyPremiumPlan": MessageLookupByLibrary.simpleMessage(
            "Vásároljon prémium tervezetet"),
        "buySms": MessageLookupByLibrary.simpleMessage("Üzenetek vásárlása"),
        "calculator": MessageLookupByLibrary.simpleMessage("Számológép:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Mégse"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacitás"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Készpénz és bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Készpénz"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategóriák"),
        "category": MessageLookupByLibrary.simpleMessage("Kategória"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategória neve"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Különbözet"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Változható összeg"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Garancia ellenőrzése"),
        "choseAplan":
            MessageLookupByLibrary.simpleMessage("Válasszon egy tervet"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Nyitott tétel beszedése >"),
        "color": MessageLookupByLibrary.simpleMessage("Szín"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Cégnév"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Cég címe"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Cég leírása"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Cég e-mail címe"),
        "companyName": MessageLookupByLibrary.simpleMessage("Cégnév"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Cég telefonszáma"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Cég weboldal URL-je"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Jelszó megerősítése"),
        "continu": MessageLookupByLibrary.simpleMessage("Folytatni"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Átalakítás eladásra"),
        "create": MessageLookupByLibrary.simpleMessage("Létrehozni"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Fizetés létrehozása"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Létrehozta"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreatív központ"),
        "currency": MessageLookupByLibrary.simpleMessage("Pénznem"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Jelenlegi terv"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Egyedi számla branding"),
        "customer": MessageLookupByLibrary.simpleMessage("Ügyfél"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Vevői tartozás"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Vevői számlák"),
        "customerList": MessageLookupByLibrary.simpleMessage("Vevőlista"),
        "customerName": MessageLookupByLibrary.simpleMessage("Vevő neve"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("A hónap ügyfele"),
        "customerType": MessageLookupByLibrary.simpleMessage("Vevő típusa"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Ügyfél: Bejövő ügyfél"),
        "customers": MessageLookupByLibrary.simpleMessage("Ügyfelek"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Napi beszedések"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Napi eladások"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Napi tranzakció"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Vezérlőpult"),
        "date": MessageLookupByLibrary.simpleMessage("Dátum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Dátum és idő"),
        "dealer": MessageLookupByLibrary.simpleMessage("Díler"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Díjszabás"),
        "delete": MessageLookupByLibrary.simpleMessage("Törlés"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Szállítási költség"),
        "description": MessageLookupByLibrary.simpleMessage("Leírás"),
        "details": MessageLookupByLibrary.simpleMessage("Részletek>"),
        "discount": MessageLookupByLibrary.simpleMessage("Kedvezmény"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Kedvezményes ár"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF letöltése"),
        "due": MessageLookupByLibrary.simpleMessage("Teljesítési határidő"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Nyitott tétel"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "A fennmaradó összeg itt jelenik meg, ha elérhető"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Fennmaradó beszedés"),
        "dueList": MessageLookupByLibrary.simpleMessage("Fennmaradó listák"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Esedékes tranzakció"),
        "edit": MessageLookupByLibrary.simpleMessage("Szerkesztés"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Szerkesztés/hozzáadás sorszám:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Profil szerkesztése"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Összeg megadása"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Írja be a márka nevét"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Adja meg a kategória nevét"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Adja meg a cég leírását"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Adja meg a cég e-mail címét"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Adja meg a cég telefonszámát"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Adja meg a cég weboldal URL-jét"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Adja meg a vevő nevét"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Írja be a kereskedő árát"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Adja meg a kedvezményes árat"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Adja meg a kiadás kategóriát"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Adja meg a kiadás dátumát"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Adja meg a bevétel kategóriáját"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage(
            "Jövedelem dátumának megadása"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Írja be a gyártó nevét"),
        "enterName": MessageLookupByLibrary.simpleMessage("Adja meg a nevet"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Név megadása"),
        "enterNote":
            MessageLookupByLibrary.simpleMessage("Adja meg a megjegyzést"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Adja meg a nyitóegyenleget"),
        "enterPaidAmount": MessageLookupByLibrary.simpleMessage(
            "Adja meg a fizetendő összeget"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Adja meg a jelszót"),
        "enterPayingAmount": MessageLookupByLibrary.simpleMessage(
            "Adja meg a fizetendő összeget"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Írja be az árat"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Adja meg a termék kapacitását"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Írja be a termékkódot"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Adja meg a termék színét"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Írja be a termék nevét"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Írja be a termékmennyiséget"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Adja meg a termék méretét"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Írja be a termék típusát"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Adja meg a termék egységét"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Adja meg a termék súlyát"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Írja be a vételárat"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Adja meg az irányítószámot"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Adja meg az eladási árat"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Írja be a sorozatszámot"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Adja meg az üzenet tartalmát"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Adja meg a készletmennyiséget"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Adja meg a tranzakciós azonosítót"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage(
            "Írja be a mértékegység nevét"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Adja meg a felhasználói szerep nevét"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Adja meg a felhasználói címet"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Írja be a garanciát"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "Adja meg a nagykereskedelmi árat"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Adja meg az összeget"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Adja meg a címét"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Adja meg a cég címét"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Adja meg a cégnevét"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Adja meg a cégnevét"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Adja meg az e-mail címét"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Adja meg a jelszavát"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Adja meg újra a jelszavát"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Adja meg a telefonszámát"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Adja meg az üzlet nevét"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Adja meg a kategória nevét"),
        "expense": MessageLookupByLibrary.simpleMessage("Kiadás"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Kiadás dátuma"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Kiadási részletek"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Kiadás célja"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Kiadási kategóriák listája"),
        "expenses": MessageLookupByLibrary.simpleMessage("Kiadások"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "A hónap öt legtöbbet vásárolt terméke"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Korlátlan felhasználásra"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Elfelejtett jelszó?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Ingyenes adatmentés"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Ingyenes élettartam frissítés"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Ingyenes csomag"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Ingyenes tervezet"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Kezdés"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Kormányzati okmány"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Teljes összeg"),
        "hold": MessageLookupByLibrary.simpleMessage("Tart"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Tartszám"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Azonosítás ellenőrzése"),
        "inc": MessageLookupByLibrary.simpleMessage("Bevétel"),
        "income": MessageLookupByLibrary.simpleMessage("Bevétel"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Jövedelemkategória"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Jövedelemkategórialista"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Jövedelem dátuma"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Jövedelem-adatok"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Jövedelemért"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Jövedelemlista"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Készlet növelése"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Azonnali adatvédelem"),
        "invoice": MessageLookupByLibrary.simpleMessage("Számla"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Számla:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Számlaszám.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Számlaszám"),
        "item": MessageLookupByLibrary.simpleMessage("Tárgy"),
        "itemName": MessageLookupByLibrary.simpleMessage("Tárgynév"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC ellenőrzés"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Könyvviteli adatok"),
        "ledger": MessageLookupByLibrary.simpleMessage("Könyv"),
        "left": MessageLookupByLibrary.simpleMessage("Balra"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Hitelszámlák"),
        "logOut": MessageLookupByLibrary.simpleMessage("Kijelentkezés"),
        "login": MessageLookupByLibrary.simpleMessage("Bejelentkezés"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logó pozíciója a számlán?"),
        "loss": MessageLookupByLibrary.simpleMessage("Veszteség"),
        "lossOrProfit":
            MessageLookupByLibrary.simpleMessage("Veszteség/Profit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Veszteség (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Alacsony Készlet"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Alacsony készletek"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Hagyjon tartós benyomást ügyfelei számára egyedi logóval ellátott számlákkal. Az Korlátlan Frissítés egyedülálló előnyt kínál a számlák testreszabásával, ami professzionális érintést ad vállalkozásának identitásához, és erősíti az ügyfélhűséget."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Gyártó"),
        "mobiPosLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Bejelentkezési panel"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Regisztrációs panel"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Mobilalkalmazás\n+\nAsztali alkalmazás"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Pénzbevételi elismervény"),
        "nam": MessageLookupByLibrary.simpleMessage("Név*"),
        "name": MessageLookupByLibrary.simpleMessage("Név"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Név, kód vagy kategória"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Új ügyfelek"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Új ügyfelek"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Új jövedelem"),
        "no": MessageLookupByLibrary.simpleMessage("Nem"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Nincs kapcsolat"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nem található vevő"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("Nincs nyitott tétel"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("Nincs kiadás kategória"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nem található jövedelemkategória"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nem található jövedelem"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nem található számla"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nem található termék"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nincs vásárlási tranzakció"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nincs árajánlat"),
        "noReportFound": MessageLookupByLibrary.simpleMessage("Nincs jelentés"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nincs értékesítési tranzakció található"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Nincs sorozatszám található"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Nem található beszállító"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nincs tranzakció"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Nincs felhasználó található"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Nincs felhasználói szerep található"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Nincs sorozatszám található"),
        "note": MessageLookupByLibrary.simpleMessage("Megjegyzés"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Nyitott csekkek"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("Nyitóegyenleg"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "vagy húzza és tegye le a PNG, JPG fájlt"),
        "orders": MessageLookupByLibrary.simpleMessage("Rendelések"),
        "other": MessageLookupByLibrary.simpleMessage("Egyéb"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Egyéb bevétel"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Csomag jellemzők"),
        "paid": MessageLookupByLibrary.simpleMessage("Fizetett"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Fizetett összeg"),
        "partyName": MessageLookupByLibrary.simpleMessage("Személy neve"),
        "partyType": MessageLookupByLibrary.simpleMessage("Személy típusa"),
        "password": MessageLookupByLibrary.simpleMessage("Jelszó"),
        "payCash": MessageLookupByLibrary.simpleMessage("Készpénz fizetés"),
        "payable": MessageLookupByLibrary.simpleMessage("Fizetendő"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Fizetendő összeg"),
        "payment": MessageLookupByLibrary.simpleMessage("Fizetés"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Bejövő fizetés"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Kifizetés"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Fizetési mód"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Fizetési mód"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefonszám"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefonszám-ellenőrzés"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Adjon hozzá egy eladást"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Kérjük, adjon hozzá vevőt"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Kérjük, ellenőrizze internetkapcsolatát"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Töltse le mobilalkalmazásunkat és iratkozzon fel egy csomagra a asztali verzió használatához"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Kérjük, adja meg a termék készletét"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Kérjük, adja meg a valid adatokat"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Válasszon ki egy ügyfelet"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Kérjük, adja meg a valid adatokat"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas Regisztrációs panel"),
        "practies": MessageLookupByLibrary.simpleMessage("Gyakorlatok"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Prémium ügyféltámogatás"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Prémium tervezet"),
        "preview": MessageLookupByLibrary.simpleMessage("Előnézet"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Előző esedékesség:"),
        "price": MessageLookupByLibrary.simpleMessage("Ár"),
        "print": MessageLookupByLibrary.simpleMessage("Nyomtatás"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Számla nyomtatása"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF nyomtatása"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Adatvédelmi irányelvek"),
        "product": MessageLookupByLibrary.simpleMessage("Termék"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Termékkategória"),
        "productCod": MessageLookupByLibrary.simpleMessage("Termékkód*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Termék színe"),
        "productList": MessageLookupByLibrary.simpleMessage("Terméklista"),
        "productNam": MessageLookupByLibrary.simpleMessage("Terméknév*"),
        "productName": MessageLookupByLibrary.simpleMessage("Terméknév"),
        "productSize": MessageLookupByLibrary.simpleMessage("Termékméret"),
        "productStock": MessageLookupByLibrary.simpleMessage("Termék készlet"),
        "productType": MessageLookupByLibrary.simpleMessage("Terméktípus"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Termékegység"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Termékgarancia"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Termék súlya"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Termék kapacitása"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Profil szerkesztése"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profit(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profit(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Vásárlás"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Vásárlási lista"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Prémium tervezet vásárlása"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Vásárlási ár"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Vásárlási tranzakció"),
        "quantity": MessageLookupByLibrary.simpleMessage("Mennyiség"),
        "quotation": MessageLookupByLibrary.simpleMessage("Árajánlat"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Árajánlat lista"),
        "recentSale":
            MessageLookupByLibrary.simpleMessage("Legutóbbi Eladások"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Beérkezett összeg"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Irányítószám"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("Irányítószám"),
        "registration": MessageLookupByLibrary.simpleMessage("Regisztráció"),
        "remaining": MessageLookupByLibrary.simpleMessage("Maradék: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Maradványtartozás"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Maradék tartozás"),
        "reports": MessageLookupByLibrary.simpleMessage("Jelentések"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Jelszó visszaállítása"),
        "retailer": MessageLookupByLibrary.simpleMessage("Kiskereskedő"),
        "revenue": MessageLookupByLibrary.simpleMessage("Bevétel"),
        "right": MessageLookupByLibrary.simpleMessage("Jobbra"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Eladási Összeg"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Védelmezze vállalkozása adatait könnyedén. Az Pos Saas POS Korlátlan frissítés ingyenes adatmentést tartalmaz, amely biztosítja az értékes információk védelmét minden előre nem látható esemény ellen. Azokra összpontosíthat, ami igazán fontos - vállalkozása növekedésére."),
        "sale": MessageLookupByLibrary.simpleMessage("Eladás"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Eladási összeg"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Értékesítési részletek"),
        "saleList": MessageLookupByLibrary.simpleMessage("Értékesítési lista"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Eladási ár"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Eladási ár*"),
        "saleReturn":
            MessageLookupByLibrary.simpleMessage("Értékesítési visszatérítés"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Értékesítési tranzakció"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Értékesítési tranzakciók (árajánlat-értékesítési előzmények)"),
        "sales": MessageLookupByLibrary.simpleMessage("Értékesítések"),
        "salesList": MessageLookupByLibrary.simpleMessage("Értékesítési lista"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Mentés és közzététel"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Mentés és publikálás"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Változtatások mentése"),
        "search": MessageLookupByLibrary.simpleMessage("Keresés......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Keresés bármi..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Keresés számla alapján..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Keresés számla vagy név alapján"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Keresés név szerint"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Keresés név vagy telefon alapján..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Keresés sorozatszám"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Felek kiválasztása"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Válassza ki a termékmárkát"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Válassza ki a sorozatszámot"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Válassza ki a variációkat:"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage(
            "Válassza ki a jótállási időt"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Válassza ki a nyelvet"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Üzenet küldése"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Sorozatszám"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Sorozatszám"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Szolgáltatási díj"),
        "setting": MessageLookupByLibrary.simpleMessage("Beállítás"),
        "share": MessageLookupByLibrary.simpleMessage("Megosztás"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Szállítás/Egyéb"),
        "shopName": MessageLookupByLibrary.simpleMessage("Üzlet neve"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Üzletnyitó egyenleg"),
        "show": MessageLookupByLibrary.simpleMessage("Megtekintés>"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Logó megjelenítése a számlán?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Szállítás/Szolgáltatás"),
        "size": MessageLookupByLibrary.simpleMessage("Méret"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statisztika"),
        "status": MessageLookupByLibrary.simpleMessage("Állapot"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Maradjon a technológiai fejlesztések élén további költségek nélkül. Az Pos Saas POS Korlátlan Frissítés biztosítja, hogy mindig a legújabb eszközök és funkciók álljanak rendelkezésre, garantálva, hogy vállalkozása mindig élvonalban maradjon."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Maradjon a technológiai fejlesztések élén további költségek nélkül. Az Pos Sass POS Korlátlan Frissítés biztosítja, hogy mindig a legújabb eszközök és funkciók álljanak rendelkezésre, garantálva, hogy vállalkozása mindig élvonalban maradjon."),
        "stock": MessageLookupByLibrary.simpleMessage("Készlet"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("Raktárkészlet"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Készletjelentés"),
        "stockValue":
            MessageLookupByLibrary.simpleMessage("Raktárkészlet értéke"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Készlet Értékek"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Részösszeg"),
        "subciption": MessageLookupByLibrary.simpleMessage("Feliratkozás"),
        "submit": MessageLookupByLibrary.simpleMessage("Küldés"),
        "supplier": MessageLookupByLibrary.simpleMessage("Beszállítók"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Szállítói tartozás"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Beszállítói számla"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Beszállítói lista"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT kód"),
        "tSale": MessageLookupByLibrary.simpleMessage("Összes Eladás"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Vegyen egy vezetői engedélyt, személyi igazolványt vagy útlevelet"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Használati feltételek"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "A név mindent elárul. A Pos Saas POS Korlátlan változatában nincs korlát a használatban. Legyen szó néhány tranzakcióról vagy ügyfelek rohamáról, magabiztosan működhet, anélkül, hogy korlátok korlátoznák."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Ennek a vevőnek nincs fennmaradó összege"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Ennek a vevőnek van korábbi tartozása"),
        "to": MessageLookupByLibrary.simpleMessage("Címzett"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Legjobban eladott termék"),
        "total": MessageLookupByLibrary.simpleMessage("Összesen"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Összes összeg"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Összes kedvezmény"),
        "totalDue":
            MessageLookupByLibrary.simpleMessage("Összes nyitott tétel"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Összes tartozás"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Összes kiadás"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Összes jövedelem"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Teljes elem: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Összes veszteség"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Összes kifizetett"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Összes fizetendő"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Összes kifizetés"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Teljes ár"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Teljes termék"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Összes profit"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Összes vásárlás"),
        "totalReturnAmount": MessageLookupByLibrary.simpleMessage(
            "Összes visszatérítési összeg"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Összes visszatérítés"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Összes eladás"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Összes eladás"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Összes ÁFA"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Összes bejövő fizetés"),
        "transaction": MessageLookupByLibrary.simpleMessage("Tranzakció"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Tranzakciós azonosító"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Tranzakciós jelentés"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Próbálja újra"),
        "type": MessageLookupByLibrary.simpleMessage("Típus"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Fizetetlen"),
        "unit": MessageLookupByLibrary.simpleMessage("Egység"),
        "unitName": MessageLookupByLibrary.simpleMessage("Mértékegység neve"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Egységár"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Korlátlan"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Végtelen számla"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Korlátlan használat"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Hódítsa meg a Pos Saas POS teljes potenciálját személyre szabott tréningek segítségével, amelyeket szakértő csapatunk vezet. A kezdetektől az előrehaladott technikákig mindenre felkészítjük Önt, hogy minden rendszer részét hatékonyan használja vállalkozási folyamatainak optimalizálásához."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Frissítés most"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Frissítse a tervét először \\ nEladási határérték túllépve."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Frissítés a mobilalkalmazáson"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Kép feltöltése"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Számla logó feltöltése"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Dokumentum feltöltése"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Fájl feltöltése"),
        "userName": MessageLookupByLibrary.simpleMessage("Felhasználónév"),
        "userRole": MessageLookupByLibrary.simpleMessage("Felhasználó szerep"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Felhasználói szerep neve"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Felhasználói cím"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("ÁFA/ÁFA"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefonszám ellenőrzése"),
        "view": MessageLookupByLibrary.simpleMessage("Megtekintés"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Bejövő ügyfél"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garancia"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garancia"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Telefonszámát regisztrálni kell, mielőtt elkezdené!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Tudjuk, mennyire fontos a zökkenőmentes működés. Ezért non-stop támogatásunk rendelkezésre áll, hogy segítsen Önnek, legyen szó gyors kérdésről vagy átfogó aggodalomról. Kapcsolódjon velünk bármikor, bárhol hívás vagy WhatsApp útján, hogy egyedülálló ügyfélszolgálatot tapasztalhasson."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Végösszeg"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Nagykereskedő"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Nagykereskedelem"),
        "wight": MessageLookupByLibrary.simpleMessage("Súly"),
        "yesReturn":
            MessageLookupByLibrary.simpleMessage("Igen, visszatérítés"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Újra be kell jelentkeznie fiókjába."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Azonosítania kell magát, mielőtt üzeneteket vásárol"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Összes eladási listája"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Összes eladása"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Ön használja"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Önnek járó eladások"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Azonosítania kell magát, mielőtt üzeneteket vásárol"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Az Ön csomaga"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Fizetése lemondva"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Fizetése sikeres"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("A fizetése törölve lett")
      };
}
