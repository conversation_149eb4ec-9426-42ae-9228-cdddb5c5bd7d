// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a az locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'az';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("SATIŞ ƏLAVƏ ET"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("Kateqoriya"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS Satışı"),
        "PRICE": MessageLookupByLibrary.simpleMessage("Qiymət"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("Məhsul adı"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Giriş paneli"),
        "QTY": MessageLookupByLibrary.simpleMessage("Miqdar"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Miqdar*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("Status"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("Cəmi dəyər"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("İstifadəçi titri"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Tətbiq haqqında"),
        "accountName": MessageLookupByLibrary.simpleMessage("Hesabın adı"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Hesabın nömrəsi"),
        "action": MessageLookupByLibrary.simpleMessage("Əməliyyat"),
        "add": MessageLookupByLibrary.simpleMessage("Əlavə et"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Marka Əlavə Et"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Kateqoriya əlavə edin"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Müştəri əlavə et"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Təsvir əlavə edin...."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Sənədləri Əlavə Et"),
        "addItem": MessageLookupByLibrary.simpleMessage("Məhsul Əlavə Et"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage(
            "Məhsul Kateqoriyası Əlavə Et"),
        "addNew": MessageLookupByLibrary.simpleMessage("Yeni əlavə et"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Yeni istifadəçi əlavə et"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Məhsul Əlavə Et"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("Əlavə edildi"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Təchizatçı Əlavə Et"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Vahid Əlavə Et"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Xərclər siyahısını əlavə edin/Yeniləyin"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Gəlir Siyahısını Əlavə Et/Yenilə"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("İstifadəçi rolunu əlavə et"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Seriya nömrəsi əlavə edilir?"),
        "address": MessageLookupByLibrary.simpleMessage("Ünvan"),
        "all": MessageLookupByLibrary.simpleMessage("Hamısı"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Bütün əsas xüsusiyyətlər"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Artıq hesabınız var?"),
        "amount": MessageLookupByLibrary.simpleMessage("Məbləğ"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android & iOS tətbiq dəstəyi"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Bu Təklifi yaratmaq istəyirsiniz?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Bu müştəriyi silmək istəyirsiniz?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Bu məhsulu silmək istəyirsiniz?"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Bu təklifi silmək istəyirsinizmi?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Bu satışı qaytarmaq istəyirsiniz?"),
        "balance": MessageLookupByLibrary.simpleMessage("Balans"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Bankın hesabının valyutası"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bank Hesabları"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bank məlumatı"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bankın adı"),
        "between": MessageLookupByLibrary.simpleMessage("Arasında"),
        "billTo": MessageLookupByLibrary.simpleMessage("Əsas hesaba:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Şöbənin adı"),
        "brand": MessageLookupByLibrary.simpleMessage("Marka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Marka Adı"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Biznes Kateqoriyası"),
        "buy": MessageLookupByLibrary.simpleMessage("Alın"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium planını alın"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMS al"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulyator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Ləğv et"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapasitet"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Nağd və Bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Cibdə Nağd"),
        "categories": MessageLookupByLibrary.simpleMessage("Kateqoriyalar"),
        "category": MessageLookupByLibrary.simpleMessage("Kateqoriya"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kateqoriya adı"),
        "changeAmount":
            MessageLookupByLibrary.simpleMessage("Dəyişiklik məbləği"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Dəyişdirilə bilən məbləğ"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("Zəmanəti Yoxla"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Bir plan seçin"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Borc toplayın >"),
        "color": MessageLookupByLibrary.simpleMessage("Rəng"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Şirkət adı"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Şirkət Ünvanı"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Şirkət Təsviri"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Şirkət Elektron Poçt Ünvanı"),
        "companyName": MessageLookupByLibrary.simpleMessage("Şirkət adı"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Şirkət Telefon Nömrəsi"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Şirkət Veb Sayt URL-i"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Şifrəni təsdiqləyin"),
        "continu": MessageLookupByLibrary.simpleMessage("Davam et"),
        "convertToSale": MessageLookupByLibrary.simpleMessage("Satışa çevir"),
        "create": MessageLookupByLibrary.simpleMessage("Yarat"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Ödəniş yarat"),
        "createdBy":
            MessageLookupByLibrary.simpleMessage("Tərəfindən Yaradıldı"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Tərəfdaş Mərkəz"),
        "currency": MessageLookupByLibrary.simpleMessage("Valyuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Cari plan"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Xüsusi faktura brendinqi"),
        "customer": MessageLookupByLibrary.simpleMessage("Müştəri"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Müştəri Borcu"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Müştəri Faturaları"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Müştəri Siyahısı"),
        "customerName": MessageLookupByLibrary.simpleMessage("Müştəri adı"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Ayın Müştərisi"),
        "customerType": MessageLookupByLibrary.simpleMessage("Müştəri Növü"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Müştəri: Gələn Müştəri"),
        "customers": MessageLookupByLibrary.simpleMessage("Müştərilər"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Günlük toplama"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Günlük satış"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Günlük əməliyyatlar"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("İdarə paneli"),
        "date": MessageLookupByLibrary.simpleMessage("Tarix"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Tarix və Saat"),
        "dealer": MessageLookupByLibrary.simpleMessage("Bayi"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Bayi Qiyməti"),
        "delete": MessageLookupByLibrary.simpleMessage("Sil"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Çatdırılma Haqqı"),
        "description": MessageLookupByLibrary.simpleMessage("Təsvir"),
        "details": MessageLookupByLibrary.simpleMessage("Detallar >"),
        "discount": MessageLookupByLibrary.simpleMessage("Endirim"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Endirimli Qiymət"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF yüklə"),
        "due": MessageLookupByLibrary.simpleMessage("Qalıq"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Borc məbləği"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Əgər mövcuddirsə, borcunuz burada göstəriləcək"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("Borc Toplama"),
        "dueList": MessageLookupByLibrary.simpleMessage("Borc Siyahısı"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Qalıq əməliyyatı"),
        "edit": MessageLookupByLibrary.simpleMessage("Düzəlt"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Seriya nömrəsini düzəlt/əlavə et:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Profilinizi düzəldin"),
        "email": MessageLookupByLibrary.simpleMessage("E-poçt"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("Məbləği Daxil Edin"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Marka Adını Daxil Edin"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Kateqoriya adını daxil edin"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Şirkət təsvirini daxil edin"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Şirkət elektron poçt ünvanını daxil edin"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Şirkət telefon nömrəsini daxil edin"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Şirkət veb saytının URL adresini daxil edin"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Müştəri adını daxil edin"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Bayi Qiymətini Daxil Edin"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Endirimli qiyməti daxil edin"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Xərc kateqoriyasını daxil edin"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Xərclər tarixini daxil edin"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Gəlir kateqoriyasını daxil edin"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Gəlir Tarixini Daxil Edin"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("İstehsalçı Adını Daxil Edin"),
        "enterName": MessageLookupByLibrary.simpleMessage("Adı daxil edin"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Adları Daxil Edin"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Qeyd daxil edin"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Açılış balansını daxil edin"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Ödənilən məbləği daxil edin"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Şifrəni daxil edin"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Ödəniş məbləğini daxil edin"),
        "enterPrice":
            MessageLookupByLibrary.simpleMessage("Qiyməti Daxil Edin"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Məhsul həcmini daxil edin"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Məhsul Kodunu Daxil Edin"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Məhsul rəngini daxil edin"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Məhsul Adını Daxil Edin"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Məhsul Miqdarını Daxil Edin"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Məhsul ölçüsünü daxil edin"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Məhsul Növünü Daxil Edin"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Məhsul vahidini daxil edin"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Məhsul çəkisini daxil edin"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Alış Qiymətini Daxil Edin"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "İstiğlal nömrəsini daxil edin"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Satış qiymətini daxil edin"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriya Nömrəsini Daxil Edin"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Mesaj Məzmununu daxil edin"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Stok Məbləğini daxil edin"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Əməliyyatın İD-ni daxil edin"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Vahid Adını Daxil Edin"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "İstifadəçi rolunun adını daxil edin"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "İstifadəçi titrini daxil edin"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Zəmanəti Daxil Edin"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "Topdan satış qiymətini daxil edin"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Məbləği daxil edin"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Ünvanınızı daxil edin"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Şirkət ünvanınızı daxil edin"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Şirkət adını daxil edin"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Şirkət adınızı daxil edin"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "E-poçt ünvanınızı daxil edin"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Şifrənizi daxil edin"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Şifrənizi təkrar daxil edin"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Telefon nömrəsini daxil edin"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Mağaza adınızı daxil edin"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Kateqoriya adını daxil edin"),
        "expense": MessageLookupByLibrary.simpleMessage("Xərclər"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Xərclər tarixi"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Xərclər detalları"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Xərclər üçün"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Xərclər kateqoriya siyahısı"),
        "expenses": MessageLookupByLibrary.simpleMessage("Xərclər"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Ayın ən çox alınan beş məhsulu"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Məhdudlaşdırılmamış istifadə üçün"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Şifrəni unutmusunuz?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Pulsuz məlumat ehtiyat nüsxəsi"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Pulsuz ömürboyu yeniləmə"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Pulsuz paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Pulsuz plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Başlamaq"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Dövlət Təyinatı"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Ümumi cəm"),
        "hold": MessageLookupByLibrary.simpleMessage("Tutmaq"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Tutma Numarası"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Şəxsiyyətin Təsdiqi"),
        "inc": MessageLookupByLibrary.simpleMessage("Gəlir"),
        "income": MessageLookupByLibrary.simpleMessage("Gəlir"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Gəlir Kateqoriyası"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Gəlir Kateqoriya Siyahısı"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Gəlir Tarixi"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Gəlir Detalları"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Gəlir Üçün"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Gəlir Siyahısı"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Stoku artır"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage("Ani gizlilik"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Faktura:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Faktura №.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Faktura №"),
        "item": MessageLookupByLibrary.simpleMessage("Məhsul"),
        "itemName": MessageLookupByLibrary.simpleMessage("Məhsul Adı"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC Təsdiqi"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Hesab Detalları"),
        "ledger": MessageLookupByLibrary.simpleMessage("Hesabat"),
        "left": MessageLookupByLibrary.simpleMessage("Sol"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Kredit Hesabları"),
        "logOut": MessageLookupByLibrary.simpleMessage("Çıxış et"),
        "login": MessageLookupByLibrary.simpleMessage("Daxil ol"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Fakturada Loqo mövqeyi?"),
        "loss": MessageLookupByLibrary.simpleMessage("Ziyan"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Ziyan/Mənfəət"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Ziyan(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Az Stok"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Az stoklar"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Müştərilərinizə brendinq fakturaları ilə əbədi təsir qoyun. Bizim Məhdudlaşdırılmamış Yeniləmə fakturalarını xüsusiyyətləndirmənin unikal üstünlüyünü təklif edir, marka şəxsiyyətinizi təmin edən və müştəri loyallığını artıran profesionallıq nüansı əlavə edir."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("İstehsalçı"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Giriş Paneli"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Qeydiyyat Paneli"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobil tətbiq\n+\nMasaüstü"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Pul Qəbzi"),
        "nam": MessageLookupByLibrary.simpleMessage("Ad*"),
        "name": MessageLookupByLibrary.simpleMessage("Ad"),
        "nameCodeOrCateogry": MessageLookupByLibrary.simpleMessage(
            "Adı, Kodu və ya Kateqoriyası"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Yeni müştərilər"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Yeni müştərilər"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Yeni Gəlir"),
        "no": MessageLookupByLibrary.simpleMessage("Xeyr"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Əlaqə yoxdur"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Heç bir müştəri tapılmadı"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("Borc əməliyyatı tapılmadı"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("Xərc kateqoriyası tapılmadı"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Gəlir Kateqoriyası Tapılmadı"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Gəlir Tapılmadı"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Heç bir Faktura Tapılmadı"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Məhsul tapılmadı"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("Alış əməliyyatı tapılmadı"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Təklif tapılmadı"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Hesabat tapılmadı"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Heç bir Satış Əməliyyatı Tapılmadı"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Heç bir Seriya Nömrəsi Tapılmadı"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "Heç bir Təchizatçı Tapılmadı"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Heç bir Əməliyyat Tapılmadı"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("İstifadəçi tapılmadı"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("İstifadəçi rol tapılmadı"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Heç bir Seriya Nömrəsi Tapılmadı"),
        "note": MessageLookupByLibrary.simpleMessage("Qeyd"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Açıq Çeklər"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Açılış balansı"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " və ya PNG, JPG şəkilini buraxın"),
        "orders": MessageLookupByLibrary.simpleMessage("Sifarişlər"),
        "other": MessageLookupByLibrary.simpleMessage("Digər"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Digər gəlir"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Paket xüsusiyyəti"),
        "paid": MessageLookupByLibrary.simpleMessage("Ödənib"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Ödənilmiş Məbləğ"),
        "partyName": MessageLookupByLibrary.simpleMessage("Tərəf adı"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tərəf növü"),
        "password": MessageLookupByLibrary.simpleMessage("Şifrə"),
        "payCash": MessageLookupByLibrary.simpleMessage("Nəğd ödəmə"),
        "payable": MessageLookupByLibrary.simpleMessage("Ödəniləcək"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Ödənilən məbləğ"),
        "payment": MessageLookupByLibrary.simpleMessage("Ödəniş"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Gəlir ödənişi"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Gəlir çıxışı"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Ödəniş növü"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Ödəniş Növləri"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefon nömrəsi"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefon Təsdiqi"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Zəhmət olmasa, bir satış əlavə edin"),
        "pleaseAddCustomer": MessageLookupByLibrary.simpleMessage(
            "Zəhmət olmasa müştəri əlavə edin"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Xahiş edirik İnternet əlaqənizi yoxlayın"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Zəhmət olmasa mobil tətbiqi yükləyin və istifadəçi panelini istifadə etmək üçün bir paketə abunə olun"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Xahiş edirik məhsul stokunu daxil edin"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Xahiş edirəm düzgün məlumat daxil edin"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Zəhmət olmasa, bir müştəri seçin"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Xahiş edirəm düzgün məlumat daxil edin"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Qeydiyyat paneli"),
        "practies": MessageLookupByLibrary.simpleMessage("Praktika"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium müştəri dəstəyi"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Önizləmə"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Əvvəlki borc:"),
        "price": MessageLookupByLibrary.simpleMessage("Qiymət"),
        "print": MessageLookupByLibrary.simpleMessage("Çap et"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Fakturanı Çap Et"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF Çap Et"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Gizlilik siyasəti"),
        "product": MessageLookupByLibrary.simpleMessage("Məhsul"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Məhsul kateqoriyası"),
        "productCod": MessageLookupByLibrary.simpleMessage("Məhsul Kodu*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Məhsul rəngi"),
        "productList": MessageLookupByLibrary.simpleMessage("Məhsul Siyahısı"),
        "productNam": MessageLookupByLibrary.simpleMessage("Məhsul Adı*"),
        "productName": MessageLookupByLibrary.simpleMessage("Məhsul adı"),
        "productSize": MessageLookupByLibrary.simpleMessage("Məhsul ölçüsü"),
        "productStock": MessageLookupByLibrary.simpleMessage("Məhsulun stoku"),
        "productType": MessageLookupByLibrary.simpleMessage("Məhsul Növü"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Məhsul Vahidi"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Məhsul Zəmanəti"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Məhsul çəkisi"),
        "productcapacity": MessageLookupByLibrary.simpleMessage("Məhsul həcmi"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Profil düzəliş"),
        "profit": MessageLookupByLibrary.simpleMessage("Mənfəət"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Mənfəət(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Mənfəət(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Alış"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Alış siyahısı"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium planını satın alın"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Alış Qiyməti"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Satınalma Əməliyyatı"),
        "quantity": MessageLookupByLibrary.simpleMessage("Miqdar"),
        "quotation": MessageLookupByLibrary.simpleMessage("Təklif"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Təklif siyahısı"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Son Satışlar"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Alınan Məbləğ"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("İstiğlal nömrəsi"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("İstiğlal nömrəsi"),
        "registration": MessageLookupByLibrary.simpleMessage("Qeydiyyat"),
        "remaining": MessageLookupByLibrary.simpleMessage("Qalmış: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Qalıq balans"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Qalıq Borc"),
        "reports": MessageLookupByLibrary.simpleMessage("Hesabatlar"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Şifrəni yeniləyin"),
        "retailer": MessageLookupByLibrary.simpleMessage("Pərakəndə Satış"),
        "revenue": MessageLookupByLibrary.simpleMessage("Gəlir"),
        "right": MessageLookupByLibrary.simpleMessage("Sağ"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Satış Miqdarı"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Ən çox təhlükəsiz şəkildə iş məlumatlarınızı qoruyun. Bizim Pos Saas POS Unlimited Yeniləməsi pulsuz məlumat ehtiyat nüsxəsini içərisinə alır, qiymətli məlumatınızın qeyri gözlənən hadisələrə qarşı qorunduğundan əmin olur. Həqiqətən əhəmiyyətli olan şeyə mərkəzləşin - işinizi böyütmək."),
        "sale": MessageLookupByLibrary.simpleMessage("Satış"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Satış Məbləği"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Satış Detalları"),
        "saleList": MessageLookupByLibrary.simpleMessage("Satış Siyahısı"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Satış Qiyməti"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Satış Qiyməti*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Satış Qaytarışı"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Satış Əməliyyatı"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Satış Əməliyyatları (Təklif Satış Tarixi)"),
        "sales": MessageLookupByLibrary.simpleMessage("Satışlar"),
        "salesList": MessageLookupByLibrary.simpleMessage("Satış siyahısı"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Saxla & Yayımla"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Yadda saxla & Dərc et"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Dəyişiklikləri Yadda Saxla"),
        "search": MessageLookupByLibrary.simpleMessage("Axtar..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Hər hansı bir şey axtar"),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Faktura ilə axtarış...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Faktura və ya ad ilə axtarış"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Adla Axtar"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("Ad və ya telefonla axtar..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriya Nömrəsini Axtar"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Tərəfləri Seçin"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Məhsul Markasını Seçin"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seriya Nömrəsini Seçin"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Variasiyaları Seçin:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Zəmanət Müddətini Seçin"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Dilinizi seçin"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Mesaj Göndər"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Seriya Nömrəsi"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Seriya Nömrəsi"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Xidmət Haqqı"),
        "setting": MessageLookupByLibrary.simpleMessage("Quraşdırmalar"),
        "share": MessageLookupByLibrary.simpleMessage("Paylaş"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Çatdırılma/Digər"),
        "shopName": MessageLookupByLibrary.simpleMessage("Mağaza adı"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Mağaza Açılış Balansı"),
        "show": MessageLookupByLibrary.simpleMessage("Göstər >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Fakturada Loqo Göstərilsin?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Göndərmə/Xidmət"),
        "size": MessageLookupByLibrary.simpleMessage("Ölçü"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistika"),
        "status": MessageLookupByLibrary.simpleMessage("Vəziyyət"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Əlavə xərclər olmadan texnologiya nailiyyətlərinin ən qabaqda qalın. Bizim Pos Saas POS Unlimited Yeniləmə sizin daim ən son alətlər və xüsusiyyətlərin əlinizdə olmasını təmin edir və biznesinizin ən son dərəcədə məşhur olmasını təmin edir."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Əlavə xərclər olmadan texnologiya nailiyyətlərinin ən qabaqda qalın. Bizim Pos Sass POS Unlimited Yeniləmə sizin daim ən son alətlər və xüsusiyyətlərin əlinizdə olmasını təmin edir və biznesinizin ən son dərəcədə məşhur olmasını təmin edir."),
        "stock": MessageLookupByLibrary.simpleMessage("Stok"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("Stok Inventar"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Stok hesabatı"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Stok dəyəri"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Stok Dəyərləri"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Cəmi Altqiymət"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abunəlik"),
        "submit": MessageLookupByLibrary.simpleMessage("Göndər"),
        "supplier": MessageLookupByLibrary.simpleMessage("Təchizatçılar"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Təchizatçı Borcu"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Təchizatçı Faturası"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Təchizatçı Siyahısı"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT kodu"),
        "tSale": MessageLookupByLibrary.simpleMessage("Ümumi Satış"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Sürücü vəsiqəsi, milli təyinat və ya pasportun şəklini göndərin"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("İstifadə şərtləri"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Ad hər şeyi deyir. Pos Saas POS Unlimited ilə istifadənizdə heç bir məhdudiyyət yoxdur. Bir neçə əməliyyatı emal edirsinizsə və ya müştərilərin dəhlizində yaşamaqda olarsınızsa, heç bir məhdudiyyətlə məhdudiyyətli olmadığınızı bilərək özünüzlə əməliyyat edə bilərsiniz."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("Bu müştərinin borcu yoxdur"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Bu müştərinin əvvəlki qalığı var"),
        "to": MessageLookupByLibrary.simpleMessage("ərzində"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Ən Çox Satılan Məhsul"),
        "total": MessageLookupByLibrary.simpleMessage("cəmi"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Cəmi məbləğ"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Ümumi Endirim"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Ümumi borc"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Ümumi Borclar"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Ümumi xərclər"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Ümumi Gəlir"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Ümumi Məhsul : 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Ümumi Ziyan"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Cəmi ödənilib"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Cəmi ödəniləcək"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Cəmi gəlir çıxışı"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Ümumi Qiymət"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Ümumi Məhsullar"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Ümumi Mənfəət"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Ümumi Alış"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Cəmi Qaytarış Məbləği"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Cəmi Qaytarışlar"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Ümumi Satış"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Ümumi satışlar"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Ümumi KDV"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Cəmi gəlir ödənişi"),
        "transaction": MessageLookupByLibrary.simpleMessage("Əməliyyat"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Əməliyyat İDsi"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Əməliyyat Hesabatı"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Yenidən cəhd edin"),
        "type": MessageLookupByLibrary.simpleMessage("Növ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Ödənilməmiş"),
        "unit": MessageLookupByLibrary.simpleMessage("Vahid"),
        "unitName": MessageLookupByLibrary.simpleMessage("Vahid Adı"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Birim Qiymət"),
        "unlimited":
            MessageLookupByLibrary.simpleMessage("Məhdudlaşdırılmamış"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Limitsiz fakturalar"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage(
            "Məhdudlaşdırılmamış istifadə"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Pos Saas POS-un bütün potensialını kilidləyin, ekspert komandamız tərəfindən rəhbərlik edilən şəxsi təlim sessiyaları ilə. Əsaslarından başlayaraq, çoxaqıt təsərrüfatlara qədər, sistem hər tərəfini istifadə etmək üçün ixtisaslanmış olduğunuzdan əminik, biznes proseslərinizi optimal hala gətirərik."),
        "updateNow": MessageLookupByLibrary.simpleMessage("İndi yeniləyin"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Əvvəlcə planınızı yeniləyin\\nSatış Limiti qalib."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Mobil Tətbiqetmə yüksəldin"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Şəkil yüklə"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Faktura Loqosu Yüklə"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Sənədi yükləyin"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Faylı yükləyin"),
        "userName": MessageLookupByLibrary.simpleMessage("İstifadəçi adı"),
        "userRole": MessageLookupByLibrary.simpleMessage("İstifadəçi rolu"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("İstifadəçi rolunun adı"),
        "userTitle": MessageLookupByLibrary.simpleMessage("İstifadəçi titri"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("KDV/GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Telefon nömrəsini təsdiqləyin"),
        "view": MessageLookupByLibrary.simpleMessage("Baxış"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Gələn Müştəri"),
        "warranty": MessageLookupByLibrary.simpleMessage("Zəmanət"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Zəmanət"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Başlamaq üçün telefonunuzu qeydiyyatdan keçirməliyik!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Biz düşəri işlərin əhəmiyyətini başa düşürük. Bu səbəbdən 24 saat dəstəyimiz sürətli sorğu və geniş təfərrüatlı nailiyyət olsun, sizi kömək etməyə hazırdır. Bizimlə çağrı və ya WhatsApp vasitəsilə hər zaman, hər yerə əlaqə qurun və qıyaslanmaz müştəri xidmətinin keyfini çıxarın."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Topdan Satış Qiyməti"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Toptancı"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Topdan satış"),
        "wight": MessageLookupByLibrary.simpleMessage("Çəki"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Bəli, Qaytar"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Hesabınıza YENİDƏ GİRİŞ etməlisiniz."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Mesajlar almaq üçün öncə şəxsiyyətinizi təsdiq etməlisiniz"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Bütün satış siyahınız"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Bütün Satışlarınız"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Siz istifadə edirsiniz"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Ödənilməli Satışlarınız"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Mesajlar almaq üçün öncə şəxsiyyətinizi təsdiq etməlisiniz"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Sizin paketiniz"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Ödənişiniz ləğv edildi"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Ödənişiniz uğurla həyata keçirildi"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Ödənişiniz ləğv edildi")
      };
}
