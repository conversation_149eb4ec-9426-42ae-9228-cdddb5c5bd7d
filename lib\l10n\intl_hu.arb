{"addNewUser": "<PERSON>j f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userRole": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "addNew": "<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON>", "revenue": "Bevétel", "userName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noUserFound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "all": "Minden", "profileEdit": "<PERSON><PERSON>", "practies": "Gyakorlatok", "salesList": "Értékesítési lista", "enterPassword": "Adja meg a j<PERSON>zót", "noUserRoleFound": "<PERSON><PERSON><PERSON><PERSON> szerep <PERSON>", "addUserRole": "Felhasználói szerep hozzáadása", "UserTitle": "Felhasználói cím", "enterUserTitle": "Adja meg a felhasználói címet", "userTitle": "Felhasználói cím", "addSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "youHaveToRelogin": "Újra be kell jelentkeznie fiókjába.", "ok": "Ok", "payCash": "Készpénz fizetés", "freeLifeTimeUpdate": "Ingyenes élettartam fris<PERSON>í<PERSON>", "androidIOSAppSupport": "Android és iOS alkalmazás támogatás", "premiumCustomerSupport": "Prémium ügyféltámogatás", "customInvoiceBranding": "Egyedi számla branding", "unlimitedUsage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freeDataBackup": "Ingyenes adatmentés", "stayAtTheForFront": "Maradjon a technológiai fejlesztések élén további költségek nélkül. Az Pos Saas POS Korlátlan Frissítés biztosítja, hogy mindig a legújabb eszközök és funkciók álljanak rendelkezésre, g<PERSON><PERSON><PERSON><PERSON>va, hogy vállalkozása mindig élvonalban maradjon.", "weUnderStand": "<PERSON><PERSON><PERSON><PERSON>, mennyire fontos a zökkenőmentes működés. Ezért non-stop támogatásunk rendelkez<PERSON><PERSON>, hogy seg<PERSON><PERSON><PERSON>, legyen szó gyors kérdésről vagy átfogó aggodalomról. Kapcsolódjon velünk b<PERSON>r, bárhol hívás vagy <PERSON>s<PERSON>pp útján, hogy egyedülálló ügyfélszolgálatot tapasz<PERSON>has<PERSON>.", "unlockTheFull": "Hódítsa meg a Pos Saas POS teljes potenciálját személyre szabott tréningek segítségével, amelyeket szakértő csapatunk vezet. A kezdetektől az előrehaladott technikákig mindenre felkészítjü<PERSON>, hogy minden rendszer részét hatékonyan használja vállalkozási folyamatainak optimalizálásához.", "makeALastingImpression": "Hagyjon tartós benyomást ügyfelei számára egyedi logóval ellátott számlákkal. Az Korlátlan Frissítés egyedülálló előnyt kínál a számlák testresza<PERSON>, ami professzionális érintést ad vállalkozásának identitásához, és erősíti az ügyfélhűséget.", "theNameSysIt": "A név mindent elárul. A Pos Saas POS Korlátlan változatában nincs korlát a használatban. Legyen szó néhány tranzakcióról vagy ügyfelek rohamáról, ma<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy korlátok korlátoznák.", "safegurardYourBusinessDate": "Védelmezze vállalkozása adatait könnyedén. Az Pos Saas POS Korlátlan frissítés ingyenes adatmentést tartalmaz, amely biztosítja az értékes információk védelmét minden előre nem látható esemény ellen. Azokra összpontosíthat, ami igazán font<PERSON> - vállalkozása növekedésére.", "buy": "Vásárlás", "bankInformation": "Bank információ", "bankName": "Bank neve", "branchName": "Fiók neve", "accountName": "Számla neve", "accountNumber": "Számlaszám", "bankAccountingCurrecny": "Bank számla valuta", "swiftCode": "SWIFT kód", "enterTransactionId": "Adja meg a tranzakciós azonosítót", "uploadDocument": "Dokumentum feltöltése", "uploadFile": "<PERSON><PERSON><PERSON><PERSON>", "aboutApp": "Az alkalmazásról", "termsOfUse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privacyPolicy": "Adatvédelmi <PERSON>", "userRoleName": "Felhasználói szerep neve", "enterUserRoleName": "Adja meg a felhasználói szerep nevét", "yourPackage": "<PERSON>z <PERSON><PERSON>", "freePlan": "<PERSON>gy<PERSON><PERSON> te<PERSON>t", "yourAreUsing": "<PERSON><PERSON>", "freePackage": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "premiumPlan": "Prémium tervezet", "packageFeature": "Csomag j<PERSON>k", "remaining": "Maradék: ", "unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forUnlimitedUses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateNow": "<PERSON><PERSON><PERSON><PERSON><PERSON> most", "purchasePremiumPlan": "Prémium tervezet vásárlása", "stayAtTheForeFrontOfTechnological": "Maradjon a technológiai fejlesztések élén további költségek nélkül. Az Pos Sass POS Korlátlan Frissítés biztosítja, hogy mindig a legújabb eszközök és funkciók álljanak rendelkezésre, g<PERSON><PERSON><PERSON><PERSON>va, hogy vállalkozása mindig élvonalban maradjon.", "buyPremiumPlan": "Vásároljon prémium tervezetet", "mobilePlusDesktop": "Mobilalkalmazás\n+\nAsztali alkalmazás", "transactionId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productStock": "Termék <PERSON>", "pleaseEnterProductStock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a <PERSON><PERSON>k k<PERSON>", "increaseStock": "Készlet növelése", "areYouWantToDeleteThisProduct": "Biztosan törölni szeretné ezt a terméket", "noConnection": "<PERSON><PERSON><PERSON>", "pleaseCheckYourInternetConnectivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON>ő<PERSON>ze internetkapcsolatát", "tryAgain": "Prób<PERSON><PERSON><PERSON>", "currency": "Pénznem", "PosSaasLoginPanel": "Pos Saas Bejelentkezés panel", "posSaasSingUpPanel": "Pos Saas Regisztrá<PERSON>ós panel", "businessCategory": "<PERSON><PERSON><PERSON> kateg<PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterYourCompanyName": "Adja meg a cégnev<PERSON>t", "phoneNumber": "Telefonszám", "enterYourPhoneNumber": "Adja meg a telefonszámát", "shopOpeningBalance": "Üzletnyitó egyenleg", "enterYOurAmount": "Adja meg az összeget", "continu": "<PERSON><PERSON><PERSON><PERSON>", "resetYourPassword": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "email": "E-mail", "enterYourEmailAddress": "Adja meg az e-mail címét", "pleaseDownloadOurMobileApp": "Töltse le mobilalkalmazásunkat és iratkozzon fel egy csomagra a asztali verzió <PERSON>", "mobiPosLoginPanel": "Pos Saas Bejelentkezési panel", "enterYourPassword": "Adja meg a j<PERSON>v<PERSON>", "login": "Bejelentkezés", "password": "Je<PERSON><PERSON><PERSON>", "forgotPassword": "Elfelejtett jelszó?", "registration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editYourProfile": "<PERSON><PERSON>", "uploadAImage": "<PERSON><PERSON><PERSON>", "orDragAndDropPng": "vag<PERSON> h<PERSON> tegye le a PNG, JPG fájlt", "comapnyName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterYourCompanyNames": "Adja meg a cégnev<PERSON>t", "address": "Cím", "enterYourAddress": "Adja meg a címét", "mobiPosSignUpPane": "Pos Saas Regisztrá<PERSON>ós panel", "confirmPassword": "<PERSON><PERSON><PERSON>ó megerősítése", "enterYourPasswordAgain": "Adja meg új<PERSON> a j<PERSON>zavát", "alreadyHaveAnAccounts": "<PERSON><PERSON><PERSON>?", "choseAplan": "V<PERSON><PERSON>zon egy tervet", "allBasicFeatures": "Minden alapvető <PERSON>", "unlimitedInvoice": "<PERSON>égtelen <PERSON>", "getStarted": "Kezdés", "currentPlan": "Jelenlegi terv", "selectYourLanguage": "Válassza ki a nyelvet", "shopName": "<PERSON><PERSON>t neve", "enterYourShopName": "Adja meg az üzlet nevét", "phoneVerification": "Telefonszám-ellenőrzés", "weNeedToRegisterYourPhone": "Telefonszámát regisztrálni kell, miel<PERSON>tt elkezdené!", "verifyPhoneNumber": "Telefonszám ellenőrzése", "customerName": "<PERSON><PERSON><PERSON>ve", "enterCustomerName": "Adja meg a vevő nevét", "openingBalance": "Nyitóegyenleg", "enterOpeningBalance": "Adja meg a nyitóegyenleget", "type": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "saveAndPublish": "Mentés és közzététel", "customerList": "Vevőlista", "searchByNameOrPhone": "Keresés név vagy telefon alapján...", "addCustomer": "<PERSON><PERSON><PERSON>", "partyName": "<PERSON><PERSON><PERSON><PERSON> neve", "partyType": "<PERSON><PERSON><PERSON><PERSON>", "phone": "Telefon", "due": "Teljesítési hatá<PERSON>ő", "edit": "Szerkesztés", "delete": "Törlés", "areYouWantToDeleteThisCustomer": "Biztosan törölni akarja ezt a vevőt?", "thisCustomerHavepreviousDue": "Ennek a vevőnek van korábbi tartozása", "noCustomerFound": "<PERSON><PERSON> vevő", "totalDue": "Összes nyitott tétel", "customers": "Ügyfelek", "supplier": "Be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectDue": "Nyitott tétel beszedése >", "noDueTransantionFound": "<PERSON><PERSON><PERSON> n<PERSON> t<PERSON>", "createPayment": "Fizetés létrehozása", "grandTotal": "Teljes összeg", "payingAmount": "Fizetendő összeg", "enterPaidAmount": "Adja meg a fizetendő összeget", "changeAmount": "Különbözet", "dueAmount": "Nyitott tétel", "paymentType": "<PERSON><PERSON><PERSON><PERSON> mód", "submit": "<PERSON><PERSON><PERSON><PERSON>", "enterExpanseCategory": "Adja meg a kiadás kategóriát", "pleaseEnterValidData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a valid adatokat", "categoryName": "Kategória neve", "entercategoryName": "Adja meg a kategória nevét", "description": "Le<PERSON><PERSON><PERSON>", "addDescription": "Le<PERSON><PERSON><PERSON> ho<PERSON>...", "expensecategoryList": "Kiadási kategóriák <PERSON>á<PERSON>", "searchByInvoice": "Keresés számla alapján...", "addCategory": "Kategória ho<PERSON>adása", "action": "Művelet", "noExpenseCategoryFound": "<PERSON><PERSON><PERSON> kia<PERSON> ka<PERSON>gó<PERSON>", "expenseDetails": "Kiadási részletek", "date": "<PERSON><PERSON><PERSON>", "name": "Név", "category": "Kategória", "referenceNo": "Irányí<PERSON>", "amount": "Összeg", "note": "Megjegyzés", "nam": "Név*", "income": "Bevétel", "addUpdateExpenseList": "Kiadási lista hozzáadása/módosítása", "expenseDate": "<PERSON><PERSON><PERSON>", "enterExpenseDate": "Adja meg a kiadás d<PERSON>", "expenseFor": "<PERSON><PERSON><PERSON>", "enterName": "Adja meg a nevet", "referenceNumber": "Irányí<PERSON>", "enterReferenceNumber": "Adja meg az irányítószámot", "enterNote": "Adja meg a megjegyzést", "between": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to": "Címzett", "totalExpense": "Összes kiadás", "totalSales": "Összes eladás", "purchase": "Vásárlás", "newCustomers": "<PERSON><PERSON>", "dailySales": "<PERSON><PERSON>", "dailyCollection": "<PERSON><PERSON>", "instantPrivacy": "Azonnali adatvédelem", "stockInventory": "Raktárk<PERSON><PERSON><PERSON>", "stockValue": "Raktárkészlet értéke", "lowStocks": "Alacsony készletek", "other": "<PERSON><PERSON><PERSON><PERSON>", "otherIncome": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "MOBIPOS": "<PERSON><PERSON>", "newCusotmers": "<PERSON><PERSON>", "enterIncomeCategory": "Adja meg a bevétel kategóriáját", "pleaseentervaliddata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a valid adatokat", "saveAndPublished": "Mentés és publikálás", "incomeCategoryList": "Jövedelemkategórialista", "noIncomeCategoryFound": "<PERSON><PERSON> j<PERSON>elemkategória", "incomeDetails": "Jövedelem-adatok", "paymentTypes": "<PERSON><PERSON><PERSON><PERSON> mód", "totalIncome": "Összes jövedelem", "incomeList": "Jövedelemlista", "incomeCategory": "Jövedelemkategória", "newIncome": "<PERSON><PERSON>", "createdBy": "Létrehozta", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noIncomeFound": "<PERSON><PERSON>", "addUpdateIncomeList": "Jövedelemlista hozzáadása/módosítása", "incomeDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterIncomeDate": "Jövedelem d<PERSON>tumának megadása", "incomeFor": "Jövedelemért", "enterNames": "Név megadása", "enterAmount": "Összeg megadása", "printInvoice": "Számla nyomtatása", "moneyReciept": "Pénzbevételi <PERSON>", "billTo": "Számla a következőnek:", "invoiceNo": "Számlaszám", "totalDues": "Összes tartozás", "paidAmount": "Fizetett összeg", "remainingDue": "Maradék tartozás", "deliveryCharge": "Szállítási költség", "INVOICE": "Szá<PERSON>la", "product": "Termék", "quantity": "Mennyiség", "unitPrice": "<PERSON>gy<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalPrice": "<PERSON><PERSON><PERSON>", "subTotal": "Részösszeg", "totalVat": "Összes ÁFA", "totalDiscount": "Összes kedvezmény", "payable": "Fizetendő", "paid": "Fizetett", "serviceCharge": "Szolgáltatási díj", "totalSale": "Összes eladás", "totalPurchase": "Összes vásárlás", "recivedAmount": "Beérkezett összeg", "customerDue": "<PERSON>ev<PERSON><PERSON>", "supplierDue": "Szállítói tartozás", "selectParties": "Felek kiválasztása", "details": "Részletek>", "show": "<PERSON><PERSON>kintés>", "noTransactionFound": "<PERSON><PERSON><PERSON>", "ledgeDetails": "Könyvviteli adatok", "status": "<PERSON><PERSON><PERSON>", "itemName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purchasePrice": "Vásárlási ár", "salePrice": "Eladási ár", "profit": "Profit", "loss": "Veszteség", "total": "Összesen", "totalProfit": "Összes profit", "totalLoss": "Összes veszteség", "unPaid": "Fizetetlen", "lossOrProfit": "Veszteség/Profit", "saleAmount": "Eladási összeg", "profitPlus": "Profit(+)", "profitMinus": "Profit(-)", "yourPaymentIsCancelled": "Fizetése lemon<PERSON>", "yourPaymentIsSuccessfully": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "hold": "Tart", "holdNumber": "Tartszám", "selectSerialNumber": "Válassza ki a sorozatszámot", "serialNumber": "Sorozatszám", "searchSerialNumber": "Keresés soroza<PERSON>zá<PERSON>", "noSerialNumberFound": "<PERSON><PERSON><PERSON>", "nameCodeOrCateogry": "<PERSON><PERSON><PERSON>, kód vagy ka<PERSON>gó<PERSON>", "vatOrgst": "ÁFA/ÁFA", "discount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "areYouWantToCreateThisQuation": "<PERSON>zeretne létrehozni ezt a  quotation?", "updateYourPlanFirst": "Frissítse a tervét először \\ nEladási határérték túllépve.", "quotation": "<PERSON><PERSON><PERSON>", "addProduct": "Termék hozzáadása", "totalProduct": "<PERSON><PERSON><PERSON>", "shpingOrServices": "Szállítás/Szolgáltatás", "addItemCategory": "Tételkategória hozzáadása", "selectVariations": "Válassza ki a variációkat:", "size": "<PERSON><PERSON><PERSON>", "color": "Szín", "wight": "Súly", "capacity": "Kapacitás", "warranty": "Garancia", "addBrand": "Brand hozzáadása", "brandName": "<PERSON><PERSON><PERSON><PERSON>", "enterBrandName": "<PERSON><PERSON><PERSON> be a márka nevét", "addUnit": "Mértékegység hozzáadása", "unitName": "Mértékegység neve", "enterUnitName": "<PERSON><PERSON><PERSON> be a mértékegység nevét", "productNam": "Terméknév*", "enterProductName": "<PERSON><PERSON><PERSON> be a term<PERSON>k nevét", "productType": "Terméktípus", "enterProductType": "<PERSON><PERSON><PERSON> be a termék típus<PERSON>", "productWaranty": "Termékgarancia", "enterWarranty": "<PERSON><PERSON><PERSON> be a garan<PERSON>", "warrantys": "Garancia", "selectWarrantyTime": "Válassza ki a jótállási időt", "brand": "<PERSON><PERSON><PERSON>", "selectProductBrand": "Válassza ki a termékmárkát", "productCod": "Termékkód*", "enterProductCode": "<PERSON><PERSON><PERSON> be a termékkódot", "enterProductQuantity": "<PERSON><PERSON><PERSON> be a termékmennyiséget", "Quantity": "Mennyiség*", "productUnit": "Termékegység", "enterPurchasePrice": "<PERSON><PERSON><PERSON> be a vételárat", "salePrices": "Eladási ár*", "dealerPrice": "Díjszabás", "enterDealePrice": "<PERSON>r<PERSON> be a kereskedő árát", "wholeSaleprice": "Végösszeg", "enterPrice": "<PERSON><PERSON><PERSON> be a<PERSON>", "manufacturer": "G<PERSON><PERSON><PERSON><PERSON>", "enterManufacturerName": "<PERSON><PERSON><PERSON> be a gyártó nevét", "serialNumbers": "Sorozatszám", "enterSerialNumber": "<PERSON><PERSON><PERSON> be a sorozatszámot", "nosSerialNumberFound": "<PERSON><PERSON><PERSON>", "productList": "Terméklista", "searchByName": "Keresés név szerint", "retailer": "Kiskereskedő", "dealer": "<PERSON><PERSON><PERSON>", "wholesale": "Nagykereskedelem", "expense": "<PERSON><PERSON><PERSON>", "totalPayable": "Összes fizetendő", "totalAmount": "Összes összeg", "searchByInvoiceOrName": "Keresés számla vagy név alapján", "invoice": "Szá<PERSON>la", "lossminus": "V<PERSON>zteség (-)", "yourPaymentIscancelled": "A fizetése törölve lett", "previousDue": "Előző esedékesség:", "calculator": "Számológép:", "dashBoard": "Vezérlőpult", "price": "<PERSON><PERSON>", "create": "Létrehozni", "payment": "<PERSON><PERSON><PERSON><PERSON>", "enterPayingAmount": "Adja meg a fizetendő összeget", "enterCategoryName": "Adja meg a kategória nevét", "productSize": "Termék<PERSON><PERSON>", "enterProductSize": "<PERSON>ja meg a <PERSON><PERSON><PERSON> m<PERSON>", "productColor": "Termék színe", "enterProductColor": "Adja meg a termék <PERSON>", "productWeight": "Termék súlya", "enterProductWeight": "<PERSON>ja meg a <PERSON>ék <PERSON>", "productcapacity": "Termék ka<PERSON>ása", "enterProductCapacity": "Adja meg a termék ka<PERSON>ását", "enterSalePrice": "Adja meg az eladási árat", "add": "Hozzáad", "productCategory": "Termékkategória", "enterProductUnit": "Adja meg a termék egységét", "productName": "Terméknév", "noProductFound": "<PERSON><PERSON>", "addingSerialNumber": "So<PERSON>zatszám ho<PERSON>adása?", "unit": "Egység", "editOrAddSerial": "Szerkesztés/hozzáadás sorszám:", "enterWholeSalePrice": "Adja meg a nagykereskedelmi árat", "invoiceCo": "Számla:", "categories": "Kate<PERSON><PERSON><PERSON><PERSON>", "purchaseList": "Vásárlási lista", "print": "Nyomtatás", "noPurchaseTransactionFound": "Nincs vásárlási tranzakció", "quotationList": "Árajánlat lista", "areYouWantToDeleteThisQuotion": "Törli ezt az árajánlatot?", "convertToSale": "Átalakítás eladásra", "noQuotionFound": "<PERSON><PERSON><PERSON>", "stockReport": "Készletjelentés", "PRODUCTNAME": "TERMÉKNÉV", "CATEGORY": "KATEGÓRIA", "PRICE": "ÁR", "QTY": "MENNYISÉG", "STATUS": "ÁLLAPOT", "TOTALVALUE": "ÖSSZES ÉRTÉK", "noReportFound": "<PERSON><PERSON><PERSON>", "remainingBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalpaymentIn": "Összes bejövő fizetés", "totalPaymentOut": "Összes kifizetés", "dailyTransaction": "<PERSON><PERSON>", "paymentIn": "Bejö<PERSON><PERSON> fizetés", "paymentOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "balance": "Tartalék", "totalPaid": "Összes kifizetett", "dueTransaction": "Esedékes <PERSON>", "downloadPDF": "PDF letöltése", "customerType": "<PERSON><PERSON><PERSON>", "pleaseAddCustomer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ad<PERSON> ho<PERSON> vev<PERSON>t", "purchaseTransaction": "Vásárlási tranzakció", "printPdf": "PDF nyomtatása", "saleTransactionQuatationHistory": "Értékesítési t<PERSON>k (árajánlat-értékesítési előzmények)", "ADDSALE": "ÉRTÉKESÍTÉS HOZZÁADÁSA", "search": "Keresés.......", "transactionReport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saleTransaction": "Értékesítési tran<PERSON>ó", "totalReturns": "Összes visszatérítés", "totalReturnAmount": "Összes visszatérítési összeg", "saleReturn": "Értékesítési v<PERSON>zatérí<PERSON>s", "noSaleTransaactionFound": "<PERSON>ncs értékesítési tranzakció található", "saleList": "Értékesítési lista", "reports": "<PERSON><PERSON><PERSON><PERSON>", "areYouWantToReturnThisSale": "Szeretné v<PERSON>zaadni ezt az értékesítést?", "no": "Nem", "yesReturn": "Igen, visszatérítés", "setting": "Be<PERSON>llít<PERSON>", "uploadAnInvoiceLogo": "Számla logó feltöltése", "showLogoInInvoice": "Logó megjelenítése a számlán?", "logoPositionInInvoice": "Logó pozíciója a számlán?", "left": "Balra", "right": "Jobbra", "companyAddress": "<PERSON><PERSON><PERSON> c<PERSON>", "enterYourCompanyAddress": "Adja meg a cég címét", "companyPhoneNumber": "Cég telefonszáma", "companyEmailAddress": "Cég e-mail címe", "enterCompanyPhoneNumber": "Adja meg a cég telefonszámát", "enterCompanyEmailAddress": "Adja meg a cég e-mail címét", "companyWebsiteUrl": "Cég weboldal URL-je", "enterCompanyWebsiteUrl": "Adja meg a cég weboldal URL-jét", "companyDescription": "<PERSON><PERSON><PERSON>", "enterCompanyDesciption": "Adja meg a cég leí<PERSON>", "saveChanges": "Változtatások mentése", "kycVerification": "KYC ellenőrzés", "identityVerify": "Azonosítás ellenőrzése", "yourNeedToIdentityVerify": "Azonosítania kell mag<PERSON>, mielőtt üzeneteket vásárol", "govermentId": "Kormányzati <PERSON>", "takeADriveLisense": "Vegyen egy vezet<PERSON>i en<PERSON>, s<PERSON><PERSON><PERSON><PERSON> igazolványt vagy <PERSON>", "addDucument": "Dokumentum hozzáadása", "youNeedToIdentityVerifySms": "Azonosítania kell mag<PERSON>, mielőtt üzeneteket vásárol", "wholeSeller": "Nagykereskedő", "enterSmsContent": "Adja meg az üzenet tartalmát", "sendMessage": "Üzenet küldése", "buySms": "Üzenetek vásárlása", "supplierList": "Beszállítói lista", "addSupplier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "noSupplierFound": "<PERSON><PERSON>", "checkWarranty": "Garancia ellenőrz<PERSON>e", "customerInvoices": "<PERSON><PERSON><PERSON><PERSON>", "supplierInvoice": "Beszállítói számla", "addItem": "<PERSON><PERSON>", "noInvoiceFound": "<PERSON><PERSON>", "stock": "<PERSON><PERSON><PERSON><PERSON>", "enterStockAmount": "Adja meg a készletmennyiséget", "discountPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterDiscountPrice": "Adja meg a kedvezményes árat", "dateTime": "Dátum <PERSON>", "walkInCustomer": "Bejövő ügyfél", "saleDetails": "Értékesítési részletek", "customerWalkIncostomer": "Ügyfél: <PERSON><PERSON><PERSON><PERSON><PERSON> ügyfél", "item": "<PERSON><PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "totalItem2": "Teljes elem: 2", "shipingOrOther": "Szállítás/Egyéb", "yourDueSales": "Önnek j<PERSON>", "yourAllSales": "Összes eladása", "invoiceHint": "Számlaszám..", "customer": "Ügyfél", "dueAmountWillShowHere": "A fennmaradó összeg itt jelenik meg, ha elérhető", "thisCustmerHasNoDue": "Ennek a vevőnek nincs fennmaradó összege", "pleaseSelectACustomer": "Válasszon ki egy ügyfelet", "pleaseAddASale": "<PERSON><PERSON> hozz<PERSON> egy el<PERSON>", "yourAllSaleList": "Összes eladási listája", "changeableAmount": "Változható összeg", "sales": "Értékesíté<PERSON>k", "dueList": "Fennmaradó listák", "ledger": "Könyv", "transaction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subciption": "Felirat<PERSON>z<PERSON>", "upgradeOnMobileApp": "Frissítés a mobilalkalmazáson", "POSSale": "POS-eladás", "searchAnyThing": "Ke<PERSON><PERSON> b<PERSON>i...", "sale": "Eladás", "logOut": "Kijelentkezés", "cashAndBank": "Készpénz és bank", "cashInHand": "Készpénz", "bankAccounts": "Bankszámlák", "creativeHub": "Kreatív központ", "openCheques": "Nyitott csekkek", "loanAccounts": "Hitelszámlák", "share": "Megosztás", "preview": "Előnézet", "dueCollection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerOfTheMonth": "A hónap ügyfele", "topSellingProduct": "<PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>", "statistic": "Statisztika", "stockValues": "<PERSON><PERSON><PERSON><PERSON>", "lowStock": "Alacsony <PERSON>", "fivePurchase": "A hónap öt legtöbbet vásárolt terméke", "recentSale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tSale": "Összes Eladás", "sAmount": "Eladási Összeg", "expenses": "<PERSON><PERSON><PERSON><PERSON>", "inc": "Bevétel", "prof": "Profil"}