// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a da locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'da';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("TILFØJ SALG"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORI"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS-salg"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRIS"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("PRODUKTNAVN"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Loginpanel"),
        "QTY": MessageLookupByLibrary.simpleMessage("ANTAL"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Mængde*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("SAMLET VÆRDI"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Brugertitel"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Om Appen"),
        "accountName": MessageLookupByLibrary.simpleMessage("Kontonavn"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Kontonummer"),
        "action": MessageLookupByLibrary.simpleMessage("Handling"),
        "add": MessageLookupByLibrary.simpleMessage("Tilføj"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Tilføj Mærke"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Tilføj kategori"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Tilføj kunde"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Tilføj beskrivelse...."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Tilføj dokumenter"),
        "addItem": MessageLookupByLibrary.simpleMessage("Tilføj vare"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Tilføj Varekategori"),
        "addNew": MessageLookupByLibrary.simpleMessage("Tilføj Ny"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("Tilføj Ny Bruger"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Tilføj Produkt"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Tilføjet med Succes"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Tilføj leverandør"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Tilføj Enhed"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("Tilføj/Opdater udgiftsliste"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Tilføj/Opdater indkomstliste"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Tilføj Brugerrolle"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Tilføjelse af serienummer?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresse"),
        "all": MessageLookupByLibrary.simpleMessage("Alle"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Alle grundlæggende funktioner"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Har du allerede en konto?"),
        "amount": MessageLookupByLibrary.simpleMessage("Beløb"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Android & iOS App Support"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Ønsker du at oprette dette Tilbud?"),
        "areYouWantToDeleteThisCustomer":
            MessageLookupByLibrary.simpleMessage("Vil du slette denne kunde?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Ønsker du at slette dette produkt"),
        "areYouWantToDeleteThisQuotion":
            MessageLookupByLibrary.simpleMessage("Vil du slette dette tilbud?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Ønsker du at returnere dette salg?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Bankkontovaluta"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankkonti"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankoplysninger"),
        "bankName": MessageLookupByLibrary.simpleMessage("Banknavn"),
        "between": MessageLookupByLibrary.simpleMessage("Mellem"),
        "billTo": MessageLookupByLibrary.simpleMessage("Faktura til:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Afdelingsnavn"),
        "brand": MessageLookupByLibrary.simpleMessage("Mærke"),
        "brandName": MessageLookupByLibrary.simpleMessage("Mærkenavn"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Forretningskategori"),
        "buy": MessageLookupByLibrary.simpleMessage("Køb"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Køb Premium Plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Køb sms"),
        "calculator": MessageLookupByLibrary.simpleMessage("Lommeregner:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annuller"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacitet"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("Kontanter og bank"),
        "cashInHand":
            MessageLookupByLibrary.simpleMessage("Kontanter i hånden"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorier"),
        "category": MessageLookupByLibrary.simpleMessage("Kategori"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategorinavn"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Ændr beløb"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Ændringsbeløb"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("Tjek garanti"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Vælg en plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Indsamle forfaldne >"),
        "color": MessageLookupByLibrary.simpleMessage("Farve"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Virksomhedsnavn"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Virksomhedsadresse"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Virksomhedsbeskrivelse"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Virksomhedens e-mailadresse"),
        "companyName": MessageLookupByLibrary.simpleMessage("Virksomhedsnavn"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Virksomhedens telefonnummer"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Virksomhedens webstedsadresse"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Bekræft adgangskode"),
        "continu": MessageLookupByLibrary.simpleMessage("Fortsæt"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konverter til salg"),
        "create": MessageLookupByLibrary.simpleMessage("Opret"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Opret betaling"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Oprettet af"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreativt center"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Nuværende plan"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Tilpasning af Fakturabranding"),
        "customer": MessageLookupByLibrary.simpleMessage("Kunde"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Kunde forfald"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Kunde fakturaer"),
        "customerList": MessageLookupByLibrary.simpleMessage("Kundeliste"),
        "customerName": MessageLookupByLibrary.simpleMessage("Kundens navn"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Månedens kunde"),
        "customerType": MessageLookupByLibrary.simpleMessage("Kundetype"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Kunde: Gå ind kunde"),
        "customers": MessageLookupByLibrary.simpleMessage("Kunder"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Daglig indsamling"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Dagligt salg"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Daglig transaktion"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Instrumentbræt"),
        "date": MessageLookupByLibrary.simpleMessage("Dato"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Dato Tid"),
        "dealer": MessageLookupByLibrary.simpleMessage("Distributør"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Forhandlerpris"),
        "delete": MessageLookupByLibrary.simpleMessage("Slet"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Leveringsgebyr"),
        "description": MessageLookupByLibrary.simpleMessage("Beskrivelse"),
        "details": MessageLookupByLibrary.simpleMessage("Detaljer >"),
        "discount": MessageLookupByLibrary.simpleMessage("Rabat"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Rabatpris"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Hent PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Skyldig"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Forfaldent beløb"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Forfaldent beløb vises her, hvis tilgængeligt"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Forfaldsinddrivelse"),
        "dueList": MessageLookupByLibrary.simpleMessage("Forfaldsliste"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Skyldig transaktion"),
        "edit": MessageLookupByLibrary.simpleMessage("Rediger"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Rediger/Tilføj serienummer:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Rediger din profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Indtast beløb"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Indtast Mærkenavn"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Indtast kategorinavn"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Indtast virksomhedsbeskrivelse"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Indtast virksomhedens e-mailadresse"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Indtast virksomhedens telefonnummer"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Indtast virksomhedens webstedsadresse"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Indtast kundens navn"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Indtast Forhandlerpris"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Indtast rabatpris"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Indtast udgiftskategori"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Indtast udgifts dato"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Indtast indkomstkategori"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Indtast indkomstdato"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Indtast Producentnavn"),
        "enterName": MessageLookupByLibrary.simpleMessage("Indtast navn"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Indtast navne"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Indtast bemærkning"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Indtast åbningsbalance"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Indtast betalte beløb"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Indtast Adgangskode"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Indtast betalingsbeløb"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Indtast Pris"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Indtast produktkapacitet"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Indtast Produktkode"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Indtast produktfarve"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Indtast Produktnavn"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Indtast Produktmængde"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Indtast produktstørrelse"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Indtast Produkttype"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Indtast produktenhed"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Indtast produktvægt"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Indtast Købspris"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Indtast referencenummer"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Indtast salgspris"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Indtast Serienummer"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Indtast beskedindhold"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Indtast lagermængde"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Indtast Transaktions-ID"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Indtast Enhedsnavn"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Indtast Brugerrolle Navn"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Indtast brugertitel"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Indtast Garanti"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Indtast engrospris"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Indtast din saldo"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Indtast din adresse"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Indtast din virksomhedsadresse"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Indtast dit virksomhedsnavn"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Indtast dit virksomhedsnavn"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Indtast din email-adresse"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Indtast din adgangskode"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Indtast din adgangskode igen"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Indtast dit telefonnummer"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Indtast dit butiksnavn"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Indtast kategorinavn"),
        "expense": MessageLookupByLibrary.simpleMessage("Udgift"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Udgifts dato"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Udgiftdetaljer"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Udgift til"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Udgiftskategoriliste"),
        "expenses": MessageLookupByLibrary.simpleMessage("Udgifter"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top fem købsprodukter i måneden"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Til Ubegrænset Brug"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Glemt adgangskode?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Gratis Databackup"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Gratis Livstidsopdatering"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Gratis Pakke"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Gratis Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Kom i gang"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Regerings-ID"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Samlet total"),
        "hold": MessageLookupByLibrary.simpleMessage("Hold"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Holdnummer"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Identitetsverifikation"),
        "inc": MessageLookupByLibrary.simpleMessage("Indkomst"),
        "income": MessageLookupByLibrary.simpleMessage("Indkomst"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Indkomstkategori"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Indkomstkategoriliste"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Indkomstdato"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Indkomstdetaljer"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Indkomst for"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Indkomstliste"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Øg Beholdning"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Øjeblikkelig privatliv"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Faktura:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Fakturanr.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Fakturanummer"),
        "item": MessageLookupByLibrary.simpleMessage("Vare"),
        "itemName": MessageLookupByLibrary.simpleMessage("Varenavn"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC-verifikation"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Bogføringsdetaljer"),
        "ledger": MessageLookupByLibrary.simpleMessage("Bogføring"),
        "left": MessageLookupByLibrary.simpleMessage("Venstre"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Lånekonti"),
        "logOut": MessageLookupByLibrary.simpleMessage("Log ud"),
        "login": MessageLookupByLibrary.simpleMessage("Log ind"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Logoets placering på fakturaen?"),
        "loss": MessageLookupByLibrary.simpleMessage("Tab"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Tab/Fortjeneste"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Tab(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Lav Lagerbeholdning"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Lavt lager"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Sæt et varigt indtryk på dine kunder med brandede fakturaer. Vores Ubegrænsede Opgradering tilbyder den unikke fordel ved at tilpasse dine fakturaer, hvilket tilføjer et professionelt touch, der styrker din brandidentitet og fremmer kundeloyalitet."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Producent"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Login Panel"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Tilmeldingspanel"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobilapp\n+\nSkrivebord"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Kvittering for betaling"),
        "nam": MessageLookupByLibrary.simpleMessage("Navn*"),
        "name": MessageLookupByLibrary.simpleMessage("Navn"),
        "nameCodeOrCateogry": MessageLookupByLibrary.simpleMessage(
            "Navn eller Kode eller Kategori"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nye kunder"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nye kunder"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Ny indkomst"),
        "no": MessageLookupByLibrary.simpleMessage("Nej"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Ingen Forbindelse"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Ingen kunde fundet"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Ingen forfaldne transaktioner fundet"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Ingen udgiftskategori fundet"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Ingen indkomstkategori fundet"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Ingen indkomst fundet"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Ingen faktura fundet"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Ingen produkter fundet"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Ingen købstransaktion fundet"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Ingen tilbud fundet"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Ingen rapport fundet"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Ingen salgstransaktion fundet"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Intet Serienummer Fundet"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Ingen leverandør fundet"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Ingen Transaktion Fundet"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Ingen Bruger Fundet"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Ingen Brugerrolle Fundet"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Intet Serienummer Fundet"),
        "note": MessageLookupByLibrary.simpleMessage("Bemærkning"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Åbne checks"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Åbningsbalance"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage(" eller træk & slip PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Ordrer"),
        "other": MessageLookupByLibrary.simpleMessage("Andet"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Anden indkomst"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Pakke Funktion"),
        "paid": MessageLookupByLibrary.simpleMessage("Betalt"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Betalt beløb"),
        "partyName": MessageLookupByLibrary.simpleMessage("Festnavn"),
        "partyType": MessageLookupByLibrary.simpleMessage("Festtype"),
        "password": MessageLookupByLibrary.simpleMessage("Adgangskode"),
        "payCash":
            MessageLookupByLibrary.simpleMessage("Betaling med Kontanter"),
        "payable": MessageLookupByLibrary.simpleMessage("Betalelig"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Betalingsbeløb"),
        "payment": MessageLookupByLibrary.simpleMessage("Betaling"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Betaling ind"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Betaling ud"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Betalingsmetode"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Betalingsmetode"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefonnummer"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefonverifikation"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Tilføj venligst et salg"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Tilføj venligst kunde"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Tjek din internetforbindelse"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Download venligst vores mobilapp og abonner på en pakke for at bruge desktop-versionen"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Indtast produktbeholdning"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Indtast venligst gyldige data"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Vælg venligst en kunde"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Indtast venligst gyldige data"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Tilmeldingspanel"),
        "practies": MessageLookupByLibrary.simpleMessage("Øvelser"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Kundesupport"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Forhåndsvisning"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Tidligere saldo:"),
        "price": MessageLookupByLibrary.simpleMessage("Pris"),
        "print": MessageLookupByLibrary.simpleMessage("Print"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Udskriv faktura"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Print PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Privatlivspolitik"),
        "product": MessageLookupByLibrary.simpleMessage("Produkt"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Produktkategori"),
        "productCod": MessageLookupByLibrary.simpleMessage("Produktkode*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Produktfarve"),
        "productList": MessageLookupByLibrary.simpleMessage("Produktliste"),
        "productNam": MessageLookupByLibrary.simpleMessage("Produktnavn*"),
        "productName": MessageLookupByLibrary.simpleMessage("Produktnavn"),
        "productSize": MessageLookupByLibrary.simpleMessage("Produktstørrelse"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Produktbeholdning"),
        "productType": MessageLookupByLibrary.simpleMessage("Produkttype"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Produktenhed"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Produktgaranti"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Produktvægt"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Produktkapacitet"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Rediger Profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Fortjeneste"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Fortjeneste(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Fortjeneste(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Køb"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Købsliste"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Køb Premium Plan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Købspris"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Købstransaktion"),
        "quantity": MessageLookupByLibrary.simpleMessage("Antal"),
        "quotation": MessageLookupByLibrary.simpleMessage("Tilbud"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Tilbudsliste"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Nylige Salg"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referencenummer"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referencenummer"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrering"),
        "remaining": MessageLookupByLibrary.simpleMessage("Resterende: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Resterende saldo"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Resterende forfald"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapporter"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Nulstil din adgangskode"),
        "retailer": MessageLookupByLibrary.simpleMessage("Forhandler"),
        "revenue": MessageLookupByLibrary.simpleMessage("Indtægt"),
        "right": MessageLookupByLibrary.simpleMessage("Højre"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Salgsbeløb"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Beskyt dine forretningsdata uden besvær. Vores Pos Saas POS Unlimited Upgrade inkluderer gratis databackup, hvilket sikrer, at dine værdifulde oplysninger beskyttes mod uforudsete begivenheder. Fokuser på det, der virkelig betyder noget - din forretningsvækst."),
        "sale": MessageLookupByLibrary.simpleMessage("Salg"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Salgsbeløb"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Salgsdetaljer"),
        "saleList": MessageLookupByLibrary.simpleMessage("Salgsliste"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Salgspris"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Salgspris*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Salgsretur"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Salgstransaktion"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Salgstransaktioner (Tilbudssalghistorik)"),
        "sales": MessageLookupByLibrary.simpleMessage("Salg"),
        "salesList": MessageLookupByLibrary.simpleMessage("Salgsliste"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Gem og offentliggør"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Gem og offentliggør"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Gem ændringer"),
        "search": MessageLookupByLibrary.simpleMessage("Søg..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Søg efter noget..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Søg efter faktura...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Søg efter faktura eller navn"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Søg Efter Navn"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Søg efter navn eller telefon..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Søg Serienummer"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Vælg Parter"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Vælg Produktmærke"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Vælg Serienummer"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Vælg Variationer:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Vælg Garantitid"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Vælg dit sprog"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Send besked"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serienummer"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serienummer"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Servicegebyr"),
        "setting": MessageLookupByLibrary.simpleMessage("Indstillinger"),
        "share": MessageLookupByLibrary.simpleMessage("Del"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Forsendelse/Andet"),
        "shopName": MessageLookupByLibrary.simpleMessage("Butiksnavn"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Butiksåbningsbalance"),
        "show": MessageLookupByLibrary.simpleMessage("Vis >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Vis logo på fakturaen?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Fragt/Service"),
        "size": MessageLookupByLibrary.simpleMessage("Størrelse"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistik"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Forbliv i fronten af teknologiske fremskridt uden ekstra omkostninger. Vores Pos Saas POS Unlimited Upgrade sikrer, at du altid har de nyeste værktøjer og funktioner ved hånden og garanterer, at din forretning forbliver topmoderne."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Forbliv i fronten af teknologiske fremskridt uden ekstra omkostninger. Vores Pos Sass POS Unlimited Upgrade sikrer, at du altid har de nyeste værktøjer og funktioner ved hånden og garanterer, at din forretning forbliver topmoderne."),
        "stock": MessageLookupByLibrary.simpleMessage("Lager"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Lagerbeholdning"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Lager rapport"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Lager værdi"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Lager Værdier"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abonnement"),
        "submit": MessageLookupByLibrary.simpleMessage("Indsend"),
        "supplier": MessageLookupByLibrary.simpleMessage("Leverandører"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Leverandørskyld"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Leverandør faktura"),
        "supplierList": MessageLookupByLibrary.simpleMessage("Leverandørliste"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT-kode"),
        "tSale": MessageLookupByLibrary.simpleMessage("Samlet Salg"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Tag et kørekort, nationalt id-kort eller pasfoto"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Brugsvilkår"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Navnet siger det hele. Med Pos Saas POS Unlimited er der ingen begrænsning i din brug. Uanset om du behandler en håndfuld transaktioner eller oplever et rush af kunder, kan du arbejde med tillid, vel vidende at du ikke er begrænset af begrænsninger."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Denne kunde skylder ikke noget"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Denne kunde har tidligere skyld"),
        "to": MessageLookupByLibrary.simpleMessage("Til"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Top sælgende produkt"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Samlet beløb"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Samlet rabat"),
        "totalDue":
            MessageLookupByLibrary.simpleMessage("Total skyldige beløb"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Samlede forfald"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Samlet udgift"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Samlet indkomst"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Total antal : 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Samlet Tab"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Samlet betalt"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Samlet betalingspligtig"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Samlet betaling ud"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Samlet pris"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Samlet Antal Produkter"),
        "totalProfit":
            MessageLookupByLibrary.simpleMessage("Samlet Fortjeneste"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Samlet køb"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Samlet returbeløb"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Samlede returer"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Samlet salg"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Samlet salg"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Samlet moms"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Samlet betaling ind"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaktion"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Transaktions-ID"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Transaktionsrapport"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Prøv Igen"),
        "type": MessageLookupByLibrary.simpleMessage("Type"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Ubetalt"),
        "unit": MessageLookupByLibrary.simpleMessage("Enheder"),
        "unitName": MessageLookupByLibrary.simpleMessage("Enhed Navn"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Enhedspris"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Ubegrænset"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Ubegrænsede fakturaer"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Ubegrænset Brug"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Lås fuldt ud potentialet af Pos Saas POS op med personlige træningssessioner ledet af vores eksperthold. Fra grundlæggende til avancerede teknikker sørger vi for, at du er velbevandret i at udnytte alle aspekter af systemet til at optimere dine forretningsprocesser."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Opdater Nu"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Opdater dit abonnement først\\nSalgsgrænsen er overskredet."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Opgrader via mobilapp"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Upload et billede"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Upload et faktura logo"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Upload Dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Upload Fil"),
        "userName": MessageLookupByLibrary.simpleMessage("Brugernavn"),
        "userRole": MessageLookupByLibrary.simpleMessage("Brugerrolle"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Brugerrolle Navn"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Brugertitel"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("Moms/Afgift"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Bekræft telefonnummer"),
        "view": MessageLookupByLibrary.simpleMessage("Se"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Gå ind kunde"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garanti"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garanti"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Vi skal registrere din telefon, før du kommer i gang!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Vi forstår vigtigheden af sømløs drift. Derfor er vores døgnåbne support til rådighed for at hjælpe dig, hvad enten det er en hurtig forespørgsel eller en omfattende bekymring. Forbind med os når som helst og hvor som helst via opkald eller WhatsApp for at opleve en enestående kundeservice."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Engrospris"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Engroshandler"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Engros"),
        "wight": MessageLookupByLibrary.simpleMessage("Vægt"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ja, returner"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Du skal LOGGE IND igen på din konto."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Du skal identitetsverificere dig, før du kan købe beskeder"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Din samlede salgsliste"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Dine alle salg"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Du bruger"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Dine forfaldne salg"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Du skal identitetsverificere dig, før du kan købe beskeder"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Dit Pakke"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Din Betaling er Annulleret"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Din Betaling er Gennemført"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Din betaling er annulleret")
      };
}
