// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a sv locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'sv';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE":
            MessageLookupByLibrary.simpleMessage("LÄGG TILL FÖRSÄLJNING"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORI"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Försäljning i POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRIS"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("PRODUKTNAMN"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Inloggningspanel"),
        "QTY": MessageLookupByLibrary.simpleMessage("ANTAL"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("TOTALT VÄRDE"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Användartitel"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Om appen"),
        "accountName": MessageLookupByLibrary.simpleMessage("Kontonamn"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Kontonummer"),
        "action": MessageLookupByLibrary.simpleMessage("Åtgärd"),
        "add": MessageLookupByLibrary.simpleMessage("Lägg till"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Lägg till Märke"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Lägg till kategori"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Lägg till kund"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Lägg till beskrivning..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Lägg till dokument"),
        "addItem": MessageLookupByLibrary.simpleMessage("Lägg till artikel"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Lägg Till Artikelkategori"),
        "addNew": MessageLookupByLibrary.simpleMessage("Lägg till nytt"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Lägg till ny användare"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Lägg Till Produkt"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Tillagt framgångsrikt"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Lägg till leverantör"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Lägg till Enhet"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Lägg till/Uppdatera utgiftslista"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Lägg till/Uppdatera inkomstlista"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Lägg till användarroll"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Lägger till serienummer?"),
        "address": MessageLookupByLibrary.simpleMessage("Adress"),
        "all": MessageLookupByLibrary.simpleMessage("Alla"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Alla grundläggande funktioner"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Har redan ett konto?"),
        "amount": MessageLookupByLibrary.simpleMessage("Belopp"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Support för Android och iOS-appar"),
        "areYouWantToCreateThisQuation":
            MessageLookupByLibrary.simpleMessage("Vill du skapa denna Offert?"),
        "areYouWantToDeleteThisCustomer":
            MessageLookupByLibrary.simpleMessage("Vill du ta bort denna kund?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Vill du ta bort den här produkten"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Vill du ta bort denna offert?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Vill du returnera denna försäljning?"),
        "balance": MessageLookupByLibrary.simpleMessage("Balans"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankkonton"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankinformation"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bankens namn"),
        "between": MessageLookupByLibrary.simpleMessage("Mellan"),
        "billTo": MessageLookupByLibrary.simpleMessage("Faktureras till:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Avdelningsnamn"),
        "brand": MessageLookupByLibrary.simpleMessage("Märke"),
        "brandName": MessageLookupByLibrary.simpleMessage("Märkesnamn"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Affärskategori"),
        "buy": MessageLookupByLibrary.simpleMessage("Köp"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Köp Premiumplan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Köp SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkylator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Avbryt"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacitet"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Kassa och Bank"),
        "cashInHand":
            MessageLookupByLibrary.simpleMessage("Kontanter i handen"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorier"),
        "category": MessageLookupByLibrary.simpleMessage("Kategori"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategorinamn"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Ändra belopp"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Ändringsbar summa"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Kontrollera garantin"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Välj en plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Inkassera förfall >"),
        "color": MessageLookupByLibrary.simpleMessage("Färg"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Företagsnamn"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Företagsadress"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Företagsbeskrivning"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Företagets e-postadress"),
        "companyName": MessageLookupByLibrary.simpleMessage("Företagsnamn"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Företagets telefonnummer"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Företagets webbplatsadress"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Bekräfta lösenord"),
        "continu": MessageLookupByLibrary.simpleMessage("Fortsätt"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konvertera till försäljning"),
        "create": MessageLookupByLibrary.simpleMessage("Skapa"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Skapa betalning"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Skapad av"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Skapande Hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Nuvarande plan"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Anpassad fakturamärkning"),
        "customer": MessageLookupByLibrary.simpleMessage("Kund"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Kundskuld"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Kundfakturor"),
        "customerList": MessageLookupByLibrary.simpleMessage("Kundlista"),
        "customerName": MessageLookupByLibrary.simpleMessage("Kundnamn"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Månadens kund"),
        "customerType": MessageLookupByLibrary.simpleMessage("Kundtyp"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Kund: Kund som kommer in"),
        "customers": MessageLookupByLibrary.simpleMessage("Kunder"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Daglig insamling"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Daglig försäljning"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Daglig transaktion"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Instrumentpanel"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum Tid"),
        "dealer": MessageLookupByLibrary.simpleMessage("Återförsäljare"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Återförsäljarpris"),
        "delete": MessageLookupByLibrary.simpleMessage("Ta bort"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Leveransavgift"),
        "description": MessageLookupByLibrary.simpleMessage("Beskrivning"),
        "details": MessageLookupByLibrary.simpleMessage("Detaljer >"),
        "discount": MessageLookupByLibrary.simpleMessage("Rabatt"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Rabatterat pris"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Ladda ner PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Förfallen"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Förfall belopp"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Förfallen summa visas här om tillgänglig"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Inkasso av förfallen summa"),
        "dueList": MessageLookupByLibrary.simpleMessage("Förfallen lista"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Förfallotransaktion"),
        "edit": MessageLookupByLibrary.simpleMessage("Redigera"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Redigera/Lägg till serienummer:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Redigera din profil"),
        "email": MessageLookupByLibrary.simpleMessage("E-post"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Ange belopp"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Ange Märkesnamn"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Ange kategorinamn"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Ange företagsbeskrivning"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Ange företagets e-postadress"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Ange företagets telefonnummer"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Ange företagets webbplatsadress"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Ange kundnamn"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Ange rabatterat pris"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Ange utgiftskategori"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Ange utgiftsdatum"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Ange inkomstkategori"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Ange inkomstdatum"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Ange Tillverkarnamn"),
        "enterName": MessageLookupByLibrary.simpleMessage("Ange namn"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Ange namn"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Ange anteckning"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Ange ingående balans"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Ange betalat belopp"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Ange lösenord"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Ange betalningsbelopp"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Ange Pris"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Ange produktkapacitet"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Ange Produktkod"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Ange produktfärg"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Ange Produktnamn"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Ange Produktmängd"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Ange produktstorlek"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Ange Produkttyp"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Ange produktenshet"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Ange produktvikt"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Ange Inköpspris"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Ange referensnummer"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Ange försäljningspris"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Ange Serienummer"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Ange meddelandeinnehåll"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Ange lagerbelopp"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Ange transaktions-ID"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Ange Enhetens namn"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Ange användarrollsnamn"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Ange användartitel"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Ange Garanti"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Ange partihandelspris"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Ange beloppet"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Ange din adress"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Ange din företagsadress"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Ange ditt företagsnamn"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Ange ditt företagsnamn"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Ange din e-postadress"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Ange ditt lösenord"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Ange ditt lösenord igen"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Ange ditt telefonnummer"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Ange ditt butiksnamn"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Ange kategorinamn"),
        "expense": MessageLookupByLibrary.simpleMessage("Kostnad"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Utgiftsdatum"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Utgiftsdetaljer"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Utgift för"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Utgiftskategorilista"),
        "expenses": MessageLookupByLibrary.simpleMessage("Utgifter"),
        "fivePurchase":
            MessageLookupByLibrary.simpleMessage("Top fem köp av månaden"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("För obegränsad användning"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Glömt lösenord?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Gratis dataskyddskopia"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Gratis livstidsuppdatering"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Gratis paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Gratis plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Kom igång"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Regerings-ID"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Totalt belopp"),
        "hold": MessageLookupByLibrary.simpleMessage("Håll"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Hållnummer"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verifiera identitet"),
        "inc": MessageLookupByLibrary.simpleMessage("Inkomst"),
        "income": MessageLookupByLibrary.simpleMessage("Inkomst"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Inkomstkategori"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Inkomstkategorilista"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Inkomstdatum"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Inkomstdetaljer"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Inkomst för"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Inkomstlista"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Öka lagret"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Omedelbar sekretess"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Faktura:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Fakturanummer..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Fakturanummer"),
        "item": MessageLookupByLibrary.simpleMessage("Artikel"),
        "itemName": MessageLookupByLibrary.simpleMessage("Artikelnamn"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC-verifiering"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Verifikationsdetaljer"),
        "ledger": MessageLookupByLibrary.simpleMessage("Huvudbok"),
        "left": MessageLookupByLibrary.simpleMessage("Vänster"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Lånekonton"),
        "logOut": MessageLookupByLibrary.simpleMessage("Logga ut"),
        "login": MessageLookupByLibrary.simpleMessage("Logga in"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Logotypens position i fakturan?"),
        "loss": MessageLookupByLibrary.simpleMessage("Förlust"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Förlust/Vinst"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Förlust(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Lågt Lager"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Lågt lagersaldo"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Gör ett varaktigt intryck på dina kunder med varumärkta fakturor. Vår Obegränsade Uppgradering erbjuder den unika fördelen att anpassa dina fakturor och lägga till en professionell touch som förstärker ditt varumärkesidentitet och främjar kundlojalitet."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Tillverkare"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Inloggning"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Registrering"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobilapp\n+\nSkrivbord"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Kvitto"),
        "nam": MessageLookupByLibrary.simpleMessage("Namn*"),
        "name": MessageLookupByLibrary.simpleMessage("Namn"),
        "nameCodeOrCateogry": MessageLookupByLibrary.simpleMessage(
            "Namn eller Kod eller Kategori"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nya kunder"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nya kunder"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Ny inkomst"),
        "no": MessageLookupByLibrary.simpleMessage("Nej"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Ingen anslutning"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Ingen kund hittades"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Ingen förfallstransaktion hittades"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Ingen utgiftskategori hittades"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Ingen inkomstkategori hittades"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Ingen inkomst hittades"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Ingen faktura hittades"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Ingen produkt hittades"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Ingen köptransaktion hittades"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Ingen offert hittades"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Ingen rapport hittades"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Ingen försäljningstransaktion hittades"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Inget Serienummer Hittades"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Ingen leverantör hittades"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Ingen Transaktion Hittad"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Ingen användare hittad"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Ingen användarroll hittades"),
        "note": MessageLookupByLibrary.simpleMessage("Anteckning"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Öppna checkar"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Ingående balans"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " eller dra och släpp PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Beställningar"),
        "other": MessageLookupByLibrary.simpleMessage("Annat"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Annat intäkter"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("Paketfunktion"),
        "paid": MessageLookupByLibrary.simpleMessage("Betalt"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Betalt belopp"),
        "partyName": MessageLookupByLibrary.simpleMessage("Partinamn"),
        "partyType": MessageLookupByLibrary.simpleMessage("Partityp"),
        "password": MessageLookupByLibrary.simpleMessage("Lösenord"),
        "payCash": MessageLookupByLibrary.simpleMessage("Betala kontant"),
        "payable": MessageLookupByLibrary.simpleMessage("Att betala"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Betalningsbelopp"),
        "payment": MessageLookupByLibrary.simpleMessage("Betalning"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Betalning in"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Betalning ut"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Betalningstyp"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Betalningstyp"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefonnummer"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefonverifikation"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Vänligen lägg till en försäljning"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Lägg till kund"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Kontrollera din internetanslutning"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Vänligen ladda ner vår mobilapp och prenumerera på en paket för att använda desktop-versionen"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Ange produktlager"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Ange giltiga uppgifter"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Vänligen välj en kund"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Ange giltiga data"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Registreringspanel"),
        "practies": MessageLookupByLibrary.simpleMessage("Öva"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium kundsupport"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premiumplan"),
        "preview": MessageLookupByLibrary.simpleMessage("Förhandsvisning"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Tidigare skuld:"),
        "price": MessageLookupByLibrary.simpleMessage("Pris"),
        "print": MessageLookupByLibrary.simpleMessage("Skriv ut"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Skriv ut faktura"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Skriv ut PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Integritetspolicy"),
        "product": MessageLookupByLibrary.simpleMessage("Produkt"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Produktkategori"),
        "productColor": MessageLookupByLibrary.simpleMessage("Produktfärg"),
        "productList": MessageLookupByLibrary.simpleMessage("Produktlista"),
        "productName": MessageLookupByLibrary.simpleMessage("Produktnamn"),
        "productSize": MessageLookupByLibrary.simpleMessage("Produktstorlek"),
        "productStock": MessageLookupByLibrary.simpleMessage("Produktlager"),
        "productType": MessageLookupByLibrary.simpleMessage("Produkttyp"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Produktenhet"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Produktvikt"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Produktkapacitet"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Redigera profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Vinst"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Vinst(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Vinst(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Inköp"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Inköpslista"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Köp Premiumplan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Inköpspris"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Inköpstransaktion"),
        "quantity": MessageLookupByLibrary.simpleMessage("Mängd*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Offert"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Offertlista"),
        "recentSale":
            MessageLookupByLibrary.simpleMessage("Senaste Försäljningarna"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referensnummer"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referensnummer"),
        "registration": MessageLookupByLibrary.simpleMessage("Registrering"),
        "remaining": MessageLookupByLibrary.simpleMessage("Återstående: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Återstående balans"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Återstående belopp"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapporter"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Återställ ditt lösenord"),
        "retailer": MessageLookupByLibrary.simpleMessage("Återförsäljare"),
        "revenue": MessageLookupByLibrary.simpleMessage("Intäkter"),
        "right": MessageLookupByLibrary.simpleMessage("Höger"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Försäljningsbelopp"),
        "sale": MessageLookupByLibrary.simpleMessage("Försäljning"),
        "saleAmount":
            MessageLookupByLibrary.simpleMessage("Försäljningsbelopp"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Försäljningsdetaljer"),
        "saleList": MessageLookupByLibrary.simpleMessage("Försäljningslista"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Försäljningspris"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Försäljningspris*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Försäljningsretur"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Försäljningstransaktion"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Försäljningstransaktioner (Offertförsäljningshistorik)"),
        "sales": MessageLookupByLibrary.simpleMessage("Försäljningar"),
        "salesList": MessageLookupByLibrary.simpleMessage("Försäljningslista"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Spara och publicera"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Spara och publicera"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Spara ändringar"),
        "search": MessageLookupByLibrary.simpleMessage("Sök......."),
        "searchAnyThing": MessageLookupByLibrary.simpleMessage("Sök något..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Sök via faktura...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Sök efter faktura eller namn"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Sök Efter Namn"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Sök efter namn eller telefonnummer..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Sök Serienummer"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Välj Parter"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Välj Produktmärke"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Välj Serienummer"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Välj Variationer:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Välj Garantitid"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Välj ditt språk"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Skicka meddelande"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serienummer"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serienummer"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Serviceavgift"),
        "setting": MessageLookupByLibrary.simpleMessage("Inställning"),
        "share": MessageLookupByLibrary.simpleMessage("Dela"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Frakt/Övrigt"),
        "shopName": MessageLookupByLibrary.simpleMessage("Butiksnamn"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Butikens öppningsbalans"),
        "show": MessageLookupByLibrary.simpleMessage("Visa >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Visa logotyp i fakturan?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Frakt/Tjänst"),
        "size": MessageLookupByLibrary.simpleMessage("Storlek"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistik"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Håll dig i framkant av teknikens framsteg utan extra kostnader. Vår Pos Saas POS Unlimited-uppgradering säkerställer att du alltid har de senaste verktygen och funktionerna vid fingertopparna och garanterar att ditt företag förblir toppmodernt."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Håll dig i framkant av teknikens framsteg utan extra kostnader. Vår Pos Sass POS Unlimited-uppgradering säkerställer att du alltid har de senaste verktygen och funktionerna vid fingertopparna och garanterar att ditt företag förblir toppmodernt."),
        "stock": MessageLookupByLibrary.simpleMessage("Lager"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Lagerinventering"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Lager Rapport"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Lagervärde"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Lagervärden"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Delsumma"),
        "submit": MessageLookupByLibrary.simpleMessage("Skicka"),
        "supplier": MessageLookupByLibrary.simpleMessage("Leverantörer"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Leverantörsförfall"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Leverantörsfaktura"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Leverantörslista"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT-kod"),
        "tSale": MessageLookupByLibrary.simpleMessage("Totala Försäljningar"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Ta ett körkort, nationellt ID-kort eller passfoto"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Användarvillkor"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Namnet säger allt. Med Pos Saas POS Obegränsad finns det ingen begränsning i användningen. Oavsett om du hanterar en handfull transaktioner eller upplever en rusning av kunder kan du arbeta med förtroende och veta att du inte är begränsad av gränser."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Den här kunden har ingen förfallen summa"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Denna kund har tidigare förfall"),
        "to": MessageLookupByLibrary.simpleMessage("Till"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Bäst säljande produkt"),
        "total": MessageLookupByLibrary.simpleMessage("totalt"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Totalbelopp"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Total rabatt"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Totalt förfall"),
        "totalDues":
            MessageLookupByLibrary.simpleMessage("Totalt belopp att betala"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Total utgift"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Totalt inkomst"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Totalt antal artiklar: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Total Förlust"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Totalt betalt"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Totalt att betala"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Total betalning ut"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Totalpris"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Totalt Antal Produkter"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Total Vinst"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Total inköp"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Totalt returbelopp"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Totalt returer"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total försäljning"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Total försäljning"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total moms"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Total betalning in"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaktion"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Transaktions-ID"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Transaktionsrapport"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Försök igen"),
        "type": MessageLookupByLibrary.simpleMessage("Typ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Obetald"),
        "unit": MessageLookupByLibrary.simpleMessage("Enhet"),
        "unitName": MessageLookupByLibrary.simpleMessage("Enhetens namn"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Enhetspris"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Obegränsad"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Obegränsade fakturor"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Obegränsad användning"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Lås upp hela potentialen av Pos Saas POS med personliga träningspass ledda av vårt expertteam. Från grunderna till avancerade tekniker ser vi till att du är väl förtrogen med att utnyttja varje aspekt av systemet för att optimera dina affärsprocesser."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Uppdatera nu"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Uppdatera din plan först.\\nFörsäljningsgränsen är överskriden."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Uppgradera i mobilappen"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Ladda upp en bild"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Ladda upp en fakturalogg"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Ladda upp dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Ladda upp fil"),
        "userName": MessageLookupByLibrary.simpleMessage("Användarnamn"),
        "userRole": MessageLookupByLibrary.simpleMessage("Användarroll"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Användarrollsnamn"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Användartitel"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("Moms/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifiera telefonnummer"),
        "view": MessageLookupByLibrary.simpleMessage("Visa"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Kund som kommer in"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garanti"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garantier"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Vi behöver registrera din telefon innan vi börjar!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Vi förstår vikten av sömlös verksamhet. Därför är vår dygnet runt-support tillgänglig för att hjälpa dig, vare sig det är en snabb förfrågan eller en omfattande fråga. Anslut med oss när som helst, var som helst via samtal eller WhatsApp för att uppleva enastående kundservice."),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Återförsäljare"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Partihandel"),
        "wight": MessageLookupByLibrary.simpleMessage("Vikt"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ja, returnera"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Du måste LOGGA IN på ditt konto igen."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Du måste verifiera din identitet innan du köper meddelanden"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Din lista över alla försäljningar"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Dina alla försäljningar"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Du använder"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage(
            "Dina försäljningar som förfaller"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Du måste verifiera din identitet innan du köper meddelanden"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Ditt paket"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Din Betalning är Avbruten"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Din Betalning lyckades"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Din betalning har avbrutits")
      };
}
