{"PosSaasLoginPanel": "<PERSON><PERSON> Sa<PERSON> Login panel", "posSaasSingUpPanel": "Pos Saas SingUp Panel", "addNewUser": "Add New User", "userRole": "User Role", "addNew": "Add New", "inventorySales": "Inventory Sales", "orders": "Orders", "revenue": "Revenue", "userName": "User Name", "noUserFound": "No User Found", "all": "All", "profileEdit": "Profile Edit", "practies": "Practise", "salesList": "Sales List", "enterPassword": "Enter Password", "noUserRoleFound": "No User Role Found", "addUserRole": "Add User Role", "UserTitle": "User Title", "enterUserTitle": "Enter user title", "userTitle": "User Title", "addSuccessful": "Added Successful", "youHaveToRelogin": "You have to RE-LOGIN on your account.", "ok": "Ok", "payCash": "Pay Cash", "freeLifeTimeUpdate": "Free Lifetime Update", "androidIOSAppSupport": "Android & iOS App Support", "premiumCustomerSupport": "Premium Customer Support", "customInvoiceBranding": "Custom Invoice Branding", "unlimitedUsage": "Unlimited Usage", "freeDataBackup": "Free Data Backup", "stayAtTheForFront": "Stay at the forefront of technological advancements without any extra costs. Our Pos Saas POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge.", "weUnderStand": " We understand the importance of seamless operations. That's why our round-the-clock support is available to assist you, whether it's a quick query or a comprehensive concern. Connect with us anytime, anywhere via call or WhatsApp to experience unrivaled customer service.", "unlockTheFull": "Unlock the full potential of Pos Saas POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you're well-versed in utilizing every facet of the system to optimize your business processes.", "makeALastingImpression": "Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty.", "theNameSysIt": "The name says it all. With Pos Saas POS Unlimited, there's no cap on your usage. Whether you're processing a handful of transactions or experiencing a rush of customers, you can operate with confidence, knowing you're not constrained by limits", "safegurardYourBusinessDate": "Safeguard your business data effortlessly. Our Pos Saas POS Unlimited Upgrade includes free data backup, ensuring your valuable information is protected against any unforeseen events. Focus on what truly matters - your business growth.", "buy": "Buy", "bankInformation": "Bank Information", "bankName": "Bank Name", "branchName": "Branch Name", "accountName": "Account Name", "accountNumber": "Account Number", "bankAccountingCurrecny": "Bank Account <PERSON><PERSON><PERSON>cy", "swiftCode": "SWIFT Code", "enterTransactionId": "Enter Transaction Id", "uploadDocument": "Upload Document", "uploadFile": "Upload File", "aboutApp": "About App", "termsOfUse": "Terms of use", "privacyPolicy": "Privacy Policy", "userRoleName": "User Role Name", "enterUserRoleName": "Enter User Role Name", "yourPackage": "Your Package", "freePlan": "Free Plan", "yourAreUsing": "You are using", "freePackage": "Free Package", "premiumPlan": "Premium Plan", "packageFeature": "Package Feature", "remaining": "Remaining: ", "unlimited": "Unlimited", "forUnlimitedUses": "For Unlimited Uses", "updateNow": "Update Now", "purchasePremiumPlan": "Purchase Premium Plan", "stayAtTheForeFrontOfTechnological": "Stay at the forefront of technological advancements without any extra costs. Our Pos Sass POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge.", "buyPremiumPlan": "Buy Premium Plan", "mobilePlusDesktop": "Mobile App\n+\nDesktop", "transactionId": "Transaction Id", "productStock": "Product Stock", "pleaseEnterProductStock": "Please enter product stock", "increaseStock": "Increase Stock", "areYouWantToDeleteThisProduct": "Are you want to delete this product", "noConnection": "No Connection", "pleaseCheckYourInternetConnectivity": "Please Check Your Internet Connectivity", "tryAgain": "Try Again", "currency": "<PERSON><PERSON><PERSON><PERSON>", "businessCategory": "Business Category", "companyName": "Company Name", "enterYourCompanyName": "Enter Your Company Name", "phoneNumber": "Phone Number", "enterYourPhoneNumber": "Enter your phone number", "shopOpeningBalance": "Shop Opening Balance", "enterYOurAmount": "Enter your amount", "continu": "Continue", "resetYourPassword": "Reset Your Password", "email": "Email", "enterYourEmailAddress": "Enter your email address", "pleaseDownloadOurMobileApp": "Please download our mobile app and subscribe to a package to use the desktop version", "mobiPosLoginPanel": "Pos Saas Login Panel", "enterYourPassword": "Enter Your Password", "login": "<PERSON><PERSON>", "password": "Password", "forgotPassword": "Forgot Password?", "registration": "Register", "editYourProfile": "Edit your profile", "uploadAImage": "Upload an image", "orDragAndDropPng": " or drag & drop PNG, JPG", "comapnyName": "Company Name", "enterYourCompanyNames": "Enter your Company Name", "address": "Address", "enterYourAddress": "Enter Your Address", "mobiPosSignUpPane": "Pos Saas Signup Panel", "confirmPassword": "Confirm password", "enterYourPasswordAgain": "Enter your password again", "alreadyHaveAnAccounts": "Already have an account's?", "choseAplan": "Chose a plan", "allBasicFeatures": "All Basic Features", "unlimitedInvoice": "Unlimited Invoices", "getStarted": "Get Started", "currentPlan": "Current plan", "selectYourLanguage": "Select your language", "shopName": "Shop Name", "enterYourShopName": "Enter Your Shop Name", "phoneVerification": "Phone Verification", "weNeedToRegisterYourPhone": "We need to register your phone before getting started!", "verifyPhoneNumber": "Verify Phone Number", "customerName": "Customer Name", "enterCustomerName": "Enter Customer Name", "openingBalance": "Opening Balance", "enterOpeningBalance": "Enter Opening Balance", "type": "Type", "cancel": "Cancel", "saveAndPublish": "Save & Publish", "customerList": "Customer List", "searchByNameOrPhone": "Search by Name or Phone...", "addCustomer": "Add Customer", "partyName": "Party Name", "partyType": "Party Type", "phone": "Phone", "due": "Due", "edit": "Edit", "delete": "Delete", "areYouWantToDeleteThisCustomer": "Are you want to delete this Customer?", "thisCustomerHavepreviousDue": "This customer have previous due", "noCustomerFound": "No Customer Found", "totalDue": "Total Due", "customers": "Customers", "supplier": "Suppliers", "collectDue": "Collect Due >", "noDueTransantionFound": "No Due Transaction Found", "createPayment": "Create Payment", "grandTotal": "Grand Total", "payingAmount": "Paying Amount", "enterPaidAmount": "Enter paid amounts", "changeAmount": "Change Amount", "dueAmount": "Due Amount", "paymentType": "Payment Type", "submit": "Submit", "enterExpanseCategory": "Enter Expense Category", "pleaseEnterValidData": "Please enter valid data", "categoryName": "Category Name", "entercategoryName": "Enter Category Name", "description": "Description", "addDescription": "Add description....", "expensecategoryList": "Expense CategoryList", "searchByInvoice": "Search by invoice....", "addCategory": "Add Category", "action": "Action", "noExpenseCategoryFound": "No Expense Category Found", "expenseDetails": "Expense Details", "date": "Date", "name": "Name", "category": "Category", "referenceNo": "Reference No.", "amount": "Amount", "note": "Note", "nam": "Name*", "income": "Income", "addUpdateExpenseList": "Add/Update Expense List", "expenseDate": "Expense Date", "enterExpenseDate": "Enter Expense Date", "expenseFor": "Expense For", "enterName": "Enter Name", "referenceNumber": "Reference Number", "enterReferenceNumber": "Enter Reference Number", "enterNote": "Enter Note", "between": "Between", "to": "To", "totalExpense": "Total Expense", "totalSales": "Total Sales", "purchase": "Purchase", "newCustomers": "New Customers", "dailySales": "Daily Sales", "dailyCollection": "Daily Collection", "instantPrivacy": "Instant Privacy", "stockInventory": "Stock Inventory", "stockValue": "Stock Value", "lowStocks": "Low Stocks", "other": "Other", "otherIncome": "Other Income", "MOBIPOS": "<PERSON><PERSON>", "newCusotmers": "New Customers", "enterIncomeCategory": "Enter Income Category", "pleaseentervaliddata": "Please enter valid data", "saveAndPublished": "Save & Publish", "incomeCategoryList": "Income Category List", "noIncomeCategoryFound": "No Income Category Found", "incomeDetails": "Income Details", "paymentTypes": "Payment Type", "totalIncome": "Total Income", "incomeList": "Income List", "incomeCategory": "income Category", "newIncome": "New Income", "createdBy": "Created By", "view": "View", "noIncomeFound": "No Income Found", "addUpdateIncomeList": "Add/Update Income List", "incomeDate": "Income Date", "enterIncomeDate": "Enter Income Date", "incomeFor": "Income For", "enterNames": "Enter Name", "enterAmount": "Enter Amount", "printInvoice": "Print Invoice", "moneyReciept": "Money Receipt", "billTo": "Bill to:", "invoiceNo": "Invoice No.", "totalDues": "Total Dues", "paidAmount": "Paid amount", "remainingDue": "Remaining Due", "deliveryCharge": "Delivery Charge", "INVOICE": "INVOICE", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "subTotal": "Sub Total", "totalVat": "Total Vat", "totalDiscount": "Total Discount", "payable": "Payable", "paid": "Paid", "serviceCharge": "Service Charge", "totalSale": "Total Sale", "totalPurchase": "Total Purchase", "recivedAmount": "Received Amount", "customerDue": "Customer Due", "supplierDue": "Supplier Due", "selectParties": "Select Parties", "details": "Details >", "show": "Show >", "noTransactionFound": "No Transaction Found", "ledgeDetails": "Ledger Details", "status": "Status", "itemName": "ItemName", "purchasePrice": "Purchase Price", "salePrice": "Sale Price", "profit": "Profit", "loss": "Loss", "total": "total", "totalProfit": "Total Profit", "totalLoss": "Total Loss", "unPaid": "Unpaid", "lossOrProfit": "Loss/Profit", "saleAmount": "Sale Amount", "profitPlus": "Profit(+)", "profitMinus": "Profit(-)", "yourPaymentIsCancelled": "Your Payment is canceled", "yourPaymentIsSuccessfully": "Your Payment is successfully", "hold": "Hold", "holdNumber": "Hold Number", "selectSerialNumber": "Select Serial Number", "serialNumber": "Serial Number", "searchSerialNumber": "Search Serial Number", "noSerialNumberFound": "No Serial Number Found", "nameCodeOrCateogry": "Name or Code or Category", "vatOrgst": "Vat/GST", "discount": "Discount", "areYouWantToCreateThisQuation": "Are you want to create this Quotation?", "updateYourPlanFirst": "Update your plan first\\nSale Limit is over.", "quotation": "Quotation", "addProduct": "Add Product", "totalProduct": "Total Product", "shpingOrServices": "Shipping/Service", "addItemCategory": "Add Item Category", "selectVariations": "Select Variations:", "size": "Size", "color": "Color", "wight": "Weight", "capacity": "Capacity", "warranty": "Warranty", "addBrand": "Add Brand", "brandName": "Brand Name", "enterBrandName": "Enter Brand Name", "addUnit": "Add Unit", "unitName": "Unit Name", "enterUnitName": "Enter Unit Name", "productNam": "Product Name*", "enterProductName": "Enter Product Name", "productType": "ProductType", "enterProductType": "Enter Product Type", "productWaranty": "Product Warranty", "enterWarranty": "Enter Warranty", "warrantys": "Warranty", "selectWarrantyTime": "Select Warranty ATime", "brand": "Brand", "selectProductBrand": "Select Product Brand", "productCod": "Product Code*", "enterProductCode": "Enter Product Code", "enterProductQuantity": "Enter Product Quantity", "Quantity": "Quantity*", "productUnit": "Product Unit", "enterPurchasePrice": "Enter Purchase Price", "salePrices": "Sale Price*", "dealerPrice": "Dealer Price", "enterDealePrice": "Enter Dealer Price", "wholeSaleprice": "WholeSale Price", "enterPrice": "Enter Price", "manufacturer": "Manufacturer", "enterManufacturerName": "Enter Manufacturer Name", "serialNumbers": "Serial Number", "enterSerialNumber": "Enter Serial Number", "nosSerialNumberFound": "No Serial Number Found", "productList": "Product List", "searchByName": "Search By Name", "retailer": "Retailer", "dealer": "Dealer", "wholesale": "Wholesale", "expense": "Expense", "totalPayable": "Total Payable", "totalAmount": "Total Amount", "searchByInvoiceOrName": "Search by invoice or name", "invoice": "Invoice", "lossminus": "Loss(-)", "yourPaymentIscancelled": "Your Payment is canceled", "previousDue": "Previous Due:", "calculator": "Calculator:", "dashBoard": "DashBoard", "price": "Price", "create": "Create", "payment": "Payment", "enterPayingAmount": "Enter Paying Amount", "enterCategoryName": "Enter Category Name", "productSize": "Product Size", "enterProductSize": "Enter Product Size", "productColor": "Product Color", "enterProductColor": "Enter Product Color", "productWeight": "Product weight", "enterProductWeight": "Enter Product Weight", "productcapacity": "Product Capacity", "enterProductCapacity": "Enter Product Capacity", "enterSalePrice": "Enter Sale Price", "add": "Add", "productCategory": "Product Category", "enterProductUnit": "Enter Product Unit", "productName": "Product Name", "noProductFound": "No Product Found", "addingSerialNumber": "Adding Serial Number?", "unit": "Unit", "editOrAddSerial": "Edit/Add Serial:", "enterWholeSalePrice": "Enter Wholesale Price", "invoiceCo": "Invoice:", "categories": "Categories", "purchaseList": "Purchase List", "print": "Print", "noPurchaseTransactionFound": "No purchase transaction found", "quotationList": "Quotation List", "areYouWantToDeleteThisQuotion": "Are you want to delete this Quotation?", "convertToSale": "Convert To Sale", "noQuotionFound": "No Quotation Found", "stockReport": "Stock Report", "PRODUCTNAME": "PRODUCT NAME", "CATEGORY": "CATEGORY", "PRICE": "PRICE", "QTY": "QTY", "STATUS": "STATUS", "TOTALVALUE": "TOTAL VALUE", "noReportFound": "NO Report Found", "remainingBalance": "Remaining Balance", "totalpaymentIn": "Total Payment In", "totalPaymentOut": "Total Payment Out", "dailyTransaction": "Daily Transaction", "paymentIn": "Payment In", "paymentOut": "Payment Out", "balance": "Balance", "totalPaid": "Total Paid", "dueTransaction": "Due Transaction", "downloadPDF": "Download PDF", "customerType": "Customer Type", "pleaseAddCustomer": "Please Add Customer", "purchaseTransaction": "Purchase Transaction", "printPdf": "Print PDF", "saleTransactionQuatationHistory": "Sale Transactions (Quotation Sale History)", "ADDSALE": "ADD SALE", "search": "Search.......", "transactionReport": "Transaction Report", "saleTransaction": "Sale Transaction", "totalReturns": "Total Returns", "totalReturnAmount": "Total Return Amount", "saleReturn": "Sale Return", "noSaleTransaactionFound": "No Sale Transaction Found", "saleList": "Sale List", "reports": "Reports", "areYouWantToReturnThisSale": "Are you want to return this sale?", "no": "No", "yesReturn": "Yes Return", "setting": "Setting", "uploadAnInvoiceLogo": "Upload An Invoice Logo", "showLogoInInvoice": "Show Logo in Invoice?", "logoPositionInInvoice": "Logo position in invoice?", "left": "Left", "right": "Right", "companyAddress": "Company Address", "enterYourCompanyAddress": "Enter Your Company Address", "companyPhoneNumber": "Company phone number", "companyEmailAddress": "Company email address", "enterCompanyPhoneNumber": "Enter company phone number", "enterCompanyEmailAddress": "Enter company email address", "companyWebsiteUrl": "Company Website Url", "enterCompanyWebsiteUrl": "Enter company website url", "companyDescription": "Company Description", "enterCompanyDesciption": "Enter Company Description", "saveChanges": "Save Changes", "kycVerification": "KYC Verification", "identityVerify": "Identity Verify", "yourNeedToIdentityVerify": "You need to identity verify before buying messages", "govermentId": "Government Id", "takeADriveLisense": "Take a driver's license, national identity card or passport photo", "addDucument": "Add Documents", "youNeedToIdentityVerifySms": "You need to identity verify before buying messages", "wholeSeller": "Wholesaler", "enterSmsContent": "Enter message Content", "sendMessage": "Send message", "buySms": "Buy sms", "supplierList": "Supplier List", "addSupplier": "Add Supplier", "noSupplierFound": "No Supplier Found", "checkWarranty": "Check Warranty", "customerInvoices": "Customer Invoices", "supplierInvoice": "Supplier Invoice", "addItem": "Add Item", "noInvoiceFound": "No Invoice Found", "stock": "Stock", "enterStockAmount": "Enter Stock Amount", "discountPrice": "Discount Price", "enterDiscountPrice": "Enter Discount Price", "dateTime": "Date Time", "walkInCustomer": "Walk in Customer", "saleDetails": "Sale Details", "customerWalkIncostomer": "Customer: Walk-in Customer", "item": "<PERSON><PERSON>", "camera": "Camera", "totalItem2": "Total Item : 2", "shipingOrOther": "Shipping/Other", "yourDueSales": "Your Due Sales", "yourAllSales": "Your All Sales", "invoiceHint": "Invoice NO..", "customer": "Customer", "dueAmountWillShowHere": "Due amount will show here if available", "thisCustmerHasNoDue": "This customer has no due", "pleaseSelectACustomer": "Please Select A Customer", "pleaseAddASale": "Please Add A Sale", "yourAllSaleList": "Your all sale list", "changeableAmount": "Changeable Amount", "sales": "Sales", "dueList": "Due List", "ledger": "Ledger", "transaction": "Transaction", "subciption": "Subscription", "upgradeOnMobileApp": "Upgrade On Mobile App", "POSSale": "POS Sale", "searchAnyThing": "Search Anythings...", "sale": "Sale", "logOut": "Log out", "cashAndBank": "Cash & Bank", "cashInHand": "Cash In Hand", "bankAccounts": "Bank Accounts", "creativeHub": "Creative Hub", "openCheques": "Open Cheques", "loanAccounts": "Loan Accounts", "share": "Share", "preview": "Preview", "dueCollection": "Due Collection", "customerOfTheMonth": "Customer of the month", "topSellingProduct": "Top Selling Product", "statistic": "Statistic", "stockValues": "Stock Value", "lowStock": "Low Stock", "fivePurchase": "Top 5 purchasing product of the month", "recentSale": "Recent Sales", "tSale": "Total Sales", "sAmount": "Sale Amount", "expenses": "Expense", "inc": "Income", "prof": "Profile"}