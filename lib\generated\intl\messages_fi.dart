// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("LISÄÄ MYYNTI"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("LASKU"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Kassamyynti"),
        "PRICE": MessageLookupByLibrary.simpleMessage("HINTA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("TUOTTEEN NIMI"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas -kirjautumispaneeli"),
        "QTY": MessageLookupByLibrary.simpleMessage("MÄÄRÄ"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Määrä*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("TILA"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("KOKONAISARVO"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Käyttäjänimike"),
        "aboutApp":
            MessageLookupByLibrary.simpleMessage("Tietoja sovelluksesta"),
        "accountName": MessageLookupByLibrary.simpleMessage("Tilin nimi"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Tilinumero"),
        "action": MessageLookupByLibrary.simpleMessage("Toiminta"),
        "add": MessageLookupByLibrary.simpleMessage("Lisää"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Lisää brändi"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Lisää kategoria"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Lisää asiakas"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Lisää kuvaus..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Lisää asiakirja"),
        "addItem": MessageLookupByLibrary.simpleMessage("Lisää tuote"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Lisää tuotekategoria"),
        "addNew": MessageLookupByLibrary.simpleMessage("Lisää uusi"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Lisää uusi käyttäjä"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Lisää tuote"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Lisäys onnistui"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Lisää toimittaja"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Lisää yksikkö"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("Lisää/päivitä menolista"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("Lisää/Päivitä tulolista"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Lisää käyttäjärooli"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Lisätäänkö sarjanumero?"),
        "address": MessageLookupByLibrary.simpleMessage("Osoite"),
        "all": MessageLookupByLibrary.simpleMessage("Kaikki"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Kaikki perusominaisuudet"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Onko sinulla jo tili?"),
        "amount": MessageLookupByLibrary.simpleMessage("Summa"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Android- ja iOS-sovellustuki"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Haluatko luoda tämän tarjouksen?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Haluatko poistaa tämän asiakkaan?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Haluatko poistaa tämän tuotteen"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Haluatko poistaa tämän tarjouksen?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Haluatko palauttaa tämän myynnin?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Pankkitilin valuutta"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Pankkitilit"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("Pankkitiedot"),
        "bankName": MessageLookupByLibrary.simpleMessage("Pankin nimi"),
        "between": MessageLookupByLibrary.simpleMessage("Välillä"),
        "billTo": MessageLookupByLibrary.simpleMessage("Lasku vastaanottaja:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Sivuliikkeen nimi"),
        "brand": MessageLookupByLibrary.simpleMessage("Brändi"),
        "brandName": MessageLookupByLibrary.simpleMessage("Brändinimi"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Yrityksen toimiala"),
        "buy": MessageLookupByLibrary.simpleMessage("Osta"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Osta Premium-suunnitelma"),
        "buySms": MessageLookupByLibrary.simpleMessage("Osta viestejä"),
        "calculator": MessageLookupByLibrary.simpleMessage("Laskin:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Peruuta"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapasiteetti"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("Käteinen ja pankki"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Käteinen kädessä"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategoriat"),
        "category": MessageLookupByLibrary.simpleMessage("Kategoria"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Kategoria"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Vaihtoraha"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Muutettava määrä"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("Tarkista takuu"),
        "choseAplan":
            MessageLookupByLibrary.simpleMessage("Valitse suunnitelma"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Kerää maksettavat >"),
        "color": MessageLookupByLibrary.simpleMessage("Väri"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Yrityksen nimi"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Yrityksen osoite"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Yrityksen kuvaus"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Yrityksen sähköpostiosoite"),
        "companyName": MessageLookupByLibrary.simpleMessage("Yrityksen nimi"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Yrityksen puhelinnumero"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Yrityksen verkkosivuston URL-osoite"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Vahvista salasana"),
        "continu": MessageLookupByLibrary.simpleMessage("Jatka"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Muunna myynniksi"),
        "create": MessageLookupByLibrary.simpleMessage("Luo"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Luo maksu"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Luonut"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Luovuuden keskus"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuutta"),
        "currentPlan":
            MessageLookupByLibrary.simpleMessage("Nykyinen suunnitelma"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Mukautettu laskun brändäys"),
        "customer": MessageLookupByLibrary.simpleMessage("Asiakas"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Asiakkaan velka"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Asiakaslaskut"),
        "customerList": MessageLookupByLibrary.simpleMessage("Asiakasluettelo"),
        "customerName": MessageLookupByLibrary.simpleMessage("Asiakkaan nimi"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kuukauden asiakas"),
        "customerType": MessageLookupByLibrary.simpleMessage("Asiakastyyppi"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Asiakas: Kävelymyyjä"),
        "customers": MessageLookupByLibrary.simpleMessage("Asiakkaat"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Päivittäiset kerätyt"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Päivittäiset myynnit"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Päivittäiset tapahtumat"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Hallintapaneeli"),
        "date": MessageLookupByLibrary.simpleMessage("Päivämäärä"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Päivämäärä ja aika"),
        "dealer": MessageLookupByLibrary.simpleMessage("Huolto"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Huoltohinta"),
        "delete": MessageLookupByLibrary.simpleMessage("Poista"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("Toimitusmaksu"),
        "description": MessageLookupByLibrary.simpleMessage("Kuvaus"),
        "details": MessageLookupByLibrary.simpleMessage("Tiedot >"),
        "discount": MessageLookupByLibrary.simpleMessage("Alennus"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Alennettu hinta"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Lataa PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Erääntynyt"),
        "dueAmount":
            MessageLookupByLibrary.simpleMessage("Maksamatta oleva summa"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Mahdollinen velka näkyy tässä"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("Velan perintä"),
        "dueList": MessageLookupByLibrary.simpleMessage("Velkalista"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Maksamattomat tapahtumat"),
        "edit": MessageLookupByLibrary.simpleMessage("Muokkaa"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Muokkaa/Lisää sarjanumero:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Muokkaa profiiliasi"),
        "email": MessageLookupByLibrary.simpleMessage("Sähköposti"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Syötä summa"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Syötä brändinimi"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Syötä kategorian nimi"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Syötä yrityksen kuvaus"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Syötä yrityksen sähköpostiosoite"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Syötä yrityksen puhelinnumero"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Syötä yrityksen verkkosivuston URL-osoite"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Syötä asiakkaan nimi"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Syötä huoltohinta"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Syötä alennettu hinta"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Syötä menokategoria"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Syötä menopäivämäärä"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Syötä tulokategoria"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Syötä tulopäivä"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Syötä valmistajan nimi"),
        "enterName": MessageLookupByLibrary.simpleMessage("Syötä nimi"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Syötä nimi"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Syötä huomautus"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Syötä avaussaldo"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Syötä maksetut summat"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Syötä salasana"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Syötä maksettava määrä"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Syötä hinta"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Syötä tuotteen kapasiteetti"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Syötä tuotenumero"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Syötä tuotteen väri"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Syötä tuotteen nimi"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Syötä tuotemäärä"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Syötä tuotekoko"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Syötä tuotetyyppi"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Syötä tuotteen yksikkö"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Syötä tuotteen paino"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Syötä ostohinta"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Syötä viitenumero"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Syötä myyntihinta"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Syötä sarjanumero"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Syötä viestisisältö"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Syötä varastomäärä"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Syötä tapahtumatunnus"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Syötä yksikkönimi"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Syötä käyttäjäroolin nimi"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Syötä käyttäjänimike"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("Syötä takuu"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Syötä tukkumyyntihinta"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Syötä summasi"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Syötä osoitteesi"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Syötä yrityksen osoite"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Syötä yrityksen nimi"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Syötä yrityksesi nimi"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Syötä sähköpostiosoitteesi"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Syötä salasanasi"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Syötä salasanasi uudelleen"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Syötä puhelinnumerosi"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Syötä myymälän nimi"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Syötä kategorian nimi"),
        "expense": MessageLookupByLibrary.simpleMessage("Kulu"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Menon päivämäärä"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("Menon tiedot"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Meno"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Menokategorialista"),
        "expenses": MessageLookupByLibrary.simpleMessage("Menot"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Kuukauden viisi myydyintä tuotetta"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Rajoittamattomaan käyttöön"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Unohditko salasanasi?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Ilmainen tietojen varmuuskopiointi"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Ilmainen elinikäinen päivitys"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Ilmainen paketti"),
        "freePlan":
            MessageLookupByLibrary.simpleMessage("Ilmainen suunnitelma"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Aloita"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Virallinen henkilötodistus"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Yhteensä"),
        "hold": MessageLookupByLibrary.simpleMessage("Pidä"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Pidä numero"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Tunnistautuminen"),
        "inc": MessageLookupByLibrary.simpleMessage("Tulot"),
        "income": MessageLookupByLibrary.simpleMessage("Tulot"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("Tulolaji"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Tulolajiluettelo"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Tulopäivä"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("Tulon tiedot"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Tulo"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Tulot"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Lisää tuotetilannetta"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Hetkellinen yksityisyys"),
        "invoice": MessageLookupByLibrary.simpleMessage("Lasku"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Lasku:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Laskun numero..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Laskun numero"),
        "item": MessageLookupByLibrary.simpleMessage("Tuote"),
        "itemName": MessageLookupByLibrary.simpleMessage("Tavaran nimi"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC-tarkistus"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Kirjanpitotiedot"),
        "ledger": MessageLookupByLibrary.simpleMessage("Päiväkirja"),
        "left": MessageLookupByLibrary.simpleMessage("Vasen"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Lainatilit"),
        "logOut": MessageLookupByLibrary.simpleMessage("Kirjaudu ulos"),
        "login": MessageLookupByLibrary.simpleMessage("Kirjaudu sisään"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logon sijainti laskussa?"),
        "loss": MessageLookupByLibrary.simpleMessage("Tappiot"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Tulot/Tappiot"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Tappio(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Vähäinen Varasto"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Alhaiset varastot"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Tee kestävä vaikutus asiakkaisiisi brändättyjen laskujen avulla. Rajattoman päivityksemme avulla voit mukauttaa laskujasi, lisätä ammattimaisen kosketuksen, joka vahvistaa brändisi identiteettiä ja edistää asiakasuskollisuutta."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Valmistaja"),
        "mobiPosLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas -kirjautumispaneeli"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Pos Saas -rekisteröintipaneeli"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Mobiilisovellus\n+\nPöytäkone"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Rahalasku"),
        "nam": MessageLookupByLibrary.simpleMessage("Nimi*"),
        "name": MessageLookupByLibrary.simpleMessage("Nimi"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nimi, koodi tai luokka"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Uudet asiakkaat"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Uudet asiakkaat"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Uusi tulo"),
        "no": MessageLookupByLibrary.simpleMessage("Ei"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Ei yhteyttä"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Asiakasta ei löytynyt"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Ei maksettavaa tapahtumaa löytynyt"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("Menokategoriaa ei löytynyt"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("Tulolajia ei löytynyt"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Tuloa ei löytynyt"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Laskuja ei löytynyt"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Tuotetta ei löytynyt"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("Ostotapahtumia ei löytynyt"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Tarjouksia ei löytynyt"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Selvitystä ei löytynyt"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Myyntitapahtumia ei löytynyt"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Sarjanumeroa ei löytynyt"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Toimittajia ei löytynyt"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Ei tapahtumia löytynyt"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Käyttäjää ei löytynyt"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Käyttäjäroolia ei löytynyt"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Sarjanumeroa ei löytynyt"),
        "note": MessageLookupByLibrary.simpleMessage("Huomautus"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Avoimet shekit"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("Avaussaldo"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("tai vedä ja pudota PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Tilaukset"),
        "other": MessageLookupByLibrary.simpleMessage("Muu"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Muut tulot"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Pakettiominaisuus"),
        "paid": MessageLookupByLibrary.simpleMessage("Maksettu"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Maksettu summa"),
        "partyName": MessageLookupByLibrary.simpleMessage("Osapuolen nimi"),
        "partyType": MessageLookupByLibrary.simpleMessage("Osapuolen tyyppi"),
        "password": MessageLookupByLibrary.simpleMessage("Salasana"),
        "payCash": MessageLookupByLibrary.simpleMessage("Maksa käteisellä"),
        "payable": MessageLookupByLibrary.simpleMessage("Maksattava"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Maksettava summa"),
        "payment": MessageLookupByLibrary.simpleMessage("Maksu"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Maksu sisään"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Maksu ulos"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Maksutapa"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Maksutapa"),
        "phone": MessageLookupByLibrary.simpleMessage("Puhelin"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Puhelinnumero"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Puhelinvarmennus"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage("Lisää myynti"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Lisää asiakas, ole hyvä"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage("Tarkista Internet-yhteytesi"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Lataa mobiilisovellus ja tilaa paketti, jotta voit käyttää työpöytäversiota"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Syötä tuotetilanne"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Syötä kelvollinen data"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Valitse asiakas"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Syötä kelvollinen data"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Pos Saas -rekisteröitymispaneeli"),
        "practies": MessageLookupByLibrary.simpleMessage("Harjoittelu"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium-asiakastuki"),
        "premiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium-suunnitelma"),
        "preview": MessageLookupByLibrary.simpleMessage("Esikatselu"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Edellinen jäännös:"),
        "price": MessageLookupByLibrary.simpleMessage("Hinta"),
        "print": MessageLookupByLibrary.simpleMessage("Tulosta"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Tulosta lasku"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Tulosta PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Tietosuojakäytäntö"),
        "product": MessageLookupByLibrary.simpleMessage("Tuote"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Tuotekategoria"),
        "productCod": MessageLookupByLibrary.simpleMessage("Tuotenumero*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Tuotteen väri"),
        "productList": MessageLookupByLibrary.simpleMessage("Tuotelista"),
        "productNam": MessageLookupByLibrary.simpleMessage("Tuotteen nimi*"),
        "productName": MessageLookupByLibrary.simpleMessage("Tuotteen nimi"),
        "productSize": MessageLookupByLibrary.simpleMessage("Tuotteen koko"),
        "productStock": MessageLookupByLibrary.simpleMessage("Tuotetilanne"),
        "productType": MessageLookupByLibrary.simpleMessage("Tuotetyyppi"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Tuoteyksikkö"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Tuotteen takuu"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Tuotteen paino"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Tuotteen kapasiteetti"),
        "prof": MessageLookupByLibrary.simpleMessage("Profiili"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Profiilin muokkaus"),
        "profit": MessageLookupByLibrary.simpleMessage("Tulot"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Tulot(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Tulot(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Osto"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Ostoslista"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Osta Premium-suunnitelma"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Ostohinta"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Ostotapahtuma"),
        "quantity": MessageLookupByLibrary.simpleMessage("Määrä"),
        "quotation": MessageLookupByLibrary.simpleMessage("Tarjous"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Tarjouslista"),
        "recentSale":
            MessageLookupByLibrary.simpleMessage("Viimeisimmät Myynnit"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Vastaanotettu summa"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Viitenumero"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("Viitenumero"),
        "registration": MessageLookupByLibrary.simpleMessage("Rekisteröinti"),
        "remaining": MessageLookupByLibrary.simpleMessage("Jäljellä: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Jäljellä oleva saldo"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Jäljellä oleva summa"),
        "reports": MessageLookupByLibrary.simpleMessage("Raportit"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Nollaa salasanasi"),
        "retailer": MessageLookupByLibrary.simpleMessage("Vähittäiskauppias"),
        "revenue": MessageLookupByLibrary.simpleMessage("Tulot"),
        "right": MessageLookupByLibrary.simpleMessage("Oikea"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Myyntimäärä"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Suojaa yritystietosi vaivattomasti. Pos Saas POS Unlimited -päivityksemme sisältää ilmaisen tietojen varmuuskopioinnin, joka suojaa arvokkaat tiedot ennakoimattomilta tapahtumilta. Keskitä huomiosi siihen, mikä todella merkitsee - yrityksesi kasvuun."),
        "sale": MessageLookupByLibrary.simpleMessage("Myynti"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Myyntisumma"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Myyntitiedot"),
        "saleList": MessageLookupByLibrary.simpleMessage("Myyntilista"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Myyntihinta"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Myyntihinta*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Myynnin palautus"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Myyntitapahtuma"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Myyntitapahtumat (Tarjousmyyntihistoria)"),
        "sales": MessageLookupByLibrary.simpleMessage("Myynti"),
        "salesList": MessageLookupByLibrary.simpleMessage("Myyntilista"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Tallenna ja julkaise"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Tallenna & Julkaise"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Tallenna muutokset"),
        "search": MessageLookupByLibrary.simpleMessage("Haku......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Etsi mitä tahansa..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Hae laskusta..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Hae laskun numeron tai nimen perusteella"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Hae nimellä"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Hae nimellä tai puhelimella..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Hae sarjanumero"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Valitse osapuolet"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Valitse tuotemerkki"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Valitse sarjanumero"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Valitse vaihtelut:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Valitse takuuaika"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Valitse kieli"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Lähetä viesti"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Sarjanumero"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Sarjanumero"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Palvelumaksu"),
        "setting": MessageLookupByLibrary.simpleMessage("Asetukset"),
        "share": MessageLookupByLibrary.simpleMessage("Jaa"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Toimitus/Muu"),
        "shopName": MessageLookupByLibrary.simpleMessage("Myymälän nimi"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Myymälän avaussaldon"),
        "show": MessageLookupByLibrary.simpleMessage("Näytä >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Näytä logo laskussa?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Toimitus/Palvelut"),
        "size": MessageLookupByLibrary.simpleMessage("Koko"),
        "statistic": MessageLookupByLibrary.simpleMessage("Tilasto"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Pysy teknologian huipulla ilman lisäkustannuksia. Pos Saas POS Unlimited -päivityksemme varmistaa, että sinulla on aina uusimmat työkalut ja ominaisuudet käytettävissäsi, jotta yrityksesi pysyy ajan tasalla."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Pysy teknologian kärjessä ilman lisäkustannuksia. Pos Sass POS Unlimited -päivityksemme varmistaa, että sinulla on aina uusimmat työkalut ja ominaisuudet käytettävissäsi, jotta yrityksesi pysyy ajan tasalla."),
        "stock": MessageLookupByLibrary.simpleMessage("Varasto"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Varastoinventaario"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Varastoselvitys"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Varaston arvo"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Varaston Arvot"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Välisumma"),
        "subciption": MessageLookupByLibrary.simpleMessage("Tilaus"),
        "submit": MessageLookupByLibrary.simpleMessage("Lähetä"),
        "supplier": MessageLookupByLibrary.simpleMessage("Toimittajat"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Toimittajan velka"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Toimittajan lasku"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Toimittajaluettelo"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT-koodi"),
        "tSale": MessageLookupByLibrary.simpleMessage("Kokonaismyynnit"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Ota ajokortti, henkilökortti tai passikuva"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Käyttöehdot"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Nimi kertoo sen kaiken. Pos Saas POS Unlimited -versiossa ei ole rajoituksia käytöllesi. Olitpa sitten käsittelemässä muutamia tapahtumia tai asiakasmäärän kasvaessa, voit toimia varmuudella tietäen, ettet ole rajoitettu rajoituksilla."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Tällä asiakkaalla ei ole velkaa"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Tällä asiakkaalla on aikaisempi erääntynyt saldo"),
        "to": MessageLookupByLibrary.simpleMessage("Saakka"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Parhaiten myyvä tuote"),
        "total": MessageLookupByLibrary.simpleMessage("Yhteensä"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Kokonaismäärä"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Yhteensä alennus"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Yhteensä maksettava"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Yhteensä lasku"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Yhteensä meno"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Yhteensä tulot"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Koko tuote: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Yhteensä tappiot"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Kokonaismaksut"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Kokonaisvelka"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Kokonaismaksut ulos"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Yhteensä"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Yhteensä tuote"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Yhteensä tulot"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Yhteensä ostot"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Palautusmäärä yhteensä"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Palautukset yhteensä"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Yhteensä myynti"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Yhteensä myynti"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Yhteensä ALV"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Kokonaismaksut sisään"),
        "transaction": MessageLookupByLibrary.simpleMessage("Tapahtuma"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Tapahtumatunnus"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Tapahtumaraportti"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Yritä uudelleen"),
        "type": MessageLookupByLibrary.simpleMessage("Tyyppi"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Epämaksettu"),
        "unit": MessageLookupByLibrary.simpleMessage("Yksikkö"),
        "unitName": MessageLookupByLibrary.simpleMessage("Yksikkönimi"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Yksikköhinta"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Rajoittamaton"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Rajoittamaton määrä laskuja"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Rajoittamaton käyttö"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Hyödynnä Pos Saas POS:n täysi potentiaali räätälöidyillä koulutustilaisuuksilla, jotka johtaa asiantuntijatiimimme. Perusteista edistyneisiin tekniikoihin varmistamme, että olet hyvin perillä järjestelmän jokaisesta osa-alueesta ja voit optimoida liiketoimintaprosessisi."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Päivitä nyt"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Päivitä suunnitelmasi ensin\\nMyyntiraja on ylitetty."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Päivitä mobiilisovelluksessa"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Lataa kuva"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Lataa laskun logo"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Lataa asiakirja"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Lataa tiedosto"),
        "userName": MessageLookupByLibrary.simpleMessage("Käyttäjänimi"),
        "userRole": MessageLookupByLibrary.simpleMessage("Käyttäjärooli"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Käyttäjäroolin nimi"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Käyttäjänimike"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("ALV/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Vahvista puhelinnumero"),
        "view": MessageLookupByLibrary.simpleMessage("Katso"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Kävelymyyjä"),
        "warranty": MessageLookupByLibrary.simpleMessage("Takuu"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Takuu"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Meidän on rekisteröitävä puhelimesi ennen kuin pääset alkuun!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Ymmärrämme saumattoman toiminnan tärkeyden. Siksi tukenamme on ympäri vuorokauden toimiva asiakastuki auttamassa sinua, olipa kyseessä nopea kysymys tai laajempi huolenaihe. Ota yhteyttä meihin milloin tahansa ja missä tahansa puhelimitse tai WhatsAppin kautta saadaksesi vertaansa vailla olevaa asiakaspalvelua."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Käsirahahinta"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Tukkumyyjä"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Tukkukauppa"),
        "wight": MessageLookupByLibrary.simpleMessage("Paino"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Kyllä, palauta"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Sinun on kirjauduttava uudelleen tilillesi."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Sinun on tunnistauduttava ennen viestien ostamista"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Kaikki myyntilistasi"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Kaikki myynnit"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Käytät"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("Myynnin velka"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Sinun on tunnistauduttava ennen viestien ostamista"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Pakettisi"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Maksusi on peruttu"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Maksusi onnistui"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Maksusi on peruutettu")
      };
}
