// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ru locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ru';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("ДОБАВИТЬ ПРОДАЖУ"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("КАТЕГОРИЯ"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("Счет-фактура"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS-продажа"),
        "PRICE": MessageLookupByLibrary.simpleMessage("ЦЕНА"),
        "PRODUCTNAME":
            MessageLookupByLibrary.simpleMessage("НАЗВАНИЕ ПРОДУКТА"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Панель входа Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("Количество"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Количество*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("СТАТУС"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("ОБЩАЯ СТОИМОСТЬ"),
        "UserTitle":
            MessageLookupByLibrary.simpleMessage("Заголовок пользователя"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("О приложении"),
        "accountName": MessageLookupByLibrary.simpleMessage("Название счета"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Номер счета"),
        "action": MessageLookupByLibrary.simpleMessage("Действие"),
        "add": MessageLookupByLibrary.simpleMessage("Добавить"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Добавить бренд"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Добавить категорию"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Добавить клиента"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Добавить описание..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Добавить документы"),
        "addItem": MessageLookupByLibrary.simpleMessage("Добавить товар"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Добавить категорию товара"),
        "addNew": MessageLookupByLibrary.simpleMessage("Добавить новое"),
        "addNewUser": MessageLookupByLibrary.simpleMessage(
            "Добавить нового пользователя"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Добавить товар"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Добавление прошло успешно"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Добавить поставщика"),
        "addUnit":
            MessageLookupByLibrary.simpleMessage("Добавить единицу измерения"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Добавить/обновить список расходов"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Добавить/обновить список доходов"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Добавить роль пользователя"),
        "addingSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Добавление серийного номера?"),
        "address": MessageLookupByLibrary.simpleMessage("Адрес"),
        "all": MessageLookupByLibrary.simpleMessage("Все"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Все базовые функции"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Уже есть аккаунт?"),
        "amount": MessageLookupByLibrary.simpleMessage("Сумма"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Поддержка приложений для Android и iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Вы хотите создать эту Quotation?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Вы уверены, что хотите удалить этого клиента?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Вы хотите удалить этот товар"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Вы хотите удалить это предложение?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Вы хотите вернуть эту продажу?"),
        "balance": MessageLookupByLibrary.simpleMessage("Баланс"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Валюта банковского счета"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Банковские счета"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Банковская информация"),
        "bankName": MessageLookupByLibrary.simpleMessage("Название банка"),
        "between": MessageLookupByLibrary.simpleMessage("Между"),
        "billTo":
            MessageLookupByLibrary.simpleMessage("Кому выставляется счет:"),
        "branchName":
            MessageLookupByLibrary.simpleMessage("Название отделения"),
        "brand": MessageLookupByLibrary.simpleMessage("Бренд"),
        "brandName": MessageLookupByLibrary.simpleMessage("Название бренда"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Бизнес-категория"),
        "buy": MessageLookupByLibrary.simpleMessage("Купить"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Купить Премиум-план"),
        "buySms": MessageLookupByLibrary.simpleMessage("Купить SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Калькулятор:"),
        "camera": MessageLookupByLibrary.simpleMessage("Камера"),
        "cancel": MessageLookupByLibrary.simpleMessage("Отмена"),
        "capacity": MessageLookupByLibrary.simpleMessage("Емкость"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Наличные и банк"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Наличные"),
        "categories": MessageLookupByLibrary.simpleMessage("Категории"),
        "category": MessageLookupByLibrary.simpleMessage("Категория"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Наименование категории"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Сдача"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Изменяемая сумма"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Проверить гарантию"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Выберите план"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Собрать долг >"),
        "color": MessageLookupByLibrary.simpleMessage("Цвет"),
        "comapnyName":
            MessageLookupByLibrary.simpleMessage("Название компании"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Адрес компании"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Описание компании"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Электронная почта компании"),
        "companyName":
            MessageLookupByLibrary.simpleMessage("Название компании"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Телефон компании"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL-адрес сайта компании"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Подтвердите пароль"),
        "continu": MessageLookupByLibrary.simpleMessage("Продолжить"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Перевести в продажу"),
        "create": MessageLookupByLibrary.simpleMessage("Создать"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Создать платеж"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Создано"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Креативный центр"),
        "currency": MessageLookupByLibrary.simpleMessage("Валюта"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Текущий план"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Пользовательская маркировка счетов"),
        "customer": MessageLookupByLibrary.simpleMessage("Покупатель"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Долг клиента"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Счета-фактуры клиентов"),
        "customerList": MessageLookupByLibrary.simpleMessage("Список клиентов"),
        "customerName": MessageLookupByLibrary.simpleMessage("Имя клиента"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Покупатель месяца"),
        "customerType": MessageLookupByLibrary.simpleMessage("Тип клиента"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "Покупатель: Посетитель магазина"),
        "customers": MessageLookupByLibrary.simpleMessage("Клиенты"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Ежедневный сбор"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Ежедневные продажи"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Ежедневная транзакция"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Панель мониторинга"),
        "date": MessageLookupByLibrary.simpleMessage("Дата"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Дата и время"),
        "dealer": MessageLookupByLibrary.simpleMessage("Дилер"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Цена дилера"),
        "delete": MessageLookupByLibrary.simpleMessage("Удалить"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("Доставка"),
        "description": MessageLookupByLibrary.simpleMessage("Описание"),
        "details": MessageLookupByLibrary.simpleMessage("Подробнее >"),
        "discount": MessageLookupByLibrary.simpleMessage("Скидка"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Цена со скидкой"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Скачать PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Долг"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Остаток к оплате"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Здесь будет отображаться сумма задолженности, если она есть"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Сбор задолженности"),
        "dueList": MessageLookupByLibrary.simpleMessage("Список задолженности"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Транзакция из-за"),
        "edit": MessageLookupByLibrary.simpleMessage("Редактировать"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Редактировать/добавить серийный номер:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Редактировать ваш профиль"),
        "email": MessageLookupByLibrary.simpleMessage("Электронная почта"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Введите сумму"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Введите название бренда"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Введите название категории"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Введите описание компании"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Введите адрес электронной почты компании"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Введите номер телефона компании"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Введите URL-адрес сайта компании"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Введите имя клиента"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Введите цену дилера"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Введите цену со скидкой"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Введите категорию расходов"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Введите дату расхода"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Введите категорию дохода"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Введите дату дохода"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Введите имя производителя"),
        "enterName": MessageLookupByLibrary.simpleMessage("Введите имя"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Введите имя"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Введите заметку"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Введите начальный баланс"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Введите сумму к оплате"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Введите пароль"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Введите сумму оплаты"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Введите цену"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Введите емкость товара"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Введите код товара"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Введите цвет товара"),
        "enterProductName": MessageLookupByLibrary.simpleMessage(
            "Введите наименование продукта"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Введите количество товара"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Введите размер товара"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Введите тип продукта"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage(
            "Введите единицу измерения товара"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Введите вес товара"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Введите закупочную цену"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Введите номер reference"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Введите цену продажи"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Введите серийный номер"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Введите текст сообщения"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Введите количество на складе"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Введите идентификатор транзакции"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage(
            "Введите название единицы измерения"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Введите название роли пользователя"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Введите заголовок пользователя"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Введите гарантию"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Введите оптовую цену"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Введите сумму"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Введите ваш адрес"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Введите свой адрес компании"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Введите название вашей компании"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Введите название вашей компании"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Введите ваш адрес электронной почты"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Введите ваш пароль"),
        "enterYourPasswordAgain":
            MessageLookupByLibrary.simpleMessage("Введите ваш пароль еще раз"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Введите ваш номер телефона"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Введите название вашего магазина"),
        "entercategoryName": MessageLookupByLibrary.simpleMessage(
            "Введите наименование категории"),
        "expense": MessageLookupByLibrary.simpleMessage("Расход"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Дата расхода"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Детали расходов"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Расход на"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Список категорий расходов"),
        "expenses": MessageLookupByLibrary.simpleMessage("Расходы"),
        "fivePurchase":
            MessageLookupByLibrary.simpleMessage("Топ-пять продуктов месяца"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Для неограниченного использования"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Забыли пароль?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Бесплатное резервное копирование данных"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Бесплатное обновление на всю жизнь"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Бесплатный пакет"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Бесплатный план"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Начать"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Удостоверение личности"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Общая сумма"),
        "hold": MessageLookupByLibrary.simpleMessage("Задержка"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Номер удержания"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Идентификационная проверка"),
        "inc": MessageLookupByLibrary.simpleMessage("Доход"),
        "income": MessageLookupByLibrary.simpleMessage("Доход"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Категория дохода"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Список категорий доходов"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Дата дохода"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("Детали дохода"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Доход за"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Список доходов"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Увеличить остаток"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage(
            "Мгновенная конфиденциальность"),
        "invoice": MessageLookupByLibrary.simpleMessage("Invoice"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Invoice:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Номер счета-фактуры..."),
        "invoiceNo":
            MessageLookupByLibrary.simpleMessage("Номер счета-фактуры"),
        "item": MessageLookupByLibrary.simpleMessage("Товар"),
        "itemName": MessageLookupByLibrary.simpleMessage("Наименование товара"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC-проверка"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("Детали учета"),
        "ledger": MessageLookupByLibrary.simpleMessage("Главная книга"),
        "left": MessageLookupByLibrary.simpleMessage("Слева"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Счета займов"),
        "logOut": MessageLookupByLibrary.simpleMessage("Выйти"),
        "login": MessageLookupByLibrary.simpleMessage("Войти"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Позиция логотипа в счете-фактуре?"),
        "loss": MessageLookupByLibrary.simpleMessage("Убыток"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Убыток/Прибыль"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Убыток(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Низкий Запас"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Низкие запасы"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Произведите неизгладимое впечатление на ваших клиентов с помощью брендированных счетов. Наше бесплатное обновление предоставляет уникальное преимущество в настройке ваших счетов, добавляя профессиональное прикосновение, укрепляющее вашу корпоративную идентичность и способствующее лояльности клиентов."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Производитель"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Панель входа в Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Панель регистрации Pos Saas"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Мобильное приложение\n+\nПК"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Квитанция"),
        "nam": MessageLookupByLibrary.simpleMessage("Имя*"),
        "name": MessageLookupByLibrary.simpleMessage("Имя"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Название, код или категория"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Новые клиенты"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Новые клиенты"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Новый доход"),
        "no": MessageLookupByLibrary.simpleMessage("Нет"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Нет подключения"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Клиенты не найдены"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Не найдено транзакций по долгам"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Категория расходов не найдена"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("Категория дохода не найдена"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Доход не найден"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Счет-фактура не найдена"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Товар не найден"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Не найдено ни одной транзакции покупки"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage(
            "Не найдено ни одного предложения"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Не найдено ни одного отчета"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Не найдена ни одна транзакция продажи"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Серийный номер не найден"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Поставщик не найден"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Не найдено транзакций"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Пользователь не найден"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Роль пользователя не найдена"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Серийный номер не найден"),
        "note": MessageLookupByLibrary.simpleMessage("Примечание"),
        "ok": MessageLookupByLibrary.simpleMessage("ОК"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Открытые чеки"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Начальный баланс"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("или перетащите PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Заказы"),
        "other": MessageLookupByLibrary.simpleMessage("Другое"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Другие доходы"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Функции пакета"),
        "paid": MessageLookupByLibrary.simpleMessage("Оплачено"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Сумма оплаты"),
        "partyName": MessageLookupByLibrary.simpleMessage("Имя партии"),
        "partyType": MessageLookupByLibrary.simpleMessage("Тип партии"),
        "password": MessageLookupByLibrary.simpleMessage("Пароль"),
        "payCash": MessageLookupByLibrary.simpleMessage("Оплатить наличными"),
        "payable": MessageLookupByLibrary.simpleMessage("К оплате"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Сумма к оплате"),
        "payment": MessageLookupByLibrary.simpleMessage("Оплата"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Платеж в"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Платеж за"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Тип оплаты"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Тип оплаты"),
        "phone": MessageLookupByLibrary.simpleMessage("Телефон"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Номер телефона"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Проверка телефона"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Добавьте продажу"),
        "pleaseAddCustomer": MessageLookupByLibrary.simpleMessage(
            "Пожалуйста, добавьте клиента"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Проверьте ваше интернет-соединение"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Пожалуйста, загрузите наше мобильное приложение и подпишитесь на пакет, чтобы использовать версию для настольных компьютеров"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Пожалуйста, введите остаток товара"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Пожалуйста, введите правильные данные"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Выберите покупателя"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Пожалуйста, введите правильные данные"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Панель регистрации Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Практика"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Премиум-поддержка клиентов"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Премиум-план"),
        "preview":
            MessageLookupByLibrary.simpleMessage("Предварительный просмотр"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Предыдущий долг:"),
        "price": MessageLookupByLibrary.simpleMessage("Цена"),
        "print": MessageLookupByLibrary.simpleMessage("Печать"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Распечатать счет-фактуру"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Распечатать PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Политика конфиденциальности"),
        "product": MessageLookupByLibrary.simpleMessage("Продукт"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Категория товара"),
        "productCod": MessageLookupByLibrary.simpleMessage("Код товара*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Цвет товара"),
        "productList": MessageLookupByLibrary.simpleMessage("Список товаров"),
        "productNam":
            MessageLookupByLibrary.simpleMessage("Наименование продукта*"),
        "productName": MessageLookupByLibrary.simpleMessage("Название товара"),
        "productSize": MessageLookupByLibrary.simpleMessage("Размер товара"),
        "productStock": MessageLookupByLibrary.simpleMessage("Остаток товара"),
        "productType": MessageLookupByLibrary.simpleMessage("Тип продукта"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Единица измерения товара"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Гарантия на продукт"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Вес товара"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Емкость товара"),
        "prof": MessageLookupByLibrary.simpleMessage("Профиль"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Редактировать профиль"),
        "profit": MessageLookupByLibrary.simpleMessage("Прибыль"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Прибыль (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Прибыль (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Покупка"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Покупочный список"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Купить Премиум-план"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Покупочная цена"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Покупочная транзакция"),
        "quantity": MessageLookupByLibrary.simpleMessage("Количество"),
        "quotation": MessageLookupByLibrary.simpleMessage("Цитата"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Список котировок"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Недавние Продажи"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Получен"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Номер"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Номер reference"),
        "registration": MessageLookupByLibrary.simpleMessage("Регистрация"),
        "remaining": MessageLookupByLibrary.simpleMessage("Осталось: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Остаточный баланс"),
        "remainingDue": MessageLookupByLibrary.simpleMessage(
            "Оставшаяся сумма задолженности"),
        "reports": MessageLookupByLibrary.simpleMessage("Отчеты"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Сбросить пароль"),
        "retailer": MessageLookupByLibrary.simpleMessage("Розничный торговец"),
        "revenue": MessageLookupByLibrary.simpleMessage("Доход"),
        "right": MessageLookupByLibrary.simpleMessage("Справа"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Сумма Продаж"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Легко защитите данные вашего бизнеса. Наше бесплатное резервное копирование данных Pos Saas POS Unlimited обеспечивает защиту ваших ценных данных от непредвиденных событий. Сосредоточьтесь на том, что действительно важно - на росте вашего бизнеса."),
        "sale": MessageLookupByLibrary.simpleMessage("Продажа"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Сумма продажи"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Детали продажи"),
        "saleList": MessageLookupByLibrary.simpleMessage("Список продаж"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Цена продажи"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Цена продажи*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Возврат продажи"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Транзакция продажи"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "История транзакций продаж (История продаж предложений)"),
        "sales": MessageLookupByLibrary.simpleMessage("Продажи"),
        "salesList": MessageLookupByLibrary.simpleMessage("Список продаж"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Сохранить и опубликовать"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Сохранить и опубликовать"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Сохранить изменения"),
        "search": MessageLookupByLibrary.simpleMessage("Поиск...."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Найти что-нибудь..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Поиск по номеру invoice..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Поиск по invoice или имени"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Поиск по имени"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Поиск по имени или телефону..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Поиск серийного номера"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Выбрать стороны"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Выберите бренд продукта"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Выбрать серийный номер"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Выбрать вариации:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Выберите срок гарантии"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Выберите ваш язык"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Отправить сообщение"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Серийный номер"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Серийный номер"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Сервисный сбор"),
        "setting": MessageLookupByLibrary.simpleMessage("Настройки"),
        "share": MessageLookupByLibrary.simpleMessage("Поделиться"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Доставка/Прочее"),
        "shopName": MessageLookupByLibrary.simpleMessage("Название магазина"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Баланс открытия магазина"),
        "show": MessageLookupByLibrary.simpleMessage("Показать >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Показывать логотип в счете-фактуре?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Доставка/Услуги"),
        "size": MessageLookupByLibrary.simpleMessage("Размер"),
        "statistic": MessageLookupByLibrary.simpleMessage("Статистика"),
        "status": MessageLookupByLibrary.simpleMessage("Статус"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Оставайтесь на передовой технологических достижений без дополнительных затрат. Наше бесплатное обновление Pos Saas POS гарантирует, что у вас всегда будут самые новые инструменты и функции под рукой, обеспечивая сохранение современности вашего бизнеса."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Оставайтесь на передовой технологических достижений без дополнительных затрат. Наше бесплатное обновление Pos Sass POS гарантирует, что у вас всегда будут самые новые инструменты и функции под рукой, обеспечивая сохранение современности вашего бизнеса."),
        "stock": MessageLookupByLibrary.simpleMessage("На складе"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Инвентаризация запасов"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Отчет о запасах"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Стоимость запасов"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Значение Запаса"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Итого"),
        "subciption": MessageLookupByLibrary.simpleMessage("Подписка"),
        "submit": MessageLookupByLibrary.simpleMessage("Подтвердить"),
        "supplier": MessageLookupByLibrary.simpleMessage("Поставщики"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Долг поставщика"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Счет-фактура поставщика"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Список поставщиков"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Код SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Общая Сумма Продаж"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Сделайте фото водительских прав, паспорта или национального ID"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Условия использования"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Название говорит само за себя. С Pos Saas POS Unlimited нет ограничений на использование. Независимо от того, обрабатываете ли вы несколько транзакций или сталкиваетесь с наплывом клиентов, вы можете работать с уверенностью, зная, что вы не ограничены лимитами."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "У этого покупателя нет задолженности"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "У этого клиента есть предыдущий долг"),
        "to": MessageLookupByLibrary.simpleMessage("К"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Самый продаваемый товар"),
        "total": MessageLookupByLibrary.simpleMessage("Итого"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Общая сумма"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Общая сумма скидки"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Всего долга"),
        "totalDues":
            MessageLookupByLibrary.simpleMessage("Общая сумма задолженности"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("Общая сумма расходов"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Общий доход"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Всего товаров: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Общий убыток"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Полная оплата"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Итого к оплате"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Общий платеж за"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Общая цена"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Всего товаров"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Общая прибыль"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Общая сумма закупок"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Общая сумма возврата"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Общее количество возвратов"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Общая сумма продаж"),
        "totalSales":
            MessageLookupByLibrary.simpleMessage("Общая сумма продаж"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Общая сумма НДС"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Общий платеж в"),
        "transaction": MessageLookupByLibrary.simpleMessage("Транзакция"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Идентификатор транзакции"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Отчет о транзакциях"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Попробуйте снова"),
        "type": MessageLookupByLibrary.simpleMessage("Тип"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Неоплаченный"),
        "unit": MessageLookupByLibrary.simpleMessage("Единица измерения"),
        "unitName":
            MessageLookupByLibrary.simpleMessage("Название единицы измерения"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Цена за единицу"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Неограниченный"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Неограниченные счета"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage(
            "Неограниченное использование"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Разблокируйте полный потенциал Pos Saas POS с персональными тренировочными сессиями, проводимыми нашей экспертной командой. От основ до продвинутых техник, мы гарантируем, что вы хорошо знакомы с использованием каждого аспекта системы для оптимизации бизнес-процессов."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Обновить сейчас"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Обновите свой план сначала\\nЛимит продаж превышен."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Обновить в мобильном приложении"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Загрузить изображение"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "Загрузить логотип счета-фактуры"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Загрузить документ"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Загрузить файл"),
        "userName": MessageLookupByLibrary.simpleMessage("Имя пользователя"),
        "userRole": MessageLookupByLibrary.simpleMessage("Роль пользователя"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Название роли пользователя"),
        "userTitle":
            MessageLookupByLibrary.simpleMessage("Заголовок пользователя"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("НДС/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Подтвердить номер телефона"),
        "view": MessageLookupByLibrary.simpleMessage("Просмотр"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage(
            "Покупатель, посетивший магазин"),
        "warranty": MessageLookupByLibrary.simpleMessage("Гарантия"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Гарантии"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Мы должны зарегистрировать ваш телефон перед началом работы!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Мы понимаем важность бесперебойной работы. Поэтому наша круглосуточная поддержка доступна, чтобы помочь вам, будь то быстрый запрос или комплексное обеспокоенность. Свяжитесь с нами в любое время, в любом месте по телефону или WhatsApp, чтобы оценить непревзойденное обслуживание клиентов."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Цена опта"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Оптовик"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Оптовая торговля"),
        "wight": MessageLookupByLibrary.simpleMessage("Вес"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Да, вернуть"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Вы должны ПЕРЕВОЙТИ в свой аккаунт."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Вы должны пройти верификацию личности перед покупкой SMS"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Ваш список всех продаж"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Все ваши продажи"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Вы используете"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Ваши просроченные продажи"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Вам необходимо пройти идентификационную проверку перед покупкой сообщений"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Ваш пакет"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Ваш платеж отменен"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Ваш платеж успешно"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Ваш платеж отменен")
      };
}
