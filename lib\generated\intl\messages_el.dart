// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a el locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'el';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("ΠΡΟΣΘΗΚΗ ΠΩΛΗΣΗΣ"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("ΚΑΤΗΓΟΡΙΑ"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("ΤΙΜΟΛΟΓΙΟ"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Πώληση POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("ΤΙΜΗ"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("ΟΝΟΜΑ ΠΡΟΪΟΝΤΟΣ"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Πίνακας σύνδεσης Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("ΠΟΣΟΤΗΤΑ"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Ποσότητα*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("ΚΑΤΑΣΤΑΣΗ"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("ΣΥΝΟΛΙΚΗ ΑΞΙΑ"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Τίτλος χρήστη"),
        "aboutApp":
            MessageLookupByLibrary.simpleMessage("Σχετικά με την εφαρμογή"),
        "accountName":
            MessageLookupByLibrary.simpleMessage("Όνομα λογαριασμού"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Αριθμός λογαριασμού"),
        "action": MessageLookupByLibrary.simpleMessage("Ενέργεια"),
        "add": MessageLookupByLibrary.simpleMessage("Προσθήκη"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Προσθήκη Μάρκας"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Προσθήκη Κατηγορίας"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Προσθήκη Πελάτη"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Προσθήκη περιγραφής..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Προσθήκη Εγγράφων"),
        "addItem": MessageLookupByLibrary.simpleMessage("Προσθήκη Είδους"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Προσθήκη Κατηγορίας Είδους"),
        "addNew": MessageLookupByLibrary.simpleMessage("Προσθήκη νέου"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Προσθήκη νέου χρήστη"),
        "addProduct":
            MessageLookupByLibrary.simpleMessage("Προσθήκη Προϊόντος"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Επιτυχής προσθήκη"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Προσθήκη Προμηθευτή"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Προσθήκη Μονάδας"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Προσθήκη/Ενημέρωση Λίστας Δαπανών"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Προσθήκη/Ενημέρωση Λίστας Εισοδημάτων"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Προσθήκη ρόλου χρήστη"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Προσθήκη Σειριακού Αριθμού;"),
        "address": MessageLookupByLibrary.simpleMessage("Διεύθυνση"),
        "all": MessageLookupByLibrary.simpleMessage("Όλα"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Όλα τα Βασικά Χαρακτηριστικά"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Έχετε ήδη λογαριασμό;"),
        "amount": MessageLookupByLibrary.simpleMessage("Ποσό"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Υποστήριξη εφαρμογής για Android και iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Θέλετε να δημιουργήσετε αυτήν την Προσφορά;"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Θέλετε να διαγράψετε αυτόν τον Πελάτη;"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Θέλετε να διαγράψετε αυτό το προϊόν"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Θέλετε να διαγράψετε αυτήν την Προσφορά;"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Θέλετε να επιστρέψετε αυτήν την πώληση;"),
        "balance": MessageLookupByLibrary.simpleMessage("Υπόλοιπο"),
        "bankAccountingCurrecny": MessageLookupByLibrary.simpleMessage(
            "Νόμισμα τραπεζικού λογαριασμού"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Λογαριασμοί τράπεζας"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Τραπεζικές πληροφορίες"),
        "bankName": MessageLookupByLibrary.simpleMessage("Όνομα τράπεζας"),
        "between": MessageLookupByLibrary.simpleMessage("Ανάμεσα"),
        "billTo": MessageLookupByLibrary.simpleMessage("Τιμολόγηση σε:"),
        "branchName":
            MessageLookupByLibrary.simpleMessage("Όνομα υποκαταστήματος"),
        "brand": MessageLookupByLibrary.simpleMessage("Μάρκα"),
        "brandName": MessageLookupByLibrary.simpleMessage("Όνομα Μάρκας"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Κατηγορία Επιχείρησης"),
        "buy": MessageLookupByLibrary.simpleMessage("Αγορά"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Αγορά προηγμένου πλάνου"),
        "buySms": MessageLookupByLibrary.simpleMessage("Αγορά μηνυμάτων SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Αριθμομηχανή:"),
        "camera": MessageLookupByLibrary.simpleMessage("Κάμερα"),
        "cancel": MessageLookupByLibrary.simpleMessage("Άκυρο"),
        "capacity": MessageLookupByLibrary.simpleMessage("Χωρητικότητα"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("Μετρητά και τράπεζα"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Μετρητά"),
        "categories": MessageLookupByLibrary.simpleMessage("Κατηγορίες"),
        "category": MessageLookupByLibrary.simpleMessage("Κατηγορία"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Όνομα Κατηγορίας"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Αλλαγή Ποσού"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Αλλαγή ποσού"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Έλεγχος Εγγύησης"),
        "choseAplan":
            MessageLookupByLibrary.simpleMessage("Επιλέξτε ένα πλάνο"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Είσπραξη Οφειλόμενου >"),
        "color": MessageLookupByLibrary.simpleMessage("Χρώμα"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Όνομα Εταιρείας"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Διεύθυνση Εταιρείας"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Περιγραφή Εταιρείας"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Διεύθυνση email εταιρείας"),
        "companyName": MessageLookupByLibrary.simpleMessage("Όνομα Εταιρείας"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Τηλέφωνο Εταιρείας"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL Ιστότοπου Εταιρείας"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage(
            "Επιβεβαίωση κωδικού πρόσβασης"),
        "continu": MessageLookupByLibrary.simpleMessage("Συνέχεια"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Μετατροπή σε Πώληση"),
        "create": MessageLookupByLibrary.simpleMessage("Δημιουργία"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Δημιουργία Πληρωμής"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Δημιουργήθηκε Από"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Δημιουργικό Hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Νόμισμα"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Τρέχον πλάνο"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Προσαρμοσμένη σήμανση τιμολογίου"),
        "customer": MessageLookupByLibrary.simpleMessage("Πελάτης"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Οφειλή Πελάτη"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Τιμολόγια Πελατών"),
        "customerList": MessageLookupByLibrary.simpleMessage("Λίστα Πελατών"),
        "customerName": MessageLookupByLibrary.simpleMessage("Όνομα Πελάτη"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Πελάτης του μήνα"),
        "customerType": MessageLookupByLibrary.simpleMessage("Τύπος Πελάτη"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "Πελάτης: Πελάτης που μπήκε στο κατάστημα"),
        "customers": MessageLookupByLibrary.simpleMessage("Πελάτες"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Ημερήσια Είσπραξη"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Ημερήσιες Πωλήσεις"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Καθημερινή Συναλλαγή"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Πίνακας Ελέγχου"),
        "date": MessageLookupByLibrary.simpleMessage("Ημερομηνία"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Ημερομηνία Ώρα"),
        "dealer": MessageLookupByLibrary.simpleMessage("Έμπορος"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Τιμή Έμπορου"),
        "delete": MessageLookupByLibrary.simpleMessage("Διαγραφή"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Χρέωση Παράδοσης"),
        "description": MessageLookupByLibrary.simpleMessage("Περιγραφή"),
        "details": MessageLookupByLibrary.simpleMessage("Λεπτομέρειες >"),
        "discount": MessageLookupByLibrary.simpleMessage("Έκπτωση"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Τιμή Έκπτωσης"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Λήψη PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Οφειλόμενο"),
        "dueAmount":
            MessageLookupByLibrary.simpleMessage("Υπόλοιπο Οφειλόμενου"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Το οφειλόμενο ποσό θα εμφανιστεί εδώ εάν είναι διαθέσιμο"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Συλλογή οφειλών"),
        "dueList": MessageLookupByLibrary.simpleMessage("Λίστα οφειλών"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Λόγω Συναλλαγή"),
        "edit": MessageLookupByLibrary.simpleMessage("Επεξεργασία"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Επεξεργασία/Προσθήκη Σειριακού:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Επεξεργασία του προφίλ σας"),
        "email": MessageLookupByLibrary.simpleMessage("Ηλεκτρονική Διεύθυνση"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Εισαγωγή Ποσού"),
        "enterBrandName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το Όνομα της Μάρκας"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε την ονομασία της κατηγορίας"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε την περιγραφή της εταιρείας σας"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τη διεύθυνση email της εταιρείας σας"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το τηλέφωνο της εταιρείας σας"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το URL του ιστότοπου της εταιρείας σας"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Εισαγωγή Ονόματος Πελάτη"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε την τιμή έκπτωσης"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Εισαγωγή Κατηγορίας Δαπάνης"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε την ημερομηνία δαπάνης"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε Κατηγορία Εσόδων"),
        "enterIncomeDate": MessageLookupByLibrary.simpleMessage(
            "Εισαγωγή Ημερομηνίας Εισοδήματος"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το όνομα του κατασκευαστή"),
        "enterName":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε το όνομα"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Εισαγωγή Ονόματος"),
        "enterNote":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε τη σημείωση"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Εισαγωγή Αρχικού Υπολοίπου"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Εισαγωγή πληρωμένου ποσού"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Εισαγωγή κωδικού πρόσβασης"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε το ποσό πληρωμής"),
        "enterPrice":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε την τιμή"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τη χωρητικότητα του προϊόντος"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τον Κωδικό του Προϊόντος"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το χρώμα του προϊόντος"),
        "enterProductName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το Όνομα του Προϊόντος"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τη Ποσότητα του Προϊόντος"),
        "enterProductSize": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το μέγεθος του προϊόντος"),
        "enterProductType": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τον Τύπο του Προϊόντος"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τη μονάδα μέτρησης του προϊόντος"),
        "enterProductWeight": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το βάρος του προϊόντος"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε την Τιμή Αγοράς"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τον αριθμό αναφοράς"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε την τιμή πώλησης"),
        "enterSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τον σειριακό αριθμό"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το περιεχόμενο του μηνύματος"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το ποσό αποθέματος"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Εισαγωγή αναγνωριστικού συναλλαγής"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το Όνομα της Μονάδας"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Εισαγωγή ονόματος ρόλου χρήστη"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Εισαγωγή τίτλου χρήστη"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε την Εγγύηση"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε την τιμή χονδρικής"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε το ποσό σας"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Εισαγάγετε τη διεύθυνσή σας"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τη διεύθυνση της εταιρείας σας"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το όνομα της εταιρείας σας"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το όνομα της εταιρείας σας"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε την ηλεκτρονική σας διεύθυνση"),
        "enterYourPassword": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τον κωδικό πρόσβασής σας"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε ξανά τον κωδικό πρόσβασής σας"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε τον αριθμό τηλεφώνου σας"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Εισαγάγετε το όνομα του καταστήματός σας"),
        "entercategoryName": MessageLookupByLibrary.simpleMessage(
            "Εισαγωγή Ονόματος Κατηγορίας"),
        "expense": MessageLookupByLibrary.simpleMessage("Έξοδο"),
        "expenseDate":
            MessageLookupByLibrary.simpleMessage("Ημερομηνία Δαπάνης"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Λεπτομέρειες Δαπάνης"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Δαπάνη Για"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Λίστα Κατηγορίας Δαπάνης"),
        "expenses": MessageLookupByLibrary.simpleMessage("Έξοδα"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Τα πέντε κορυφαία προϊόντα αγοράς του μήνα"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Για απεριόριστες χρήσεις"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Ξεχάσατε τον κωδικό;"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Δωρεάν δημιουργία αντιγράφων ασφαλείας δεδομένων"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Δωρεάν ενημέρωση για ολόκληρη τη ζωή"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Δωρεάν πακέτο"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Δωρεάν πλάνο"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Ξεκινήστε"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Ταυτότητα Κυβέρνησης"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Συνολικό Ποσό"),
        "hold": MessageLookupByLibrary.simpleMessage("Κράτημα"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Αριθμός Κράτησης"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Επαλήθευση Ταυτότητας"),
        "inc": MessageLookupByLibrary.simpleMessage("Έσοδα"),
        "income": MessageLookupByLibrary.simpleMessage("Έσοδα"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Κατηγορία Εισοδήματος"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage(
            "Λίστα Κατηγορίας Εισοδήματος"),
        "incomeDate":
            MessageLookupByLibrary.simpleMessage("Ημερομηνία Εισοδήματος"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Λεπτομέρειες Εισοδήματος"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Εισόδημα Για"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Λίστα Εισοδημάτων"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Αύξηση αποθέματος"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Άμεση Ιδιωτικότητα"),
        "invoice": MessageLookupByLibrary.simpleMessage("Τιμολόγιο"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Τιμολόγιο:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Αριθμός τιμολογίου..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Αρ. Τιμολογίου"),
        "item": MessageLookupByLibrary.simpleMessage("Είδος"),
        "itemName": MessageLookupByLibrary.simpleMessage("Όνομα Είδους"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Επαλήθευση KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Λεπτομέρειες Λογαριασμού"),
        "ledger": MessageLookupByLibrary.simpleMessage("Λογιστικό φύλλο"),
        "left": MessageLookupByLibrary.simpleMessage("Αριστερά"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Λογαριασμοί δανείων"),
        "logOut": MessageLookupByLibrary.simpleMessage("Αποσύνδεση"),
        "login": MessageLookupByLibrary.simpleMessage("Σύνδεση"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Θέση λογότυπου στο τιμολόγιο;"),
        "loss": MessageLookupByLibrary.simpleMessage("Ζημιά"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Ζημία/Κέρδος"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Ζημιά(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Χαμηλό Απόθεμα"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Χαμηλά Αποθέματα"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Δημιουργήστε μια αξέχαστη εντύπωση στους πελάτες σας με εξατομικευμένα τιμολόγια. Η αναβάθμισή μας προσφέρει το μοναδικό πλεονέκτημα της προσαρμογής των τιμολογίων σας, προσθέτοντας μια επαγγελματική πινελιά που ενισχύει την ταυτότητα της επιχείρησής σας και ενθαρρύνει την εμπιστοσύνη των πελατών."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Κατασκευαστής"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Πίνακας Σύνδεσης Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Πίνακας Εγγραφής Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Κινητό Εφαρμογή\n+\nΕπιφάνεια εργασίας"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Απόδειξη Πληρωμής"),
        "nam": MessageLookupByLibrary.simpleMessage("Όνομα*"),
        "name": MessageLookupByLibrary.simpleMessage("Όνομα"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Όνομα ή Κωδικός ή Κατηγορία"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Νέοι Πελάτες"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Νέο Εισόδημα"),
        "no": MessageLookupByLibrary.simpleMessage("Όχι"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Χωρίς σύνδεση"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκαν πελάτες"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Δεν βρέθηκαν συναλλαγές οφειλής"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Δεν βρέθηκε καμία κατηγορία δαπάνης"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Δεν Βρέθηκε Κατηγορία Εισοδήματος"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Δεν Βρέθηκαν Εισοδήματα"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκαν τιμολόγια"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκαν προϊόντα"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Δεν βρέθηκαν συναλλαγές αγοράς"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκε Προσφορά"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκε Αναφορά"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Δεν βρέθηκε Συναλλαγή Πώλησης"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Δεν βρέθηκε Σειριακός Αριθμός"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκαν προμηθευτές"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Δεν Βρέθηκαν Συναλλαγές"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκε χρήστης"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Δεν βρέθηκε ρόλος χρήστη"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Δεν βρέθηκε σειριακός αριθμός"),
        "note": MessageLookupByLibrary.simpleMessage("Σημείωση"),
        "ok": MessageLookupByLibrary.simpleMessage("Εντάξει"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Ανοιχτά επιταγές"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Αρχικό Υπόλοιπο"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "ή σύρετε και αποθέστε PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Παραγγελίες"),
        "other": MessageLookupByLibrary.simpleMessage("Άλλο"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Άλλα Έσοδα"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Χαρακτηριστικά πακέτου"),
        "paid": MessageLookupByLibrary.simpleMessage("Εξοφλημένο"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Πληρωθέν Ποσό"),
        "partyName": MessageLookupByLibrary.simpleMessage("Όνομα Ομάδας"),
        "partyType": MessageLookupByLibrary.simpleMessage("Τύπος Ομάδας"),
        "password": MessageLookupByLibrary.simpleMessage("Κωδικός Πρόσβασης"),
        "payCash": MessageLookupByLibrary.simpleMessage("Πληρωμή μετρητοίς"),
        "payable": MessageLookupByLibrary.simpleMessage("Πληρέξιμο"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Ποσό Πληρωμής"),
        "payment": MessageLookupByLibrary.simpleMessage("Πληρωμή"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Είσπραξη"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Εξόδων Πληρωμή"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Τύπος Πληρωμής"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Τύπος Πληρωμής"),
        "phone": MessageLookupByLibrary.simpleMessage("Τηλέφωνο"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Τηλέφωνο Επικοινωνίας"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Επαλήθευση Τηλεφώνου"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage(
            "Παρακαλώ προσθέστε μια πώληση"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Παρακαλώ Προσθέστε Πελάτη"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Παρακαλώ ελέγξτε τη συνδεσιμότητα του διαδικτύου σας"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Κατεβάστε την κινητή μας εφαρμογή και εγγραφείτε σε ένα πακέτο για να χρησιμοποιήσετε την εκδοχή για υπολογιστή"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Παρακαλώ εισαγάγετε το απόθεμα του προϊόντος"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Παρακαλώ εισάγετε έγκυρα δεδομένα"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Παρακαλώ επιλέξτε έναν πελάτη"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Παρακαλώ εισαγάγετε έγκυρα δεδομένα"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Πίνακας εγγραφής Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Πρακτική"),
        "premiumCustomerSupport": MessageLookupByLibrary.simpleMessage(
            "Προηγμένη υποστήριξη πελατών"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Προηγμένο πλάνο"),
        "preview": MessageLookupByLibrary.simpleMessage("Προεπισκόπηση"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Προηγούμενο Οφειλόμενο:"),
        "price": MessageLookupByLibrary.simpleMessage("Τιμή"),
        "print": MessageLookupByLibrary.simpleMessage("Εκτύπωση"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Εκτύπωση Τιμολογίου"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Εκτύπωση PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Πολιτική απορρήτου"),
        "product": MessageLookupByLibrary.simpleMessage("Προϊόν"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Κατηγορία Προϊόντος"),
        "productColor": MessageLookupByLibrary.simpleMessage("Χρώμα Προϊόντος"),
        "productList": MessageLookupByLibrary.simpleMessage("Λίστα Προϊόντων"),
        "productName":
            MessageLookupByLibrary.simpleMessage("Ονομασία Προϊόντος"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Μέγεθος Προϊόντος"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Απόθεμα προϊόντων"),
        "productType": MessageLookupByLibrary.simpleMessage("Τύπος Προϊόντος"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Μονάδα Προϊόντος"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Βάρος Προϊόντος"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Χωρητικότητα Προϊόντος"),
        "prof": MessageLookupByLibrary.simpleMessage("Προφίλ"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Επεξεργασία προφίλ"),
        "profit": MessageLookupByLibrary.simpleMessage("Κέρδος"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Κέρδος(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Κέρδος(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Αγορά"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Λίστα Αγορών"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Αγορά προηγμένου πλάνου"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Τιμή Αγοράς"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Συναλλαγή Αγοράς"),
        "quantity": MessageLookupByLibrary.simpleMessage("Ποσότητα"),
        "quotation": MessageLookupByLibrary.simpleMessage("Προσφορά"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Λίστα Προσφορών"),
        "recentSale":
            MessageLookupByLibrary.simpleMessage("Πρόσφατες Πωλήσεις"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Ληφθέν Ποσό"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Αρ. Αναφοράς"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Αριθμός Αναφοράς"),
        "registration": MessageLookupByLibrary.simpleMessage("Εγγραφή"),
        "remaining": MessageLookupByLibrary.simpleMessage("Υπολείπεται: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Υπόλοιπο Υπόλοιπο"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Υπόλοιπο Οφειλής"),
        "reports": MessageLookupByLibrary.simpleMessage("Αναφορές"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Επαναφορά Κωδικού Πρόσβασης"),
        "retailer": MessageLookupByLibrary.simpleMessage("Λιανοπωλητής"),
        "revenue": MessageLookupByLibrary.simpleMessage("Έσοδα"),
        "right": MessageLookupByLibrary.simpleMessage("Δεξιά"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Ποσό Πώλησης"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Προστατέψτε τα δεδομένα της επιχείρησής σας με ευκολία. Η αναβάθμισή μας για το Pos Saas POS περιλαμβάνει δωρεάν δημιουργία αντιγράφων ασφαλείας δεδομένων, εξασφαλίζοντας ότι οι πολύτιμες πληροφορίες σας προστατεύονται από απρόβλεπα γεγονότα. Επικεντρωθείτε σε αυτό που έχει πραγματικά σημασία - την ανάπτυξη της επιχείρησής σας."),
        "sale": MessageLookupByLibrary.simpleMessage("Πώληση"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Ποσό Πώλησης"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Λεπτομέρειες πώλησης"),
        "saleList": MessageLookupByLibrary.simpleMessage("Λίστα Πωλήσεων"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Τιμή Πώλησης"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Τιμή Πώλησης*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Επιστροφή Πώλησης"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Συναλλαγή Πώλησης"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Συναλλαγές Πώλησης (Ιστορικό Πώλησης Προσφοράς)"),
        "sales": MessageLookupByLibrary.simpleMessage("Πωλήσεις"),
        "salesList": MessageLookupByLibrary.simpleMessage("Λίστα πωλήσεων"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Αποθήκευση και Δημοσίευση"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Αποθήκευση και Δημοσίευση"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Αποθήκευση Αλλαγών"),
        "search": MessageLookupByLibrary.simpleMessage("Αναζήτηση..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Αναζήτηση οτιδήποτε..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Αναζήτηση με τιμολόγιο...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Αναζήτηση με τιμολόγιο ή όνομα"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Αναζήτηση με όνομα"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Αναζήτηση με Όνομα ή Τηλέφωνο..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Αναζήτηση Σειριακού Αριθμού"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Επιλογή Μερών"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Επιλέξτε Μάρκα Προϊόντος"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Επιλέξτε Σειριακό Αριθμό"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Επιλέξτε Παραλλαγές:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Επιλέξτε Χρόνο Εγγύησης"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Επιλέξτε τη γλώσσα σας"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Αποστολή μηνύματος"),
        "serialNumber":
            MessageLookupByLibrary.simpleMessage("Σειριακός Αριθμός"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("Σειριακός Αριθμός"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Χρέωση Υπηρεσίας"),
        "setting": MessageLookupByLibrary.simpleMessage("Ρύθμιση"),
        "share": MessageLookupByLibrary.simpleMessage("Κοινοποίηση"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Αποστολή/Άλλο"),
        "shopName": MessageLookupByLibrary.simpleMessage("Όνομα Καταστήματος"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Αρχικό Υπόλοιπο Καταστήματος"),
        "show": MessageLookupByLibrary.simpleMessage("Εμφάνιση >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Εμφάνιση λογότυπου στο τιμολόγιο;"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Αποστολή/Υπηρεσία"),
        "size": MessageLookupByLibrary.simpleMessage("Μέγεθος"),
        "statistic": MessageLookupByLibrary.simpleMessage("Στατιστική"),
        "status": MessageLookupByLibrary.simpleMessage("Κατάσταση"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Μείνετε στην πρώτη γραμμή των τεχνολογικών εξελίξεων χωρίς πρόσθετο κόστος. Η αναβάθμιση του Pos Saas POS Unlimited εξασφαλίζει ότι θα έχετε πάντοτε τα τελευταία εργαλεία και χαρακτηριστικά στη διάθεσή σας, εξασφαλίζοντας ότι η επιχείρησή σας παραμένει στην αιχμή."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Μείνετε στην πρώτη γραμμή των τεχνολογικών εξελίξεων χωρίς πρόσθετο κόστος. Η αναβάθμιση του Pos Sass POS Unlimited εξασφαλίζει ότι θα έχετε πάντοτε τα τελευταία εργαλεία και χαρακτηριστικά στη διάθεσή σας, εξασφαλίζοντας ότι η επιχείρησή σας παραμένει στην αιχμή."),
        "stock": MessageLookupByLibrary.simpleMessage("Απόθεμα"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Αποθέματα Εμπορευμάτων"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Έκθεση Αποθεμάτων"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Αξία Αποθεμάτων"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Αξία Αποθέματος"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Μερικό Σύνολο"),
        "subciption": MessageLookupByLibrary.simpleMessage("Εγγραφή"),
        "submit": MessageLookupByLibrary.simpleMessage("Υποβολή"),
        "supplier": MessageLookupByLibrary.simpleMessage("Προμηθευτές"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Οφειλή Προμηθευτή"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Τιμολόγιο Προμηθευτή"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Λίστα Προμηθευτών"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Κωδικός SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Συνολικές Πωλήσεις"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Πάρτε φωτογραφία διπλώματος οδήγησης, εθνικής ταυτότητας ή διαβατηρίου"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Όροι χρήσης"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Το όνομα λέει τα πάντα. Με το Pos Saas POS Unlimited, δεν υπάρχει περιορισμός στη χρήση σας. Είτε επεξεργάζεστε μια χειροτερία συναλλαγών είτε βιώνετε μια έξαρση πελατών, μπορείτε να λειτουργείτε με αυτοπεποίθηση, γνωρίζοντας ότι δεν περιορίζεστε από περιορισμούς."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Αυτός ο πελάτης δεν έχει οφειλές"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Αυτός ο πελάτης έχει προηγούμενο οφειλόμενο"),
        "to": MessageLookupByLibrary.simpleMessage("Έως"),
        "topSellingProduct": MessageLookupByLibrary.simpleMessage(
            "Προϊόν με τις περισσότερες πωλήσεις"),
        "total": MessageLookupByLibrary.simpleMessage("Σύνολο"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Συνολικό Ποσό"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Συνολική Έκπτωση"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Συνολικό Οφειλόμενο"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Συνολικές Οφειλές"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Συνολική Δαπάνη"),
        "totalIncome":
            MessageLookupByLibrary.simpleMessage("Συνολικό Εισόδημα"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Συνολικά είδη: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Συνολική Ζημιά"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Σύνολο Εξοφλημένου"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Συνολικό Ποσό προς Πληρωμή"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Συνολική Εξόδων Πληρωμή"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Συνολική Τιμή"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Σύνολο Προϊόντων"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Συνολικό Κέρδος"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Συνολικές Αγορές"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Συνολικό Ποσό Επιστροφής"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Σύνολο Επιστροφών"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Συνολικές Πωλήσεις"),
        "totalSales":
            MessageLookupByLibrary.simpleMessage("Συνολικές Πωλήσεις"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Συνολικό ΦΠΑ"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Συνολική Είσπραξη"),
        "transaction": MessageLookupByLibrary.simpleMessage("Συναλλαγή"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Ταυτότητα συναλλαγής"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Έκθεση Συναλλαγών"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Δοκιμάστε ξανά"),
        "type": MessageLookupByLibrary.simpleMessage("Τύπος"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Μη Εξοφλημένο"),
        "unit": MessageLookupByLibrary.simpleMessage("Μονάδα"),
        "unitName": MessageLookupByLibrary.simpleMessage("Όνομα Μονάδας"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Τιμή Μονάδας"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Απεριόριστο"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Απεριόριστες Τιμολογήσεις"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Απεριόριστη χρήση"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Ξεκλειδώστε την πλήρη δυνατότητα του Pos Saas POS με εξατομικευμένες συνεδρίες εκπαίδευσης που ηγούνται από την ομάδα ειδικών μας. Από τις βασικές μέχρι τις προηγμένες τεχνικές, εξασφαλίζουμε ότι θα είστε καλά εξοικειωμένοι με τη χρήση κάθε πτυχής του συστήματος για τη βελτιστοποίηση των επιχειρηματικών σας διαδικασιών."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Ενημέρωση τώρα"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Ενημερώστε πρώτα το σχέδιό σας\\nΤο όριο πώλησης έχει υπερβεί."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Ενημέρωση στην εφαρμογή για κινητά"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Μεταφόρτωση εικόνας"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "Μεταφόρτωση λογότυπου τιμολογίου"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Μεταφόρτωση εγγράφου"),
        "uploadFile":
            MessageLookupByLibrary.simpleMessage("Μεταφόρτωση αρχείου"),
        "userName": MessageLookupByLibrary.simpleMessage("Όνομα χρήστη"),
        "userRole": MessageLookupByLibrary.simpleMessage("Ρόλος χρήστη"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Όνομα ρόλου χρήστη"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Τίτλος χρήστη"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("ΦΠΑ / GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Επαλήθευση Αριθμού Τηλεφώνου"),
        "view": MessageLookupByLibrary.simpleMessage("Προβολή"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage(
            "Πελάτης που μπήκε στο κατάστημα"),
        "warranty": MessageLookupByLibrary.simpleMessage("Εγγύηση"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Εγγύηση"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Χρειαζόμαστε να εγγραφούμε το τηλέφωνό σας πριν ξεκινήσουμε!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Κατανοούμε τη σημασία της άψογης λειτουργίας. Γι\' αυτό η υποστήριξή μας διατίθεται όλο το 24ωρο για να σας βοηθήσει, είτε πρόκειται για γρήγορες ερωτήσεις είτε για πιο ολοκληρωμένα θέματα. Επικοινωνήστε μαζί μας οποτεδήποτε, οπουδήποτε, μέσω κλήσης ή WhatsApp για να ζήσετε μια ασυναγώνιστη εξυπηρέτηση πελατών."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Τιμή Χονδρικής"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Χονδρέμπορος"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Χονδρική"),
        "wight": MessageLookupByLibrary.simpleMessage("Βάρος"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ναι, Επιστροφή"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Πρέπει να ξανασυνδεθείτε στον λογαριασμό σας."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Πρέπει να επαληθεύσετε την ταυτότητά σας πριν αγοράσετε μηνύματα"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Ο κατάλογος όλων των πωλήσεών σας"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Όλες οι πωλήσεις σας"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Χρησιμοποιείτε"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Οι οφειλόμενες πωλήσεις σας"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Πρέπει να επαληθεύσετε την ταυτότητά σας πριν αγοράσετε μηνύματα"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Το πακέτο σας"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Η πληρωμή σας ακυρώθηκε"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Η πληρωμή σας ολοκληρώθηκε με επιτυχία"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Η πληρωμή σας ακυρώθηκε")
      };
}
