// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a hr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'hr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("DODAJ PRODAJU"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIJA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("RAČUN"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale":
            MessageLookupByLibrary.simpleMessage("Prodaja putem POS uređaja"),
        "PRICE": MessageLookupByLibrary.simpleMessage("CIJENA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NAZIV PROIZVODA"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Prijavna ploča"),
        "QTY": MessageLookupByLibrary.simpleMessage("KOLIČINA"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("UKUPNA VRIJEDNOST"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Naziv korisnika"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("O aplikaciji"),
        "accountName": MessageLookupByLibrary.simpleMessage("Naziv računa"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Broj računa"),
        "action": MessageLookupByLibrary.simpleMessage("Akcija"),
        "add": MessageLookupByLibrary.simpleMessage("Dodaj"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Dodaj marku"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Dodaj kategoriju"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Dodaj kupca"),
        "addDescription": MessageLookupByLibrary.simpleMessage("Dodaj opis..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Dodaj dokumente"),
        "addItem": MessageLookupByLibrary.simpleMessage("Dodaj stavku"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Dodaj kategoriju stavke"),
        "addNew": MessageLookupByLibrary.simpleMessage("Dodaj novo"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Dodaj novog korisnika"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Dodaj proizvod"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Uspješno dodano"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Dodaj dobavljača"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Dodaj jedinicu"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/Ažuriraj popis troškova"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/Ažuriraj popis prihoda"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Dodaj ulogu korisnika"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Dodavanje serijskog broja?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresa"),
        "all": MessageLookupByLibrary.simpleMessage("Svi"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Sve osnovne značajke"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Već imate račun?"),
        "amount": MessageLookupByLibrary.simpleMessage("Iznos"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Podrška za Android i iOS aplikacije"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Želite li stvoriti ovu ponudu?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Želite li izbrisati ovog kupca?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Želite li izbrisati ovaj proizvod"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Želite li izbrisati ovu ponudu?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Želite li vratiti ovu prodaju?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Valuta bankovnog računa"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankovni računi"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bankovni podaci"),
        "bankName": MessageLookupByLibrary.simpleMessage("Naziv banke"),
        "between": MessageLookupByLibrary.simpleMessage("Između"),
        "billTo": MessageLookupByLibrary.simpleMessage("Račun za:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Naziv podružnice"),
        "brand": MessageLookupByLibrary.simpleMessage("Marka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Ime marke"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Poslovna kategorija"),
        "buy": MessageLookupByLibrary.simpleMessage("Kupi"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kupi Premium plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Kupi SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Odustani"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapacitet"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Gotovina i banka"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Gotovina u ruci"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorije"),
        "category": MessageLookupByLibrary.simpleMessage("Kategorija"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Ime kategorije"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Iznos za povrat"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Promjenjivi iznos"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Provjerite jamstvo"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Odaberite plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Naplatiti dospjelo >"),
        "color": MessageLookupByLibrary.simpleMessage("Boja"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Naziv tvrtke"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Adresa tvrtke"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Opis tvrtke"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Adresa e-pošte tvrtke"),
        "companyName": MessageLookupByLibrary.simpleMessage("Naziv tvrtke"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Telefonski broj tvrtke"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL web stranice tvrtke"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Potvrdite lozinku"),
        "continu": MessageLookupByLibrary.simpleMessage("Nastavi"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Pretvori u prodaju"),
        "create": MessageLookupByLibrary.simpleMessage("Kreiraj"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Kreiraj uplatu"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Kreirao"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreativni centar"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Trenutni plan"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Prilagođeno označavanje računa"),
        "customer": MessageLookupByLibrary.simpleMessage("Kupac"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Dugovanje kupca"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Računi kupaca"),
        "customerList": MessageLookupByLibrary.simpleMessage("Popis kupaca"),
        "customerName": MessageLookupByLibrary.simpleMessage("Ime kupca"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Kupac mjeseca"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tip klijenta"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Kupac: Šetnja kod kupca"),
        "customers": MessageLookupByLibrary.simpleMessage("Kupci"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Dnevna naplata"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Dnevna prodaja"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Dnevna transakcija"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Nadzorna ploča"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum i vrijeme"),
        "dealer": MessageLookupByLibrary.simpleMessage("Distributer"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Cijena za trgovca"),
        "delete": MessageLookupByLibrary.simpleMessage("Izbriši"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Trošak dostave"),
        "description": MessageLookupByLibrary.simpleMessage("Opis"),
        "details": MessageLookupByLibrary.simpleMessage("Detalji >"),
        "discount": MessageLookupByLibrary.simpleMessage("Popust"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Cijena s popustom"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Preuzmi PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Dugovanje"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Iznos duga"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Nepodmireni iznos će se prikazati ovdje ako je dostupan"),
        "dueCollection": MessageLookupByLibrary.simpleMessage(
            "Prikupljanje nepodmirenih iznosa"),
        "dueList":
            MessageLookupByLibrary.simpleMessage("Popis nepodmirenih iznosa"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcija duga"),
        "edit": MessageLookupByLibrary.simpleMessage("Uredi"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Uredi/Dodaj serijski broj:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Uredite svoj profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Unesite iznos"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Unesite ime marke"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv kategorije"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Unesite opis tvrtke"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Unesite adresu e-pošte tvrtke"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Unesite telefonski broj tvrtke"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Unesite URL web stranice tvrtke"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Unesite ime kupca"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Unesite cijenu s popustom"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Unesite kategoriju troška"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Unesite datum troška"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Unesite kategoriju prihoda"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Unesite datum prihoda"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Unesite ime proizvođača"),
        "enterName": MessageLookupByLibrary.simpleMessage("Unesite ime"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Unesite ime"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Unesite napomenu"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Unesite početno stanje"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Unesite plaćeni iznos"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Unesite lozinku"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos plaćanja"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Unesite cijenu"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Unesite kapacitet proizvoda"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Unesite šifru proizvoda"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Unesite boju proizvoda"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Unesite ime proizvoda"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Unesite količinu proizvoda"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Unesite veličinu proizvoda"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Unesite vrstu proizvoda"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Unesite jedinicu proizvoda"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Unesite težinu proizvoda"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Unesite cijenu kupovine"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Unesite referentni broj"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Unesite prodajnu cijenu"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Unesite serijski broj"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Unesite sadržaj poruke"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Unesite količinu zaliha"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Unesite ID transakcije"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Unesite ime jedinice"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Unesite naziv uloge korisnika"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Unesite naziv korisnika"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Unesite jamstvo"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Unesite veleprodajnu cijenu"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Unesite iznos"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Unesite svoju adresu"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Unesite adresu vaše tvrtke"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Unesite naziv vaše tvrtke"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Unesite naziv svoje tvrtke"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Unesite svoju email adresu"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Unesite svoju lozinku"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Unesite ponovno svoju lozinku"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Unesite svoj broj telefona"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Unesite naziv svoje trgovine"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Unesite ime kategorije"),
        "expense": MessageLookupByLibrary.simpleMessage("Trošak"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Datum troška"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detalji troškova"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Troškovi za"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Popis kategorija troškova"),
        "expenses": MessageLookupByLibrary.simpleMessage("Troškovi"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top pet kupljenih proizvoda ovog mjeseca"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Za neograničenu upotrebu"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Zaboravili ste lozinku?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Besplatno sigurnosno kopiranje podataka"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Besplatno doživotno ažuriranje"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Besplatni paket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Besplatni plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Krenimo"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Vladina identifikacija"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Ukupno"),
        "hold": MessageLookupByLibrary.simpleMessage("Zadržano"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Broj zadržavanja"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Provjeri identitet"),
        "inc": MessageLookupByLibrary.simpleMessage("Prihod"),
        "income": MessageLookupByLibrary.simpleMessage("Prihod"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija prihoda"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Popis kategorija prihoda"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Datum prihoda"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detalji prihoda"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Prihod za"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Popis prihoda"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Povećaj zalihe"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Trenutna privatnost"),
        "invoice": MessageLookupByLibrary.simpleMessage("Račun"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Račun:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Broj računa..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Broj računa"),
        "item": MessageLookupByLibrary.simpleMessage("Artikl"),
        "itemName": MessageLookupByLibrary.simpleMessage("Naziv stavke"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC provjera"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Detalji glavne knjige"),
        "ledger": MessageLookupByLibrary.simpleMessage("Dnevnik"),
        "left": MessageLookupByLibrary.simpleMessage("Lijevo"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Kreditni računi"),
        "logOut": MessageLookupByLibrary.simpleMessage("Odjava"),
        "login": MessageLookupByLibrary.simpleMessage("Prijava"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Položaj logotipa na računu?"),
        "loss": MessageLookupByLibrary.simpleMessage("Gubitak"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Gubitak/Dobit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Gubitak(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Niska Zaliha"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Niski zalihi"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Stvorite trajan dojam na svoje kupce s prilagođenim računima. Naša neograničena nadogradnja nudi jedinstvenu prednost prilagođavanja svojih računa, dodajući profesionalni dodir koji jača vaš identitet marke i potiče vjernost kupaca."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Proizvođač"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Panel za prijavu"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Panel za prijavu"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Mobilna aplikacija\n+\nRačunalo"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Potvrda o primljenoj uplati"),
        "nam": MessageLookupByLibrary.simpleMessage("Ime*"),
        "name": MessageLookupByLibrary.simpleMessage("Ime"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Novi kupci"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Novi kupci"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Novi prihod"),
        "no": MessageLookupByLibrary.simpleMessage("Ne"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Nema veze"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih kupaca"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih dospjelih transakcija"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih kategorija troškova"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih kategorija prihoda"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih prihoda"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih računa"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih proizvoda"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih transakcija kupnje"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih ponuda"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih izvješća"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih transakcija prodaje"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih serijskih brojeva"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih dobavljača"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih transakcija"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Nema pronađenih korisnika"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Nema pronađenih uloga korisnika"),
        "note": MessageLookupByLibrary.simpleMessage("Napomena"),
        "ok": MessageLookupByLibrary.simpleMessage("U redu"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Otvoreni čekovi"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Početno stanje"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " ili povucite i ispustite PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Narudžbe"),
        "other": MessageLookupByLibrary.simpleMessage("Ostalo"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Ostali prihodi"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Značajke paketa"),
        "paid": MessageLookupByLibrary.simpleMessage("Plaćeno"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Plaćeni iznos"),
        "partyName": MessageLookupByLibrary.simpleMessage("Naziv stranke"),
        "partyType": MessageLookupByLibrary.simpleMessage("Vrsta stranke"),
        "password": MessageLookupByLibrary.simpleMessage("Lozinka"),
        "payCash": MessageLookupByLibrary.simpleMessage("Platite gotovinom"),
        "payable": MessageLookupByLibrary.simpleMessage("Plativo"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Iznos plaćanja"),
        "payment": MessageLookupByLibrary.simpleMessage("Plaćanje"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Plaćanje unutra"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Plaćanje van"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Vrsta plaćanja"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Vrste plaćanja"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Broj telefona"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verifikacija broja telefona"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Molimo dodajte prodaju"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Molimo dodajte klijenta"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Molimo provjerite vašu internetsku povezanost"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Molimo preuzmite našu mobilnu aplikaciju i pretplatite se na paket kako biste koristili desktop verziju"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Unesite zalihe proizvoda"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Unesite valjane podatke"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Molimo odaberite kupca"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Unesite valjane podatke"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Prijavna ploča"),
        "practies": MessageLookupByLibrary.simpleMessage("Prakse"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium korisnička podrška"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Pregled"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Prethodni dug:"),
        "price": MessageLookupByLibrary.simpleMessage("Cijena"),
        "print": MessageLookupByLibrary.simpleMessage("Ispis"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Ispiši račun"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Ispiši PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Pravila privatnosti"),
        "product": MessageLookupByLibrary.simpleMessage("Proizvod"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategorija proizvoda"),
        "productColor": MessageLookupByLibrary.simpleMessage("Boja proizvoda"),
        "productList": MessageLookupByLibrary.simpleMessage("Popis proizvoda"),
        "productName": MessageLookupByLibrary.simpleMessage("Naziv proizvoda"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Veličina proizvoda"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Zaliha proizvoda"),
        "productType": MessageLookupByLibrary.simpleMessage("Vrsta proizvoda"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Jedinica proizvoda"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Težina proizvoda"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapacitet proizvoda"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Uređivanje profila"),
        "profit": MessageLookupByLibrary.simpleMessage("Dobit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Dobit (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Dobit (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Kupnja"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Popis kupnji"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kupi Premium plan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Cijena kupnje"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcija kupovine"),
        "quantity": MessageLookupByLibrary.simpleMessage("Količina*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Ponuda"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Popis ponuda"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Nedavne Prodaje"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Primljeni iznos"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referentni broj"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referentni broj"),
        "registration": MessageLookupByLibrary.simpleMessage("Registracija"),
        "remaining": MessageLookupByLibrary.simpleMessage("Preostalo: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Preostali saldo"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Preostalo dugovanje"),
        "reports": MessageLookupByLibrary.simpleMessage("Izvještaji"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Poništi svoju lozinku"),
        "retailer": MessageLookupByLibrary.simpleMessage("Trgovac"),
        "revenue": MessageLookupByLibrary.simpleMessage("Prihod"),
        "right": MessageLookupByLibrary.simpleMessage("Desno"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Iznos Prodaje"),
        "sale": MessageLookupByLibrary.simpleMessage("Prodaja"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Iznos prodaje"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Detalji prodaje"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista prodaje"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Cijena prodaje*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Povrat prodaje"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcija prodaje"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transakcije prodaje (Povijest ponuda prodaje)"),
        "sales": MessageLookupByLibrary.simpleMessage("Prodaje"),
        "salesList": MessageLookupByLibrary.simpleMessage("Popis prodaje"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Spremi i objavi"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Spremi i objavi"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Spremi promjene"),
        "search": MessageLookupByLibrary.simpleMessage("Pretraži......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Pretraži bilo što..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Pretraži po računu..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Pretraži po računu ili imenu"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Pretraži po imenu"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Pretraži po imenu ili telefonu..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pretraži serijski broj"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Odaberi strane"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Odaberite marku proizvoda"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Odaberite serijski broj"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Odaberite varijacije:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Odaberite trajanje jamstva"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Odaberite svoj jezik"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Pošaljite poruku"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serijski broj"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serijski broj"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Trošak usluge"),
        "setting": MessageLookupByLibrary.simpleMessage("Postavke"),
        "share": MessageLookupByLibrary.simpleMessage("Podijeli"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Dostava/Ostalo"),
        "shopName": MessageLookupByLibrary.simpleMessage("Naziv trgovine"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Saldo otvaranja trgovine"),
        "show": MessageLookupByLibrary.simpleMessage("Prikaži >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Prikaz logotipa na računu?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Dostava/Usluga"),
        "size": MessageLookupByLibrary.simpleMessage("Veličina"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistika"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Ostanite na čelu tehnoloških napredaka bez dodatnih troškova. Naš Pos Saas POS neograničeni nadogradnja osigurava da uvijek imate najnovije alate i značajke na dohvat ruke, osiguravajući da vaš posao ostane napredan."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Ostanite na čelu tehnoloških napredaka bez dodatnih troškova. Naš Pos Sass POS neograničena nadogradnja osigurava da uvijek imate najnovije alate i značajke na dohvat ruke, osiguravajući da vaš posao ostane napredan."),
        "stock": MessageLookupByLibrary.simpleMessage("Zaliha"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventar zaliha"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Izvješće o zalihi"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Vrijednost zaliha"),
        "stockValues":
            MessageLookupByLibrary.simpleMessage("Vrijednost Zaliha"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Međuzbroj"),
        "subciption": MessageLookupByLibrary.simpleMessage("Pretplata"),
        "submit": MessageLookupByLibrary.simpleMessage("Potvrdi"),
        "supplier": MessageLookupByLibrary.simpleMessage("Dobavljači"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Dugovanje dobavljača"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Račun dobavljača"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lista dobavljača"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT kod"),
        "tSale": MessageLookupByLibrary.simpleMessage("Ukupna Prodaja"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Uzmite vozačku dozvolu, osobnu iskaznicu ili putovnicu"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Uvjeti korištenja"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Ime sve govori. S Pos Saas POS neograničenim, nema ograničenja u vašem korištenju. Bez obzira radi li se o obradi nekoliko transakcija ili naglom priljevu kupaca, možete raditi s povjerenjem, znajući da niste ograničeni."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Ovaj kupac nema nepodmirenih iznosa"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Ovaj kupac ima prethodna dugovanja"),
        "to": MessageLookupByLibrary.simpleMessage("Do"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Najprodavaniji proizvod"),
        "total": MessageLookupByLibrary.simpleMessage("ukupno"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Ukupni iznos"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Ukupni popust"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Ukupno dospjelo"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Ukupna dugovanja"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Ukupni troškovi"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Ukupni prihod"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Ukupno artikala: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Ukupan gubitak"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Ukupno plaćeno"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Ukupno za platiti"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Ukupno plaćanje van"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Ukupna cijena"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Ukupno proizvoda"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Ukupna dobit"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Ukupna kupovina"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Ukupni iznos povrata"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Ukupni povrati"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Ukupna prodaja"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Ukupna prodaja"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Ukupni PDV"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Ukupno plaćanje unutra"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transakcija"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID transakcije"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Izvješće o transakcijama"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Pokušajte ponovno"),
        "type": MessageLookupByLibrary.simpleMessage("Tip"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Nepodmireno"),
        "unit": MessageLookupByLibrary.simpleMessage("Jedinica"),
        "unitName": MessageLookupByLibrary.simpleMessage("Ime jedinice"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Jedinična cijena"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Neograničeno"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Neograničeni računi"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Neograničeno korištenje"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Otkrijte puni potencijal Pos Saas POS-a uz personalizirane trening sesije koje vodi naš stručni tim. Od osnovnih do naprednih tehnika, osiguravamo da ste dobro upućeni u korištenje svakog aspekta sustava kako biste optimizirali svoje poslovne procese."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Ažuriraj sada"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Prvo ažurirajte svoj plan\\nProdajni limit je premašen."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Ažuriranje putem mobilne aplikacije"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Prenesite sliku"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Prenesi logotip na račun"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Prijenos dokumenta"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Prijenos datoteke"),
        "userName": MessageLookupByLibrary.simpleMessage("Korisničko ime"),
        "userRole": MessageLookupByLibrary.simpleMessage("Uloga korisnika"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Naziv uloge korisnika"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Naziv korisnika"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("PDV/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Potvrdite broj telefona"),
        "view": MessageLookupByLibrary.simpleMessage("Pogledaj"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Šetnja kod kupca"),
        "warranty": MessageLookupByLibrary.simpleMessage("Jamstvo"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Jamstvo"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Morate registrirati svoj broj telefona prije nego što počnete!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Razumijemo važnost besprijekornog poslovanja. Zato je naša podrška dostupna 24 sata dnevno kako bismo vam pomogli, bilo da se radi o brzom upitu ili sveobuhvatnom pitanju. Povežite se s nama bilo kad i bilo gdje putem poziva ili WhatsApp-a kako biste doživjeli neusporedivu korisničku podršku."),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Veletrgovac"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Veleprodaja"),
        "wight": MessageLookupByLibrary.simpleMessage("Težina"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Da, vrati"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Morate se ponovo prijaviti na svoj račun."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Trebate provjeriti identitet prije kupovine poruka"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Lista svih vaših prodaja"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Sve vaše prodaje"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Koristite"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Vaše nepodmirene prodaje"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Trebate provjeriti identitet prije kupovine poruka"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Vaš paket"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Vaša uplata je otkazana"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Vaša uplata je uspješno obavljena"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Vaša uplata je otkazana")
      };
}
