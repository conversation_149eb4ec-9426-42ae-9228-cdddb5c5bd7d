// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("TAMBAHKAN PENJUALAN"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORI"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTUR"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Penjualan POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("HARGA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NAMA PRODUK"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Login panel"),
        "QTY": MessageLookupByLibrary.simpleMessage("KUANTITAS"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("NILAI TOTAL"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Judul Pengguna"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Tentang Aplikasi"),
        "accountName": MessageLookupByLibrary.simpleMessage("Nama Akun"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Nomor Akun"),
        "action": MessageLookupByLibrary.simpleMessage("Aksi"),
        "add": MessageLookupByLibrary.simpleMessage("Tambah"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Tambah Merek"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Tambah Kategori"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Tambah Pelanggan"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Tambahkan deskripsi...."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Tambahkan Dokumen"),
        "addItem": MessageLookupByLibrary.simpleMessage("Tambahkan Item"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Tambah Kategori Barang"),
        "addNew": MessageLookupByLibrary.simpleMessage("Tambah Baru"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Tambah Pengguna Baru"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Tambah Produk"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Berhasil Ditambahkan"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Tambah Pemasok"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Tambah Satuan"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Tambah/Perbarui Daftar Pengeluaran"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Tambah/Perbarui Daftar Pendapatan"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Tambah Peran Pengguna"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Tambah Nomor Seri?"),
        "address": MessageLookupByLibrary.simpleMessage("Alamat"),
        "all": MessageLookupByLibrary.simpleMessage("Semua"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Semua Fitur Dasar"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Sudah memiliki akun?"),
        "amount": MessageLookupByLibrary.simpleMessage("Jumlah"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Dukungan Aplikasi Android & iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin membuat Penawaran ini?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin menghapus Pelanggan ini?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin menghapus produk ini"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin menghapus Penawaran ini?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin mengembalikan penjualan ini?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Mata Uang Akun Bank"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Rekening Bank"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Informasi Bank"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nama Bank"),
        "between": MessageLookupByLibrary.simpleMessage("Antara"),
        "billTo": MessageLookupByLibrary.simpleMessage("Tagihan kepada:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Nama Cabang"),
        "brand": MessageLookupByLibrary.simpleMessage("Merek"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nama Merek"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Kategori Bisnis"),
        "buy": MessageLookupByLibrary.simpleMessage("Beli"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Beli Paket Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Beli SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Batal"),
        "capacity": MessageLookupByLibrary.simpleMessage("Kapasitas"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Tunai & Bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Tunai di Tangan"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategori"),
        "category": MessageLookupByLibrary.simpleMessage("Kategori"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Nama Kategori"),
        "changeAmount":
            MessageLookupByLibrary.simpleMessage("Jumlah Kembalian"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Jumlah yang Dapat Diubah"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Periksa Garansi"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Pilih rencana"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Pungut Jatuh Tempo >"),
        "color": MessageLookupByLibrary.simpleMessage("Warna"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Nama Perusahaan"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Alamat Perusahaan"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Deskripsi Perusahaan"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Alamat Email Perusahaan"),
        "companyName": MessageLookupByLibrary.simpleMessage("Nama Perusahaan"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Nomor Telepon Perusahaan"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("URL Website Perusahaan"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Konfirmasi kata sandi"),
        "continu": MessageLookupByLibrary.simpleMessage("Lanjutkan"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konversi ke Penjualan"),
        "create": MessageLookupByLibrary.simpleMessage("Buat"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Buat Pembayaran"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Dibuat oleh"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Pusat Kreatif"),
        "currency": MessageLookupByLibrary.simpleMessage("Mata Uang"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Rencana Saat Ini"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Pembranding Faktur Kustom"),
        "customer": MessageLookupByLibrary.simpleMessage("Pelanggan"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Hutang Pelanggan"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Faktur Pelanggan"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Daftar Pelanggan"),
        "customerName": MessageLookupByLibrary.simpleMessage("Nama Pelanggan"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Pelanggan Bulan Ini"),
        "customerType": MessageLookupByLibrary.simpleMessage("Jenis Pelanggan"),
        "customerWalkIncostomer": MessageLookupByLibrary.simpleMessage(
            "Pelanggan: Pelanggan Datang Langsung"),
        "customers": MessageLookupByLibrary.simpleMessage("Pelanggan"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Pengumpulan Harian"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Penjualan Harian"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transaksi Harian"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dasbor"),
        "date": MessageLookupByLibrary.simpleMessage("Tanggal"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Tanggal Waktu"),
        "dealer": MessageLookupByLibrary.simpleMessage("Pedagang"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Harga Grosir"),
        "delete": MessageLookupByLibrary.simpleMessage("Hapus"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Biaya Pengiriman"),
        "description": MessageLookupByLibrary.simpleMessage("Deskripsi"),
        "details": MessageLookupByLibrary.simpleMessage("Detail >"),
        "discount": MessageLookupByLibrary.simpleMessage("Diskon"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Harga Diskon"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Unduh PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Tertunggak"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Jumlah Jatuh Tempo"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Jumlah yang harus dibayar akan ditampilkan di sini jika tersedia"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Pengumpulan Tunggakan"),
        "dueList": MessageLookupByLibrary.simpleMessage("Daftar Tunggakan"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transaksi Hutang"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Edit/Tambah Serial:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Edit profil Anda"),
        "email": MessageLookupByLibrary.simpleMessage("Surel"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Masukkan Jumlah"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Merek"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Kategori"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Masukkan Deskripsi Perusahaan"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Masukkan Alamat Email Perusahaan"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Masukkan Nomor Telepon Perusahaan"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Masukkan URL website perusahaan"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Pelanggan"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Diskon"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Masukkan Kategori Pengeluaran"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage(
            "Masukkan Tanggal Pengeluaran"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Masukkan Kategori Pendapatan"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Masukkan Tanggal Pendapatan"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Produsen"),
        "enterName": MessageLookupByLibrary.simpleMessage("Masukkan Nama"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Masukkan Nama"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Masukkan Catatan"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Masukkan Saldo Awal"),
        "enterPaidAmount": MessageLookupByLibrary.simpleMessage(
            "Masukkan jumlah yang dibayar"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Kata Sandi"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Masukkan Jumlah Pembayaran"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Masukkan Harga"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Masukkan Kapasitas Produk"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Masukkan Kode Produk"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Masukkan Warna Produk"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Produk"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Masukkan Jumlah Produk"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Masukkan Ukuran Produk"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Masukkan Jenis Produk"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Masukkan Unit Produk"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Masukkan Berat Produk"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Beli"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Masukkan Nomor Referensi"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Jual"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Masukkan Nomor Seri"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Masukkan Konten Pesan"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Masukkan Jumlah Stok"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Masukkan ID Transaksi"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Satuan"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Masukkan Nama Peran Pengguna"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Masukkan judul pengguna"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Masukkan Garansi"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Masukkan Harga Grosir"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Masukkan jumlah Anda"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Masukkan Alamat Anda"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Masukkan Alamat Perusahaan Anda"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Masukkan Nama Perusahaan Anda"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Masukkan Nama Perusahaan Anda"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Masukkan alamat surel Anda"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Kata Sandi Anda"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Masukkan kata sandi Anda lagi"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Masukkan nomor telepon Anda"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Toko Anda"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Masukkan Nama Kategori"),
        "expense": MessageLookupByLibrary.simpleMessage("Pengeluaran"),
        "expenseDate":
            MessageLookupByLibrary.simpleMessage("Tanggal Pengeluaran"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detail Pengeluaran"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Pengeluaran Untuk"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Daftar Kategori Pengeluaran"),
        "expenses": MessageLookupByLibrary.simpleMessage("Pengeluaran"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Lima Produk Terlaris Bulan Ini"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Untuk Penggunaan Tak Terbatas"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Lupa Kata Sandi?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Pencadangan Data Gratis"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Pembaruan Gratis Seumur Hidup"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Paket Gratis"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Paket Gratis"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Mulai"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Identitas Pemerintah"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Total Keseluruhan"),
        "hold": MessageLookupByLibrary.simpleMessage("Tahan"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Nomor Tahan"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verifikasi Identitas"),
        "inc": MessageLookupByLibrary.simpleMessage("Pendapatan"),
        "income": MessageLookupByLibrary.simpleMessage("Pendapatan"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategori Pendapatan"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Daftar Kategori Pendapatan"),
        "incomeDate":
            MessageLookupByLibrary.simpleMessage("Tanggal Pendapatan"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detail Pendapatan"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Pendapatan Untuk"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Daftar Pendapatan"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Tambah Stok"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Privasi Instan"),
        "invoice": MessageLookupByLibrary.simpleMessage("Invoice"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Invoice:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Nomor Faktur.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Nomor Faktur"),
        "item": MessageLookupByLibrary.simpleMessage("Barang"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nama Barang"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Verifikasi KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Detail Buku Besar"),
        "ledger": MessageLookupByLibrary.simpleMessage("Buku Besar"),
        "left": MessageLookupByLibrary.simpleMessage("Kiri"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Akun Pinjaman"),
        "logOut": MessageLookupByLibrary.simpleMessage("Keluar"),
        "login": MessageLookupByLibrary.simpleMessage("Masuk"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Posisi Logo dalam Faktur?"),
        "loss": MessageLookupByLibrary.simpleMessage("Kerugian"),
        "lossOrProfit":
            MessageLookupByLibrary.simpleMessage("Kerugian/Keuntungan"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Kekurangan(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Stok Rendah"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Stok Rendah"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Buat kesan yang abadi pada pelanggan Anda dengan faktur bermerk. Pembaruan Tanpa Batas kami menawarkan keuntungan unik dalam menyesuaikan faktur Anda, menambahkan sentuhan profesional yang memperkuat identitas merek Anda dan memupuk loyalitas pelanggan."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Produsen"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Panel Masuk Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Panel Pendaftaran Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Aplikasi Seluler\n+\nDesktop"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Bukti Uang"),
        "nam": MessageLookupByLibrary.simpleMessage("Nama*"),
        "name": MessageLookupByLibrary.simpleMessage("Nama"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Pelanggan Baru"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Pelanggan Baru"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Pendapatan Baru"),
        "no": MessageLookupByLibrary.simpleMessage("Tidak"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Tidak Ada Koneksi"),
        "noCustomerFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Pelanggan Ditemukan"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Transaksi Jatuh Tempo Ditemukan"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Kategori Pengeluaran Ditemukan"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Kategori Pendapatan Ditemukan"),
        "noIncomeFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Pendapatan Ditemukan"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Tidak Ada Faktur Ditemukan"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Tidak Ada Produk Ditemukan"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Tidak ada transaksi pembelian ditemukan"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage(
            "Tidak ada Penawaran Ditemukan"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Tidak Ada Laporan Ditemukan"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Transaksi Penjualan Ditemukan"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Nomor Seri Ditemukan"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Tidak Ada Pemasok Ditemukan"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Transaksi Ditemukan"),
        "noUserFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Pengguna Ditemukan"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Tidak Ada Peran Pengguna Ditemukan"),
        "note": MessageLookupByLibrary.simpleMessage("Catatan"),
        "ok": MessageLookupByLibrary.simpleMessage("Oke"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Cek Terbuka"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("Saldo Awal"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " atau seret & letakkan PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Pesanan"),
        "other": MessageLookupByLibrary.simpleMessage("Lainnya"),
        "otherIncome":
            MessageLookupByLibrary.simpleMessage("Pendapatan Lainnya"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("Fitur Paket"),
        "paid": MessageLookupByLibrary.simpleMessage("Dibayar"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Jumlah Dibayar"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nama Partai"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tipe Partai"),
        "password": MessageLookupByLibrary.simpleMessage("Kata Sandi"),
        "payCash": MessageLookupByLibrary.simpleMessage("Bayar Tunai"),
        "payable":
            MessageLookupByLibrary.simpleMessage("Jumlah yang Harus Dibayar"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Jumlah Pembayaran"),
        "payment": MessageLookupByLibrary.simpleMessage("Pembayaran"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Pembayaran Masuk"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Pembayaran Keluar"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Jenis Pembayaran"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Jenis Pembayaran"),
        "phone": MessageLookupByLibrary.simpleMessage("Telepon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Nomor Telepon"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verifikasi Nomor Telepon"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Silakan Tambahkan Penjualan"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Harap Tambahkan Pelanggan"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Harap Periksa Koneksi Internet Anda"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Harap unduh aplikasi seluler kami dan berlangganan paket untuk menggunakan versi desktop"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Silakan masukkan stok produk"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Silakan masukkan data yang valid"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Silakan Pilih Pelanggan"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Silakan masukkan data yang valid"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas SingUp Panel"),
        "practies": MessageLookupByLibrary.simpleMessage("Praktik"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Dukungan Pelanggan Premium"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Paket Premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Pratinjau"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Saldo Sebelumnya:"),
        "price": MessageLookupByLibrary.simpleMessage("Harga"),
        "print": MessageLookupByLibrary.simpleMessage("Cetak"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Cetak Faktur"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Cetak PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Kebijakan Privasi"),
        "product": MessageLookupByLibrary.simpleMessage("Produk"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategori Produk"),
        "productColor": MessageLookupByLibrary.simpleMessage("Warna Produk"),
        "productList": MessageLookupByLibrary.simpleMessage("Daftar Produk"),
        "productName": MessageLookupByLibrary.simpleMessage("Nama Produk"),
        "productSize": MessageLookupByLibrary.simpleMessage("Ukuran Produk"),
        "productStock": MessageLookupByLibrary.simpleMessage("Stok Produk"),
        "productType": MessageLookupByLibrary.simpleMessage("Jenis Produk"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Satuan Produk"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Berat Produk"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Kapasitas Produk"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Sunting Profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Keuntungan"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Keuntungan(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Keuntungan(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Pembelian"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Daftar Pembelian"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Beli Paket Premium"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Harga Beli"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transaksi Pembelian"),
        "quantity": MessageLookupByLibrary.simpleMessage("Jumlah*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Penawaran"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Daftar Penawaran"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Penjualan Terbaru"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("No. Referensi"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Nomor Referensi"),
        "registration": MessageLookupByLibrary.simpleMessage("Pendaftaran"),
        "remaining": MessageLookupByLibrary.simpleMessage("Sisa: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo Tersisa"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Sisa Hutang"),
        "reports": MessageLookupByLibrary.simpleMessage("Laporan"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Atur Ulang Kata Sandi Anda"),
        "retailer": MessageLookupByLibrary.simpleMessage("Pengecer"),
        "revenue": MessageLookupByLibrary.simpleMessage("Pendapatan"),
        "right": MessageLookupByLibrary.simpleMessage("Kanan"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Jumlah Penjualan"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Lindungi data bisnis Anda dengan mudah. Peningkatan Tidak Terbatas Pos Saas POS kami mencakup pencadangan data gratis, memastikan informasi berharga Anda terlindungi dari kejadian tak terduga. Fokus pada hal yang benar-benar penting – pertumbuhan bisnis Anda."),
        "sale": MessageLookupByLibrary.simpleMessage("Penjualan"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Jumlah Penjualan"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Detail Penjualan"),
        "saleList": MessageLookupByLibrary.simpleMessage("Daftar Penjualan"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Harga Jual"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Harga Jual*"),
        "saleReturn":
            MessageLookupByLibrary.simpleMessage("Pengembalian Penjualan"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transaksi Penjualan"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transaksi Penjualan (Riwayat Penjualan Penawaran)"),
        "sales": MessageLookupByLibrary.simpleMessage("Penjualan"),
        "salesList": MessageLookupByLibrary.simpleMessage("Daftar Penjualan"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Simpan & Terbitkan"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Simpan & Terbitkan"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Simpan Perubahan"),
        "search": MessageLookupByLibrary.simpleMessage("Cari......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Cari Apapun..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Cari berdasarkan faktur...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Cari berdasarkan invoice atau nama"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Cari Berdasarkan Nama"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Cari berdasarkan Nama atau Telepon..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Cari Nomor Seri"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Pilih Pihak"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Pilih Merek Produk"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Pilih Nomor Seri"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Pilih Variasi:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Pilih Waktu Garansi"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Pilih bahasa Anda"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Kirim Pesan"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Nomor Seri"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Nomor Seri"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Biaya Layanan"),
        "setting": MessageLookupByLibrary.simpleMessage("Pengaturan"),
        "share": MessageLookupByLibrary.simpleMessage("Bagikan"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Pengiriman/Lainnya"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nama Toko"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Saldo Awal Toko"),
        "show": MessageLookupByLibrary.simpleMessage("Tampilkan >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Tampilkan Logo dalam Faktur?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Pengiriman/Layanan"),
        "size": MessageLookupByLibrary.simpleMessage("Ukuran"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistik"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Tetap di garis depan perkembangan teknologi tanpa biaya tambahan. Pembaruan Pos Saas POS Tanpa Batas kami memastikan Anda selalu memiliki alat dan fitur terbaru di ujung jari Anda, menjamin bisnis Anda tetap berada di garis depan."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Tetap di garis depan perkembangan teknologi tanpa biaya tambahan. Pembaruan Pos Saas POS Tanpa Batas kami memastikan Anda selalu memiliki alat dan fitur terbaru di ujung jari Anda, menjamin bisnis Anda tetap berada di garis depan."),
        "stock": MessageLookupByLibrary.simpleMessage("Stok"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventaris Stok"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Laporan Stok"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Nilai Stok"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Nilai Stok"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
        "submit": MessageLookupByLibrary.simpleMessage("Kirim"),
        "supplier": MessageLookupByLibrary.simpleMessage("Pemasok"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Hutang Pemasok"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Faktur Pemasok"),
        "supplierList": MessageLookupByLibrary.simpleMessage("Daftar Pemasok"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Kode SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Total Penjualan"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Ambil foto SIM, kartu identitas nasional, atau paspor"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Syarat Penggunaan"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Nama mengatakannya semua. Dengan Pos Saas POS Tanpa Batas, tidak ada batasan penggunaan. Baik Anda memproses sejumlah transaksi atau menghadapi lonjakan pelanggan, Anda dapat beroperasi dengan percaya diri, tahu Anda tidak dibatasi oleh batasan."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Pelanggan ini tidak memiliki tunggakan"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Pelanggan ini memiliki tunggakan sebelumnya"),
        "to": MessageLookupByLibrary.simpleMessage("Hingga"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Produk Terlaris"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Jumlah Total"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Total Diskon"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total Jatuh Tempo"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Total Hutang"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("Total Pengeluaran"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Total Pendapatan"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Total Barang : 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Total Kerugian"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Total Dibayar"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total Dibayar"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Total Pembayaran Keluar"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Harga Total"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Total Produk"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Total Keuntungan"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Total Pembelian"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Jumlah Total Pengembalian"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Total Pengembalian"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total Penjualan"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Total Penjualan"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total PPN"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Total Pembayaran Masuk"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaksi"),
        "transactionId": MessageLookupByLibrary.simpleMessage("ID Transaksi"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Laporan Transaksi"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Coba Lagi"),
        "type": MessageLookupByLibrary.simpleMessage("Tipe"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Belum Dibayar"),
        "unit": MessageLookupByLibrary.simpleMessage("Unit"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nama Satuan"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Harga Satuan"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Tak Terbatas"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Faktur Tanpa Batas"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Penggunaan Tanpa Batas"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Buka potensi penuh Pos Saas POS dengan sesi pelatihan yang dipersonalisasi yang dipimpin oleh tim ahli kami. Mulai dari dasar hingga teknik lanjutan, kami memastikan Anda terampil dalam memanfaatkan setiap aspek sistem untuk mengoptimalkan proses bisnis Anda."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Perbarui Sekarang"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Perbarui rencana Anda terlebih dahulu.\\nBatas Penjualan habis."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Upgrade Melalui Aplikasi Seluler"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Unggah gambar"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Unggah Logo Faktur"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Unggah Dokumen"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Unggah Berkas"),
        "userName": MessageLookupByLibrary.simpleMessage("Nama Pengguna"),
        "userRole": MessageLookupByLibrary.simpleMessage("Peran Pengguna"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nama Peran Pengguna"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Judul Pengguna"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage(
            "Pajak Nilai Tambah (VAT)/Pajak Penjualan Barang dan Jasa (GST)"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifikasi Nomor Telepon"),
        "view": MessageLookupByLibrary.simpleMessage("Lihat"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Pelanggan Datang Langsung"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garansi"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garansi"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Kami perlu mendaftarkan nomor telepon Anda sebelum memulai!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Kami memahami pentingnya operasi yang lancar. Itulah mengapa dukungan kami sepanjang waktu tersedia untuk membantu Anda, baik itu pertanyaan cepat atau masalah komprehensif. Hubungi kami kapan saja, di mana saja melalui panggilan atau WhatsApp untuk mengalami layanan pelanggan yang tak tertandingi."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Harga Eceran"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Pedagang Besar"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Grosir"),
        "wight": MessageLookupByLibrary.simpleMessage("Berat"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ya, Kembalikan"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Anda harus LOGOUT dan LOGIN kembali ke akun Anda."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Anda perlu verifikasi identitas sebelum membeli pesan"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Daftar Seluruh Penjualan Anda"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Seluruh Penjualan Anda"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Anda menggunakan"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Penjualan Tertunda Anda"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Anda perlu verifikasi identitas sebelum membeli pesan"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Paket Anda"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Pembayaran Anda dibatalkan"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Pembayaran Anda berhasil"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Pembayaran Anda dibatalkan")
      };
}
