// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a mr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'mr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("विक्री जोडा"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("श्रेणी"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("विवरणपत्र"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS विक्री"),
        "PRICE": MessageLookupByLibrary.simpleMessage("किंमत"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("उत्पादन नाव"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas लॉगिन पॅनल"),
        "QTY": MessageLookupByLibrary.simpleMessage("QTY"),
        "Quantity": MessageLookupByLibrary.simpleMessage("प्रमाण*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("स्थिती"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("एकूण मूल्य"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("वापरकर्ता शीर्षक"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("अॅपबद्दल"),
        "accountName": MessageLookupByLibrary.simpleMessage("खात्याचे नाव"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("खाते क्रमांक"),
        "action": MessageLookupByLibrary.simpleMessage("कारवाई"),
        "add": MessageLookupByLibrary.simpleMessage("जोडा"),
        "addBrand": MessageLookupByLibrary.simpleMessage("ब्रँड जोडा"),
        "addCategory": MessageLookupByLibrary.simpleMessage("श्रेणी जोडा"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("ग्राहक जोडा"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("वर्णन जोडा...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("दस्तऐवज जोडा"),
        "addItem": MessageLookupByLibrary.simpleMessage("आइटम जोडा"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("आयटम श्रेणी जोडा"),
        "addNew": MessageLookupByLibrary.simpleMessage("नवीन जोडा"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("नवीन वापरकर्ता जोडा"),
        "addProduct": MessageLookupByLibrary.simpleMessage("उत्पादन जोडा"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("सफळतेच जोडले"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("पुरवठादार जोडा"),
        "addUnit": MessageLookupByLibrary.simpleMessage("युनिट जोडा"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("खर्च यादी जोडा/अपडेट करा"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "उत्पन्न यादी जोडा/अद्यतनित करा"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("वापरकर्ता भूमिका जोडा"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("सीरियल नंबर जोडत आहे?"),
        "address": MessageLookupByLibrary.simpleMessage("पत्ता"),
        "all": MessageLookupByLibrary.simpleMessage("सर्व"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("सर्व मूलभूत वैशिष्ट्ये"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("आधीपासूनच खाते आहे?"),
        "amount": MessageLookupByLibrary.simpleMessage("रक्कम"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Android & iOS App Support"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "तुम्हाला हे कोटेशन तयार करायचे आहे का?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "तुम्हाला हा ग्राहक हटवायचा आहे का?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "काही योग्यता आहे का ही उत्पादन काढायला"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "या उद्धरणाची विल्हेवाट लावू इच्छिता?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "आपण या विक्रीस परत करू इच्छिता?"),
        "balance": MessageLookupByLibrary.simpleMessage("शिल्लक"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("बँक खाते"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("बँक माहिती"),
        "bankName": MessageLookupByLibrary.simpleMessage("बँकचे नाव"),
        "between": MessageLookupByLibrary.simpleMessage("मध्ये"),
        "billTo": MessageLookupByLibrary.simpleMessage("बिल टू:"),
        "branchName": MessageLookupByLibrary.simpleMessage("शाखेचे नाव"),
        "brand": MessageLookupByLibrary.simpleMessage("ब्रँड"),
        "brandName": MessageLookupByLibrary.simpleMessage("ब्रँडचे नाव"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("व्यवसाय श्रेणी"),
        "buy": MessageLookupByLibrary.simpleMessage("खरेदी करा"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("प्रीमियम योजना खरेदी करा"),
        "buySms": MessageLookupByLibrary.simpleMessage("संदेश खरेदी करा"),
        "calculator": MessageLookupByLibrary.simpleMessage("कॅल्क्युलेटर:"),
        "camera": MessageLookupByLibrary.simpleMessage("कॅमेरा"),
        "cancel": MessageLookupByLibrary.simpleMessage("रद्द करा"),
        "capacity": MessageLookupByLibrary.simpleMessage("क्षमता"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("नागद आणि बँक"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("हातातील रोख"),
        "categories": MessageLookupByLibrary.simpleMessage("श्रेण्या"),
        "category": MessageLookupByLibrary.simpleMessage("श्रेणी"),
        "categoryName": MessageLookupByLibrary.simpleMessage("श्रेणीचे नाव"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("बदली रक्कम"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("बदलण्यायोग्य रक्कम"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("वॉरंटी तपासा"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("एक योजना निवडा"),
        "collectDue": MessageLookupByLibrary.simpleMessage("देय जमा करा >"),
        "color": MessageLookupByLibrary.simpleMessage("रंग"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("कंपनीचे नाव"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("कंपनीचा पत्ता"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("कंपनीचे वर्णन"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("कंपनीचा ईमेल पत्ता"),
        "companyName": MessageLookupByLibrary.simpleMessage("कंपनीचे नाव"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("कंपनीचा दूरध्वनी क्रमांक"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("कंपनीचा वेबसाईट URL"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड पुष्टी करा"),
        "continu": MessageLookupByLibrary.simpleMessage("सुरू ठेवा"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("विक्रीमध्ये रूपांतरित करा"),
        "create": MessageLookupByLibrary.simpleMessage("निर्माण"),
        "createPayment": MessageLookupByLibrary.simpleMessage("पैसे भरा"),
        "createdBy": MessageLookupByLibrary.simpleMessage("निर्मित केली"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("क्रिएटिव्ह हब"),
        "currency": MessageLookupByLibrary.simpleMessage("मुद्रा"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("चालू योजना"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Custom Invoice Branding"),
        "customer": MessageLookupByLibrary.simpleMessage("ग्राहक"),
        "customerDue": MessageLookupByLibrary.simpleMessage("ग्राहक देय"),
        "customerInvoices": MessageLookupByLibrary.simpleMessage("ग्राहक बिल"),
        "customerList": MessageLookupByLibrary.simpleMessage("ग्राहक यादी"),
        "customerName": MessageLookupByLibrary.simpleMessage("ग्राहकाचे नाव"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("महिन्याच्या ग्राहक"),
        "customerType": MessageLookupByLibrary.simpleMessage("ग्राहक प्रकार"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("ग्राहक: walk-in ग्राहक"),
        "customers": MessageLookupByLibrary.simpleMessage("ग्राहक"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("दैनिक संग्रह"),
        "dailySales": MessageLookupByLibrary.simpleMessage("दैनिक विक्री"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("दैनिक व्यवहार"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("डॅशबोर्ड"),
        "date": MessageLookupByLibrary.simpleMessage("दिनांक"),
        "dateTime": MessageLookupByLibrary.simpleMessage("तारीख वेळ"),
        "dealer": MessageLookupByLibrary.simpleMessage("खळा"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("खळा किंमत"),
        "delete": MessageLookupByLibrary.simpleMessage("हटवा"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("वितरण शुल्क"),
        "description": MessageLookupByLibrary.simpleMessage("वर्णन"),
        "details": MessageLookupByLibrary.simpleMessage("तपशील >"),
        "discount": MessageLookupByLibrary.simpleMessage("सवलत"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("किंमत सूट"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF डाउनलोड करा"),
        "due": MessageLookupByLibrary.simpleMessage("देय"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("देय रक्कम"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "due रक्कम येथे दर्शविली जाईल"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("देय वसुली"),
        "dueList": MessageLookupByLibrary.simpleMessage("देय यादी"),
        "dueTransaction": MessageLookupByLibrary.simpleMessage("देय व्यवहार"),
        "edit": MessageLookupByLibrary.simpleMessage("संपादित करा"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("संपादित करा/सीरियल जोडा:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("आपली प्रोफाइल संपादित करा"),
        "email": MessageLookupByLibrary.simpleMessage("इमेल"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("रकम प्रविष्ट करा"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("ब्रँडचे नाव टाका"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("श्रेणीचे नाव प्रविष्ट करा"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("कंपनीचे वर्णन प्रविष्ट करा"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "कंपनीचा ईमेल पत्ता प्रविष्ट करा"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "कंपनीचा दूरध्वनी क्रमांक प्रविष्ट करा"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "कंपनीचा वेबसाईट URL प्रविष्ट करा"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("ग्राहकाचे नाव प्रविष्ट करा"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("खळा किंमत टाका"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("किंमत सूट प्रविष्ट करा"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("खर्च श्रेणी प्रविष्ट करा"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("खर्च तारीख प्रविष्ट करा"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("उत्पन्न श्रेणी प्रविष्ट करा"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("उत्पन्न दिनांक प्रविष्ट करा"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("निर्मात्याचे नाव टाका"),
        "enterName": MessageLookupByLibrary.simpleMessage("नाव प्रविष्ट करा"),
        "enterNames": MessageLookupByLibrary.simpleMessage("नाव प्रविष्ट करा"),
        "enterNote": MessageLookupByLibrary.simpleMessage("नोट प्रविष्ट करा"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "उघडण्याचे शिल्लक प्रविष्ट करा"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("देय रक्कम प्रविष्ट करा"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड प्रविष्ट करा"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("देय रक्कम प्रविष्ट करा"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("किंमत टाका"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("उत्पादन क्षमता प्रविष्ट करा"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("उत्पादन कोड टाका"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("उत्पादन रंग प्रविष्ट करा"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("उत्पादनाचे नाव टाका"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("उत्पादन प्रमाण टाका"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("उत्पादन आकार प्रविष्ट करा"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("उत्पादन प्रकार टाका"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("उत्पादन एकक प्रविष्ट करा"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("उत्पादन वजन प्रविष्ट करा"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("खरेदी किंमत टाका"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("संदर्भ क्रमांक प्रविष्ट करा"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("विक्री किंमत प्रविष्ट करा"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("आधारक्रम क्रमांक टाका"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("संदेश सामग्री प्रविष्ट करा"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("स्टॉक रक्कम प्रविष्ट करा"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "क्रियाकलाप आयडी प्रविष्ट करा"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("युनिटचे नाव टाका"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "वापरकर्ता भूमिका नाव प्रविष्ट करा"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "वापरकर्ता शीर्षक प्रविष्ट करा"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("वारंटी टाका"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("थोक किंमत प्रविष्ट करा"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("तुमची रक्कम प्रविष्ट करा"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("तुमचा पत्ता प्रविष्ट करा"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "आपला कंपनीचा पत्ता प्रविष्ट करा"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "तुमचे कंपनीचे नाव प्रविष्ट करा"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "तुमचे कंपनीचे नाव प्रविष्ट करा"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "तुमचा ईमेल पत्ता प्रविष्ट करा"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("तुमचा पासवर्ड प्रविष्ट करा"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "आपला पासवर्ड पुन्हा प्रविष्ट करा"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "तुमचा दूरध्वनी क्रमांक प्रविष्ट करा"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "तुमचे दुकानाचे नाव प्रविष्ट करा"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("श्रेणीचे नाव प्रविष्ट करा"),
        "expense": MessageLookupByLibrary.simpleMessage("खर्च"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("खर्च तारीख"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("खर्च तपशील"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("खर्चासाठी"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("खर्च श्रेणी यादी"),
        "expenses": MessageLookupByLibrary.simpleMessage("व्यय"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "महिन्याच्या पंधराव्या महिन्याच्या शीर्ष पंधराव्या खरेदी उत्पाद"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("अमर्याद वापरासाठी"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड विसरलात?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Free Data Backup"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Free Lifetime Update"),
        "freePackage": MessageLookupByLibrary.simpleMessage("मोफत पॅकेज"),
        "freePlan": MessageLookupByLibrary.simpleMessage("मोफत योजना"),
        "getStarted": MessageLookupByLibrary.simpleMessage("सुरुवात करा"),
        "govermentId": MessageLookupByLibrary.simpleMessage("सरकारी ओळखपत्र"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("एकूण रक्कम"),
        "hold": MessageLookupByLibrary.simpleMessage("होल्ड"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("होल्ड क्रमांक"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("पहचान सत्यापित करा"),
        "inc": MessageLookupByLibrary.simpleMessage("आय"),
        "income": MessageLookupByLibrary.simpleMessage("उपजनी"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("उत्पन्न श्रेणी"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("उत्पन्न श्रेणी यादी"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("उत्पन्न दिनांक"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("उत्पन्न तपशील"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("उत्पन्नासाठी"),
        "incomeList": MessageLookupByLibrary.simpleMessage("उत्पन्न यादी"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("स्टॉक वाढवा"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("तात्काळ गोपनीयता"),
        "invoice": MessageLookupByLibrary.simpleMessage("इनव्हॉइस"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("इनव्हॉइस:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("बिल क्रमांक..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("विवरणपत्र क्रमांक"),
        "item": MessageLookupByLibrary.simpleMessage("आइटम"),
        "itemName": MessageLookupByLibrary.simpleMessage("आयटमचे नाव"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("KYC सत्यापन"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("लेजर तपशील"),
        "ledger": MessageLookupByLibrary.simpleMessage("लेजर"),
        "left": MessageLookupByLibrary.simpleMessage("डावे"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("कर्ज खाते"),
        "logOut": MessageLookupByLibrary.simpleMessage("बाहेर पडा"),
        "login": MessageLookupByLibrary.simpleMessage("लॉग इन करा"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("इनव्हॉइसमध्ये लोगोचा स्थान?"),
        "loss": MessageLookupByLibrary.simpleMessage("तोटा"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("तोटा/नफा"),
        "lossminus": MessageLookupByLibrary.simpleMessage("तोटा(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("कमी स्टॉक"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("कमी स्टॉक"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("निर्माता"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas लॉगिन पॅनल"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas साइनअप पॅनल"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("मोबाइल अॅप\n+\nडेस्कटॉप"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("पैसे पावती"),
        "nam": MessageLookupByLibrary.simpleMessage("नाव*"),
        "name": MessageLookupByLibrary.simpleMessage("नाव"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("नाव, कोड किंवा श्रेणी"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("नवीन ग्राहक"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("नवीन ग्राहक"),
        "newIncome": MessageLookupByLibrary.simpleMessage("नवीन उत्पन्न"),
        "no": MessageLookupByLibrary.simpleMessage("नाही"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("कोणत्याही कनेक्शन नाही"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("कोणताही ग्राहक सापडला नाही"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "कोणताही देय व्यवहार सापडला नाही"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "कोणताही खर्च श्रेणी सापडला नाही"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("उत्पन्न श्रेणी आढळली नाही"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("उत्पन्न आढळले नाही"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("कोणतेही बिल सापडले नाही"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("कोणतेही उत्पादन सापडले नाही"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "कोणतेही खरेदी व्यवहार सापडले नाहीत"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("कोणतेही उद्धरण सापडले नाही"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("कोणताही अहवाल सापडला नाही"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("विक्री व्यवहार आढळले नाही"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "कोणताही अनुक्रमांक सापडला नाही"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "कोणताही पुरवठादार आढळला नाही"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "कोणतेही व्यवहार सापडले नाहीत"),
        "noUserFound": MessageLookupByLibrary.simpleMessage(
            "कोणतेही वापरकर्ता सापडले नाहीत"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "कोणत्याही वापरकर्ता भूमिका सापडली नाही"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "कोणताही आधारक्रम क्रमांक सापडला नाही"),
        "note": MessageLookupByLibrary.simpleMessage("टिप"),
        "ok": MessageLookupByLibrary.simpleMessage("ठीक आहे"),
        "openCheques": MessageLookupByLibrary.simpleMessage("खुले चेक"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("उघडण्याचे शिल्लक"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " किंवा PNG, JPG ड्रॅग आणि ड्रॉप करा"),
        "orders": MessageLookupByLibrary.simpleMessage("ऑर्डर्स"),
        "other": MessageLookupByLibrary.simpleMessage("इतर"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("इतर उत्पन्न"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("पॅकेज वैशिष्ट्य"),
        "paid": MessageLookupByLibrary.simpleMessage("देय"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("देय रक्कम"),
        "partyName": MessageLookupByLibrary.simpleMessage("पक्षाचे नाव"),
        "partyType": MessageLookupByLibrary.simpleMessage("पक्ष प्रकार"),
        "password": MessageLookupByLibrary.simpleMessage("पासवर्ड"),
        "payCash": MessageLookupByLibrary.simpleMessage("रोख पैसे द्या"),
        "payable": MessageLookupByLibrary.simpleMessage("देय"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("देय रक्कम"),
        "payment": MessageLookupByLibrary.simpleMessage("पैसे देणे"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("पैसे देणे"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("पैसे देणे"),
        "paymentType":
            MessageLookupByLibrary.simpleMessage("पैसे देण्याची पद्धत"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("पैसे देण्याच्या प्रकारा"),
        "phone": MessageLookupByLibrary.simpleMessage("दूरध्वनी"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("दूरध्वनी क्रमांक"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("फोन सत्यापन"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("कृपया विक्री जोडा"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("कृपया ग्राहक जोडा"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "कृपया आपल्या इंटरनेट कनेक्टिव्हिटीची तपास करा"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "कृपया आमचे मोबाइल अॅप डाउनलोड करा आणि डेस्कटॉप आवृत्ती वापरण्यासाठी पॅकेजची सदस्यता घ्या"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "कृपया उत्पादन स्टॉक प्रविष्ट करा"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("कृपया वैध डेटा प्रविष्ट करा"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("कृपया ग्राहक निवडा"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("कृपया वैध डेटा प्रविष्ट करा"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas साइन अप पॅनल"),
        "practies": MessageLookupByLibrary.simpleMessage("अभ्यास"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Customer Support"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("प्रीमियम योजना"),
        "preview": MessageLookupByLibrary.simpleMessage("पूर्वावलोकन"),
        "previousDue": MessageLookupByLibrary.simpleMessage("मागील देय:"),
        "price": MessageLookupByLibrary.simpleMessage("किंमत"),
        "print": MessageLookupByLibrary.simpleMessage("छापणे"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("विवरणपत्र मुद्रित करा"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF मुद्रित करा"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("गोपनीयता धोरण"),
        "product": MessageLookupByLibrary.simpleMessage("उत्पादन"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("उत्पादन श्रेणी"),
        "productCod": MessageLookupByLibrary.simpleMessage("उत्पादन कोड*"),
        "productColor": MessageLookupByLibrary.simpleMessage("उत्पादन रंग"),
        "productList": MessageLookupByLibrary.simpleMessage("उत्पादन यादी"),
        "productNam": MessageLookupByLibrary.simpleMessage("उत्पादनाचे नाव*"),
        "productName": MessageLookupByLibrary.simpleMessage("उत्पादनाचे नाव"),
        "productSize": MessageLookupByLibrary.simpleMessage("उत्पादन आकार"),
        "productStock": MessageLookupByLibrary.simpleMessage("उत्पादन स्टॉक"),
        "productType": MessageLookupByLibrary.simpleMessage("उत्पादन प्रकार"),
        "productUnit": MessageLookupByLibrary.simpleMessage("उत्पादन युनिट"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("उत्पादन वॉरंटी"),
        "productWeight": MessageLookupByLibrary.simpleMessage("उत्पादन वजन"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("उत्पादन क्षमता"),
        "prof": MessageLookupByLibrary.simpleMessage("प्रोफाइल"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("प्रोफाइल संपादन"),
        "profit": MessageLookupByLibrary.simpleMessage("नफा"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("नफा (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("नफा (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("खरेदी"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("खरेदी यादी"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("प्रीमियम योजना खरेदी करा"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("खरेदी किंमत"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("खरेदी व्यवहार"),
        "quantity": MessageLookupByLibrary.simpleMessage("quantity"),
        "quotation": MessageLookupByLibrary.simpleMessage("कोटेशन"),
        "quotationList": MessageLookupByLibrary.simpleMessage("उद्धरण यादी"),
        "recentSale": MessageLookupByLibrary.simpleMessage("नवीन विक्रय"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("प्राप्त रक्कम"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("संदर्भ क्रमांक"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("संदर्भ क्रमांक"),
        "registration": MessageLookupByLibrary.simpleMessage("नोंदणी"),
        "remaining": MessageLookupByLibrary.simpleMessage("शेष: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("उर्वरित शिल्लक"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("बाकी देय"),
        "reports": MessageLookupByLibrary.simpleMessage("अहवाल"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("आपला पासवर्ड रीसेट करा"),
        "retailer": MessageLookupByLibrary.simpleMessage("खुदरा विक्रेता"),
        "revenue": MessageLookupByLibrary.simpleMessage("राजस्व"),
        "right": MessageLookupByLibrary.simpleMessage("उजवे"),
        "sAmount": MessageLookupByLibrary.simpleMessage("विक्रय मूल्य"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Safeguard your business data effortlessly. Our Pos Saas POS Unlimited Upgrade includes free data backup, ensuring your valuable information is protected against any unforeseen events. Focus on what truly matters - your business growth."),
        "sale": MessageLookupByLibrary.simpleMessage("विक्री"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("विक्री रक्कम"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("विक्री तपशील"),
        "saleList": MessageLookupByLibrary.simpleMessage("विक्री यादी"),
        "salePrice": MessageLookupByLibrary.simpleMessage("विक्री किंमत"),
        "salePrices": MessageLookupByLibrary.simpleMessage("विक्री किंमत*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("विक्री परतावा"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("विक्री व्यवहार"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "विक्री व्यवहार (उद्धरण विक्री इतिहास)"),
        "sales": MessageLookupByLibrary.simpleMessage("विक्री"),
        "salesList": MessageLookupByLibrary.simpleMessage("विपणी सूची"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("सेव्ह करा आणि प्रकाशित करा"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("सेव्ह करा आणि प्रकाशित करा"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("बदल जतन करा"),
        "search":
            MessageLookupByLibrary.simpleMessage("शोध ..................."),
        "searchAnyThing": MessageLookupByLibrary.simpleMessage("काहीही शोध..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("इनवॉइसद्वारे शोध...."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("इनव्हॉइस किंवा नावाने शोधा"),
        "searchByName": MessageLookupByLibrary.simpleMessage("नावाने शोधा"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "नाव किंवा फोन क्रमांकाने शोध..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("अनुक्रमांक शोधा"),
        "selectParties": MessageLookupByLibrary.simpleMessage("पक्ष निवडा"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("उत्पादन ब्रँड निवडा"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("अनुक्रमांक निवडा"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("भिन्नता निवडा:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("वारंटी कालावधी निवडा"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("आपला भाषा निवडा"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("संदेश पाठवा"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("अनुक्रमांक"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("आधारक्रम क्रमांक"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("सेवा शुल्क"),
        "setting": MessageLookupByLibrary.simpleMessage("सेटिंग"),
        "share": MessageLookupByLibrary.simpleMessage("साझा करा"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("शिपिंग/इतर"),
        "shopName": MessageLookupByLibrary.simpleMessage("दुकानाचे नाव"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("दुकान उघडण्याचे शिल्लक"),
        "show": MessageLookupByLibrary.simpleMessage("दाखवा >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("इनव्हॉइसमध्ये लोगो दर्शवा?"),
        "shpingOrServices": MessageLookupByLibrary.simpleMessage("शिपिंग/सेवा"),
        "size": MessageLookupByLibrary.simpleMessage("आकार"),
        "statistic": MessageLookupByLibrary.simpleMessage("सांख्यिकी"),
        "status": MessageLookupByLibrary.simpleMessage("स्थिती"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Stay at the forefront of technological advancements without any extra costs. Our Pos Saas POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "कोणत्याही अतिरिक्त खर्चाशिवाय तंत्रज्ञानाच्या आघाडीस असा राहा. आमच्या Pos Sass POS अमर्याद अपग्रेडने सुन्दर आपल्याला आपल्या हातात नवीन उपकरणे आणि सुविधे असल्याचं सुनिश्चित करतात, तसे की आपला व्यवसाय काटिण्याच्या अवस्थेत राहील."),
        "stock": MessageLookupByLibrary.simpleMessage("स्टॉक"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("स्टॉक इन्व्हेंटरी"),
        "stockReport": MessageLookupByLibrary.simpleMessage("स्टॉक अहवाल"),
        "stockValue": MessageLookupByLibrary.simpleMessage("स्टॉक मूल्य"),
        "stockValues": MessageLookupByLibrary.simpleMessage("स्टॉक मूल्ये"),
        "subTotal": MessageLookupByLibrary.simpleMessage("उप-योग"),
        "subciption": MessageLookupByLibrary.simpleMessage("सदस्यता"),
        "submit": MessageLookupByLibrary.simpleMessage("सादर करा"),
        "supplier": MessageLookupByLibrary.simpleMessage("पुरवठादार"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("पुरवठादार देय"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("पुरवठादार बिल"),
        "supplierList": MessageLookupByLibrary.simpleMessage("पुरवठादार यादी"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT कोड"),
        "tSale": MessageLookupByLibrary.simpleMessage("एकूण विक्रय"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "एक ड्रायव्हिंग लायसन्स, राष्ट्रीय ओळखपत्र किंवा पासपोर्ट फोटो घ्या"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("वापराच्या अटी"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "The name says it all. With Pos Saas POS Unlimited, there\'s no cap on your usage. Whether you\'re processing a handful of transactions or experiencing a rush of customers, you can operate with confidence, knowing you\'re not constrained by limits"),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "या ग्राहकाची कोणतीही देय नाही"),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("या ग्राहकाचे मागील देय आहे"),
        "to": MessageLookupByLibrary.simpleMessage("पर्यंत"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("शीर्ष विक्री उत्पाद"),
        "total": MessageLookupByLibrary.simpleMessage("एकूण"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("एकूण रक्कम"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("एकूण सूट"),
        "totalDue": MessageLookupByLibrary.simpleMessage("एकूण देय"),
        "totalDues": MessageLookupByLibrary.simpleMessage("एकूण देय"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("एकूण खर्च"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("एकूण उत्पन्न"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("एकूण वस्तू : 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("एकूण तोटा"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("एकूण पेमेंट"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("एकूण देय"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("एकूण पेमेंट आउट"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("एकूण किंमत"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("एकूण उत्पादन"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("एकूण नफा"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("एकूण खरेदी"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("एकूण परताव्य रक्कम"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("एकूण परताव्य"),
        "totalSale": MessageLookupByLibrary.simpleMessage("एकूण विक्री"),
        "totalSales": MessageLookupByLibrary.simpleMessage("एकूण विक्री"),
        "totalVat": MessageLookupByLibrary.simpleMessage("एकूण व्हॅट"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("एकूण पेमेंट इन"),
        "transaction": MessageLookupByLibrary.simpleMessage("लेनदेन"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("क्रियाकलाप आयडी"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("व्यवहार अहवाल"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("पुन्हा प्रयत्न करा"),
        "type": MessageLookupByLibrary.simpleMessage("प्रकार"),
        "unPaid": MessageLookupByLibrary.simpleMessage("अदा न झालेला"),
        "unit": MessageLookupByLibrary.simpleMessage("एकक"),
        "unitName": MessageLookupByLibrary.simpleMessage("युनिटचे नाव"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("युनिट किंमत"),
        "unlimited": MessageLookupByLibrary.simpleMessage("अमर्याद"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("अनलिमिटेड इनव्हॉइस"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Unlimited Usage"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Unlock the full potential of Pos Saas POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you\'re well-versed in utilizing every facet of the system to optimize your business processes."),
        "updateNow": MessageLookupByLibrary.simpleMessage("आता अद्यतनित करा"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "आधी आपला प्लॅन अपडेट करा\\nविक्री मर्यादा संपली आहे."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("मोबाइल अॅपवर अपग्रेड करा"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("एक प्रतिमा अपलोड करा"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("इनव्हॉइस लोगो अपलोड करा"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("दस्तऐवज अपलोड करा"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("फाइल अपलोड करा"),
        "userName": MessageLookupByLibrary.simpleMessage("वापरकर्ता नाव"),
        "userRole": MessageLookupByLibrary.simpleMessage("वापरकर्ता भूमिका"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("वापरकर्ता भूमिका नाव"),
        "userTitle": MessageLookupByLibrary.simpleMessage("वापरकर्ता शीर्षक"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("व्हॅट/जीएसटी"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("फोन नंबर सत्यापित करा"),
        "view": MessageLookupByLibrary.simpleMessage("पहा"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("walk-in ग्राहक"),
        "warranty": MessageLookupByLibrary.simpleMessage("वारंटी"),
        "warrantys": MessageLookupByLibrary.simpleMessage("वारंटी"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "आम्हाला तुमचा फोन नोंदणी करण्याची आवश्यकता आहे!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            " We understand the importance of seamless operations. That\'s why our round-the-clock support is available to assist you, whether it\'s a quick query or a comprehensive concern. Connect with us anytime, anywhere via call or WhatsApp to experience unrivaled customer service."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("थोक विक्री किंमत"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("खुद्रा विक्रेता"),
        "wholesale": MessageLookupByLibrary.simpleMessage("थोक"),
        "wight": MessageLookupByLibrary.simpleMessage("वजन"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("होय परत"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "तुम्हाला तुमच्या खात्यावर पुन्हा लॉगिन करावे लागेल."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "आपण संदेश खरेदी करण्यापूर्वी ओळख सत्यापित करणे आवश्यक आहे"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("तुमची सर्व विक्री यादी"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("तुमची सर्व विक्री"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("तुमचे देय विक्री"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "आपण संदेश खरेदी करण्यापूर्वी ओळख सत्यापित करणे आवश्यक आहे"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("आपली पॅकेज"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "तुमचे पेमेंट रद्द केले गेले आहे"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("तुमचे पेमेंट यशस्वीरित्या"),
        "yourPaymentIscancelled": MessageLookupByLibrary.simpleMessage(
            "आपले पेमेंट रद्द केले गेले आहे")
      };
}
