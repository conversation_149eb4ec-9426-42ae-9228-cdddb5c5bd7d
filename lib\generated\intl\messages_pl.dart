// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pl locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pl';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("DODAJ SPRZEDAŻ"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("KATEGORIA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FAKTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Sprzedaż POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("CENA"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NAZWA PRODUKTU"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Panel logowania Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("ILOŚĆ"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Ilość*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("Całkowita wartość"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Tytuł użytkownika"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("O aplikacji"),
        "accountName": MessageLookupByLibrary.simpleMessage("Nazwa konta"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Numer konta"),
        "action": MessageLookupByLibrary.simpleMessage("Akcja"),
        "add": MessageLookupByLibrary.simpleMessage("Dodaj"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Dodaj markę"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Dodaj kategorię"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Dodaj klienta"),
        "addDescription": MessageLookupByLibrary.simpleMessage("Dodaj opis..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Dodaj dokument"),
        "addItem": MessageLookupByLibrary.simpleMessage("Dodaj pozycję"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Dodaj kategorię pozycji"),
        "addNew": MessageLookupByLibrary.simpleMessage("Dodaj nowy"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Dodaj nowego użytkownika"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Dodaj produkt"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Dodano pomyślnie"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("Dodaj dostawcę"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Dodaj jednostkę"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/zaktualizuj listę wydatków"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Dodaj/aktualizuj listę przychodów"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Dodaj rolę użytkownika"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Dodawanie numeru seryjnego?"),
        "address": MessageLookupByLibrary.simpleMessage("Adres"),
        "all": MessageLookupByLibrary.simpleMessage("Wszystko"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Wszystkie podstawowe funkcje"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Masz już konto?"),
        "amount": MessageLookupByLibrary.simpleMessage("Kwota"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Wsparcie dla aplikacji na Androida i iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Czy chcesz utworzyć tę ofertę?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Czy chcesz usunąć tego klienta?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Czy chcesz usunąć ten produkt"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Czy chcesz usunąć tę ofertę?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Czy chcesz zwrócić tę sprzedaż?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Waluta konta bankowego"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Konta bankowe"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Informacje bankowe"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nazwa banku"),
        "between": MessageLookupByLibrary.simpleMessage("Pomiędzy"),
        "billTo": MessageLookupByLibrary.simpleMessage("Do faktury:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Nazwa oddziału"),
        "brand": MessageLookupByLibrary.simpleMessage("Marka"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nazwa marki"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Kategoria biznesowa"),
        "buy": MessageLookupByLibrary.simpleMessage("Kup"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kup plan Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Kup sms"),
        "calculator": MessageLookupByLibrary.simpleMessage("Kalkulator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Anuluj"),
        "capacity": MessageLookupByLibrary.simpleMessage("Pojemność"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Gotówka i bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Gotówka w ręku"),
        "categories": MessageLookupByLibrary.simpleMessage("Kategorie"),
        "category": MessageLookupByLibrary.simpleMessage("Kategoria"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Nazwa kategorii"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Kwota reszty"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Kwota do zmiany"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Sprawdź gwarancję"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Wybierz plan"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("Należności do zapłaty >"),
        "color": MessageLookupByLibrary.simpleMessage("Kolor"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Nazwa firmy"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Adres firmy"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Opis firmy"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Adres e-mail firmy"),
        "companyName": MessageLookupByLibrary.simpleMessage("Nazwa firmy"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Numer telefonu firmy"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Adres URL witryny firmowej"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Potwierdź hasło"),
        "continu": MessageLookupByLibrary.simpleMessage("Kontynuuj"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Konwertuj na sprzedaż"),
        "create": MessageLookupByLibrary.simpleMessage("Utwórz"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Utwórz płatność"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Utworzone przez"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Kreatywny hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Waluta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Bieżący plan"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Niestandardowa identyfikacja faktury"),
        "customer": MessageLookupByLibrary.simpleMessage("Klient"),
        "customerDue":
            MessageLookupByLibrary.simpleMessage("Należności od klienta"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Faktury klienta"),
        "customerList": MessageLookupByLibrary.simpleMessage("Lista klientów"),
        "customerName": MessageLookupByLibrary.simpleMessage("Nazwa klienta"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Klient miesiąca"),
        "customerType": MessageLookupByLibrary.simpleMessage("Typ klienta"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Klient: Klient z wejścia"),
        "customers": MessageLookupByLibrary.simpleMessage("Klienci"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Dzienna ściągalność"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Dzienna sprzedaż"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcja dzienna"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Panel główny"),
        "date": MessageLookupByLibrary.simpleMessage("Data"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Data i godzina"),
        "dealer": MessageLookupByLibrary.simpleMessage("Dealer"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Cena dealera"),
        "delete": MessageLookupByLibrary.simpleMessage("Usuń"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Opłata za dostawę"),
        "description": MessageLookupByLibrary.simpleMessage("Opis"),
        "details": MessageLookupByLibrary.simpleMessage("Szczegóły >"),
        "discount": MessageLookupByLibrary.simpleMessage("Rabat"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Cena rabatowa"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Pobierz PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Należności"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Kwota do zapłaty"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Kwota należna zostanie wyświetlona tutaj, jeśli jest dostępna"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Zbiórka należności"),
        "dueList": MessageLookupByLibrary.simpleMessage("Lista należności"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcja do zapłaty"),
        "edit": MessageLookupByLibrary.simpleMessage("Edytuj"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Edytuj lub dodaj numer seryjny:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Edytuj swój profil"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Wprowadź kwotę"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Wpisz nazwę marki"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Wpisz nazwę kategorii"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("Wpisz opis firmy"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Wpisz adres e-mail firmy"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Wpisz numer telefonu firmy"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Wpisz adres URL witryny firmowej"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Wprowadź nazwę klienta"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Wpisz cenę dealera"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Wpisz cenę rabatową"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Wpisz kategorię wydatku"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Wpisz datę wydatku"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Wpisz kategorię przychodu"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Wprowadź datę przychodu"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Wpisz nazwę producenta"),
        "enterName": MessageLookupByLibrary.simpleMessage("Wpisz nazwę"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Wprowadź nazwę"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Wpisz notatkę"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Wprowadź saldo otwarcia"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Wpisz kwotę do zapłaty"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Wprowadź hasło"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Wpisz kwotę do zapłaty"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Wpisz cenę"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("Wpisz pojemność produktu"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Wpisz kod produktu"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Wpisz kolor produktu"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Wpisz nazwę produktu"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Wpisz ilość produktu"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Wpisz rozmiar produktu"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Wpisz typ produktu"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Wpisz jednostkę produktu"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Wpisz wagę produktu"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Wpisz cenę zakupu"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Wpisz numer referencyjny"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Wpisz cenę sprzedaży"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Wpisz numer seryjny"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Wpisz treść wiadomości"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Wpisz ilość zapasów"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Wprowadź identyfikator transakcji"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Wpisz nazwę jednostki"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Wprowadź nazwę roli użytkownika"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Wprowadź tytuł użytkownika"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Wpisz gwarancję"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Wpisz cenę hurtową"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Wprowadź kwotę"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Wprowadź swój adres"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Wpisz adres swojej firmy"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Wprowadź nazwę swojej firmy"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Wprowadź nazwę swojej firmy"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Wprowadź swój adres e-mail"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Wprowadź swoje hasło"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Wprowadź swoje hasło ponownie"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Wprowadź swój numer telefonu"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Wprowadź nazwę swojego sklepu"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Wpisz nazwę kategorii"),
        "expense": MessageLookupByLibrary.simpleMessage("Wydatek"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Data wydatku"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Szczegóły wydatku"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Wydatek dla"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Lista kategorii wydatków"),
        "expenses": MessageLookupByLibrary.simpleMessage("Wydatki"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top pięć produktów zakupionych w danym miesiącu"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Dla nieograniczonego wykorzystania"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Nie pamiętasz hasła?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Darmowa kopia zapasowa danych"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Bezpłatna aktualizacja na całe życie"),
        "freePackage":
            MessageLookupByLibrary.simpleMessage("Bezpłatnego pakietu"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Bezpłatny plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Rozpocznij"),
        "govermentId": MessageLookupByLibrary.simpleMessage(
            "Dokument tożsamości rządowej"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Suma"),
        "hold": MessageLookupByLibrary.simpleMessage("Zatrzymaj"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Numer rezerwacji"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Weryfikacja tożsamości"),
        "inc": MessageLookupByLibrary.simpleMessage("Dochód"),
        "income": MessageLookupByLibrary.simpleMessage("Przychód"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Kategoria przychodów"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Lista kategorii przychodów"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Data przychodu"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Szczegóły przychodów"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Przychód dla"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Lista przychodów"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Zwiększ stan"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Natychmiastowa prywatność"),
        "invoice": MessageLookupByLibrary.simpleMessage("Faktura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Faktura:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Nr faktury..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Numer faktury"),
        "item": MessageLookupByLibrary.simpleMessage("Przedmiot"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nazwa pozycji"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Weryfikacja KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Szczegóły księgi"),
        "ledger": MessageLookupByLibrary.simpleMessage("Księga"),
        "left": MessageLookupByLibrary.simpleMessage("Lewo"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Konta kredytowe"),
        "logOut": MessageLookupByLibrary.simpleMessage("Wyloguj się"),
        "login": MessageLookupByLibrary.simpleMessage("Zaloguj się"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Pozycja logo na fakturze?"),
        "loss": MessageLookupByLibrary.simpleMessage("Strata"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Strata/zysk"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Strata (-)"),
        "lowStock":
            MessageLookupByLibrary.simpleMessage("Niski Stan Magazynowy"),
        "lowStocks":
            MessageLookupByLibrary.simpleMessage("Niskie stany magazynowe"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Zostaw trwałe wrażenie na swoich klientach dzięki spersonalizowanym fakturom. Nasza nieograniczona aktualizacja oferuje unikalną możliwość dostosowania faktur, dodając profesjonalny wygląd, który wzmacnia tożsamość twojej marki i buduje lojalność klientów."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Producent"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Panel logowania Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Panel rejestracji Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Aplikacja mobilna\n+\nKomputerowa"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Paragon fiskalny"),
        "nam": MessageLookupByLibrary.simpleMessage("Nazwa*"),
        "name": MessageLookupByLibrary.simpleMessage("Nazwa"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nazwa, kod lub kategoria"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nowi klienci"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nowi klienci"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nowy przychód"),
        "no": MessageLookupByLibrary.simpleMessage("Nie"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Brak połączenia"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono klienta"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono transakcji do zapłaty"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono kategorii wydatków"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono kategorii przychodów"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono przychodów"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono faktury"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono produktu"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono transakcji zakupu"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono oferty"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono raportu"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono transakcji sprzedaży"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono numeru seryjnego"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono dostawcy"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Nie znaleziono transakcji"),
        "noUserFound": MessageLookupByLibrary.simpleMessage("Brak użytkownika"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Brak roli użytkownika"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Nie znaleziono numeru seryjnego"),
        "note": MessageLookupByLibrary.simpleMessage("Notatka"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Otwarte czeki"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo otwarcia"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "lub przeciągnij i upuść PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Zamówienia"),
        "other": MessageLookupByLibrary.simpleMessage("Inne"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Inne przychody"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Funkcja pakietu"),
        "paid": MessageLookupByLibrary.simpleMessage("Zapłacone"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Kwota zapłacona"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nazwa imprezy"),
        "partyType": MessageLookupByLibrary.simpleMessage("Typ imprezy"),
        "password": MessageLookupByLibrary.simpleMessage("Hasło"),
        "payCash": MessageLookupByLibrary.simpleMessage("Zapłać gotówką"),
        "payable": MessageLookupByLibrary.simpleMessage("Do zapłaty"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Kwota do zapłaty"),
        "payment": MessageLookupByLibrary.simpleMessage("Płatność"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Płatność"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Płatność"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Typ płatności"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Typy płatności"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Numer telefonu"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Weryfikacja telefonu"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Dodaj sprzedaż"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Dodaj klienta"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Sprawdź swoje połączenie internetowe"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Pobierz naszą aplikację mobilną i zasubskrybuj pakiet, aby korzystać z wersji na pulpit"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Proszę wprowadzić stan produktu"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Wpisz prawidłowe dane"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Wybierz klienta"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Wpisz prawidłowe dane"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Panel rejestracji Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Praktyka"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Obsługa Klienta"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Podgląd"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Poprzednie zaległe:"),
        "price": MessageLookupByLibrary.simpleMessage("Cena"),
        "print": MessageLookupByLibrary.simpleMessage("Drukuj"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Wydrukuj fakturę"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Drukuj PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Polityka prywatności"),
        "product": MessageLookupByLibrary.simpleMessage("Produkt"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Kategoria produktu"),
        "productCod": MessageLookupByLibrary.simpleMessage("Kod produktu*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Kolor produktu"),
        "productList": MessageLookupByLibrary.simpleMessage("Lista produktów"),
        "productNam": MessageLookupByLibrary.simpleMessage("Nazwa produktu*"),
        "productName": MessageLookupByLibrary.simpleMessage("Nazwa produktu"),
        "productSize": MessageLookupByLibrary.simpleMessage("Rozmiar produktu"),
        "productStock": MessageLookupByLibrary.simpleMessage("Stan produktu"),
        "productType": MessageLookupByLibrary.simpleMessage("Typ produktu"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Jednostka produktu"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Gwarancja produktu"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Waga produktu"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Pojemność produktu"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Edycja profilu"),
        "profit": MessageLookupByLibrary.simpleMessage("Zysk"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Zysk(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Zysk(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Zakup"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Lista zakupów"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Kup plan Premium"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Cena zakupu"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcja zakupu"),
        "quantity": MessageLookupByLibrary.simpleMessage("Ilość"),
        "quotation": MessageLookupByLibrary.simpleMessage("Oferta"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Lista ofert"),
        "recentSale":
            MessageLookupByLibrary.simpleMessage("Ostatnie Sprzedaże"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Kwota otrzymana"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Nr referencyjny"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Numer referencyjny"),
        "registration": MessageLookupByLibrary.simpleMessage("Rejestracja"),
        "remaining": MessageLookupByLibrary.simpleMessage("Pozostało: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Nierozliczony rachunek"),
        "remainingDue":
            MessageLookupByLibrary.simpleMessage("Pozostałe do zapłaty"),
        "reports": MessageLookupByLibrary.simpleMessage("Raporty"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Resetuj hasło"),
        "retailer": MessageLookupByLibrary.simpleMessage("Detalista"),
        "revenue": MessageLookupByLibrary.simpleMessage("Dochód"),
        "right": MessageLookupByLibrary.simpleMessage("Prawo"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Kwota Sprzedaży"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Zabezpiecz dane swojej firmy w łatwy sposób. Nasza nieograniczona aktualizacja Pos Saas POS obejmuje darmową kopię zapasową danych, co zapewnia ochronę twoich cennych informacji przed nieprzewidzianymi zdarzeniami. Skoncentruj się na tym, co naprawdę się liczy - na wzroście swojego biznesu."),
        "sale": MessageLookupByLibrary.simpleMessage("Sprzedaż"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Kwota sprzedaży"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Szczegóły sprzedaży"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista sprzedaży"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Cena sprzedaży"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Cena sprzedaży*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Zwrot sprzedaży"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transakcja sprzedaży"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transakcje sprzedaży (historia sprzedaży ofert)"),
        "sales": MessageLookupByLibrary.simpleMessage("Sprzedaży"),
        "salesList": MessageLookupByLibrary.simpleMessage("Lista sprzedaży"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Zapisz i opublikuj"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Zapisz i opublikuj"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Zapisz zmiany"),
        "search": MessageLookupByLibrary.simpleMessage("Szukaj......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Szukaj czegokolwiek..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Szukaj po fakturze..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Szukaj według faktury lub nazwy"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Szukaj po nazwie"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Szukaj po imieniu lub telefonie..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Szukaj numeru seryjnego"),
        "selectParties": MessageLookupByLibrary.simpleMessage("Wybierz strony"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Wybierz markę produktu"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Wybierz numer seryjny"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Wybierz warianty:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Wybierz czas gwarancji"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Wybierz swój język"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Wyślij wiadomość"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Numer seryjny"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Numery seryjne"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Opłata za usługę"),
        "setting": MessageLookupByLibrary.simpleMessage("Ustawienia"),
        "share": MessageLookupByLibrary.simpleMessage("Udostępnij"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Wysyłka/Inne"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nazwa sklepu"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Saldo otwarcia sklepu"),
        "show": MessageLookupByLibrary.simpleMessage("Pokaż >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Wyświetl logo na fakturze?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Wysyłka/Usługi"),
        "size": MessageLookupByLibrary.simpleMessage("Rozmiar"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statystyka"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Pozostań na czele postępu technologicznego bez dodatkowych kosztów. Nasza nieograniczona aktualizacja Pos Saas POS zapewnia, że zawsze masz dostęp do najnowszych narzędzi i funkcji, gwarantując, że twoja firma pozostaje nowoczesna."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Pozostań na czele postępu technologicznego bez dodatkowych kosztów. Nasza nieograniczona aktualizacja Pos Sass POS zapewnia, że zawsze masz dostęp do najnowszych narzędzi i funkcji, gwarantując, że twoja firma pozostaje nowoczesna."),
        "stock": MessageLookupByLibrary.simpleMessage("Zapas"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("Stan magazynu"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Raport o stanie magazynowym"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Wartość zapasów"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Wartość Zapasów"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Suma podrzędna"),
        "subciption": MessageLookupByLibrary.simpleMessage("Subskrypcja"),
        "submit": MessageLookupByLibrary.simpleMessage("Potwierdź"),
        "supplier": MessageLookupByLibrary.simpleMessage("Dostawcy"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Należności od dostawcy"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Faktura od dostawcy"),
        "supplierList": MessageLookupByLibrary.simpleMessage("Lista dostawców"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Kod SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Całkowita Sprzedaż"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Zrób zdjęcie prawa jazdy, dowodu osobistego lub paszportu"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Warunki korzystania"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Nazwa mówi sama za siebie. Dzięki Pos Saas POS Unlimited nie ma ograniczeń w korzystaniu z systemu. Bez względu na to, czy przetwarzasz kilka transakcji czy masz duże natężenie klientów, możesz działać z pewnością, wiedząc, że nie masz ograniczeń."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Ten klient nie ma żadnych należności"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Ten klient ma poprzednie zadłużenie"),
        "to": MessageLookupByLibrary.simpleMessage("Do"),
        "topSellingProduct": MessageLookupByLibrary.simpleMessage(
            "Najczęściej sprzedawany produkt"),
        "total": MessageLookupByLibrary.simpleMessage("razem"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Całkowita kwota"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Całkowity rabat"),
        "totalDue":
            MessageLookupByLibrary.simpleMessage("Należności do zapłaty"),
        "totalDues":
            MessageLookupByLibrary.simpleMessage("Pozostałe należności"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Całkowity koszt"),
        "totalIncome":
            MessageLookupByLibrary.simpleMessage("Całkowity przychód"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Razem przedmiotów: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Całkowita strata"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Całkowita zapłata"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Całkowita do zapłaty"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Całkowita płatność"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Całkowita cena"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Całkowita liczba produktów"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Całkowity zysk"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Całkowite zakupy"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Całkowita kwota zwrotu"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Całkowita ilość zwrotów"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Całkowita sprzedaż"),
        "totalSales":
            MessageLookupByLibrary.simpleMessage("Całkowita sprzedaż"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Całkowity VAT"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Całkowita płatność"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transakcja"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Identyfikator transakcji"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Raport transakcji"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Spróbuj ponownie"),
        "type": MessageLookupByLibrary.simpleMessage("Typ"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Niezapłacony"),
        "unit": MessageLookupByLibrary.simpleMessage("Jednostka"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nazwa jednostki"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Cena jednostkowa"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Nieograniczony"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Nieograniczone faktury"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Nieograniczone korzystanie"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Odblokuj pełny potencjał systemu Pos Saas POS dzięki spersonalizowanym sesjom szkoleniowym prowadzonym przez nasz zespół ekspertów. Od podstaw po zaawansowane techniki zapewniamy, że jesteś biegły w wykorzystywaniu każdego aspektu systemu w celu zoptymalizowania procesów biznesowych."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Aktualizuj teraz"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Najpierw zaktualizuj swój plan\\nLimit sprzedaży jest przekroczony."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Uaktualnij w aplikacji mobilnej"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Prześlij obraz"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Prześlij logo faktury"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Prześlij dokument"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Prześlij plik"),
        "userName": MessageLookupByLibrary.simpleMessage("Nazwa użytkownika"),
        "userRole": MessageLookupByLibrary.simpleMessage("Rola użytkownika"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nazwa roli użytkownika"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Tytuł użytkownika"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("Vat/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Zweryfikuj numer telefonu"),
        "view": MessageLookupByLibrary.simpleMessage("Zobacz"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Klient z wejścia"),
        "warranty": MessageLookupByLibrary.simpleMessage("Gwarancja"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Gwarancja"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Musimy zarejestrować Twój telefon, zanim zaczniemy!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Rozumiemy znaczenie bezproblemowej pracy. Dlatego nasze wsparcie jest dostępne przez całą dobę, aby pomóc ci w szybkiej odpowiedzi na pytanie lub rozwiązaniu poważnych problemów. Skontaktuj się z nami w dowolnym miejscu i czasie, dzwoniąc lub korzystając z WhatsApp, aby doświadczyć niezrównanej obsługi klienta."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Cena hurtowa"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Hurtownik"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Hurt"),
        "wight": MessageLookupByLibrary.simpleMessage("Waga"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Tak, zwrot"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Musisz ZALOGOWAĆ SIĘ PONOWNIE na swoje konto."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Musisz zweryfikować swoją tożsamość przed zakupem wiadomości"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Twoja lista wszystkich sprzedaży"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Wszystkie Twoje sprzedaże"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Korzystasz z"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Twoje należne należności"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Musisz zweryfikować swoją tożsamość przed zakupem wiadomości"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Twój pakiet"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "Twoje płatności zostało anulowane"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Twoje płatności zostało pomyślnie"),
        "yourPaymentIscancelled": MessageLookupByLibrary.simpleMessage(
            "Twoja płatność została anulowana")
      };
}
