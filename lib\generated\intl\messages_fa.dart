// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fa locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fa';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("اضافه کردن فروش"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("دسته"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("فاکتور"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("موبی پاس"),
        "POSSale": MessageLookupByLibrary.simpleMessage("فروش نقطه فروش (POS)"),
        "PRICE": MessageLookupByLibrary.simpleMessage("قیمت"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("نام محصول"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("پنل ورود به Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("تعداد"),
        "STATUS": MessageLookupByLibrary.simpleMessage("وضعیت"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("مقدار کل"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("عنوان کاربری"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("درباره اپلیکیشن"),
        "accountName": MessageLookupByLibrary.simpleMessage("نام حساب"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("شماره حساب"),
        "action": MessageLookupByLibrary.simpleMessage("عملیات"),
        "add": MessageLookupByLibrary.simpleMessage("افزودن"),
        "addBrand": MessageLookupByLibrary.simpleMessage("افزودن برند"),
        "addCategory": MessageLookupByLibrary.simpleMessage("اضافه کردن دسته"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("افزودن مشتری"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("توضیحات را اضافه کنید..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("افزودن اسناد"),
        "addItem": MessageLookupByLibrary.simpleMessage("افزودن مورد"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("افزودن دسته بندی محصول"),
        "addNew": MessageLookupByLibrary.simpleMessage("افزودن جدید"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("افزودن کاربر جدید"),
        "addProduct": MessageLookupByLibrary.simpleMessage("افزودن محصول"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("افزوده شد با موفقیت"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("افزودن تامین‌کننده"),
        "addUnit": MessageLookupByLibrary.simpleMessage("افزودن واحد"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "اضافه کردن / به‌روزرسانی لیست هزینه"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "افزودن/به‌روزرسانی لیست درآمد"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("افزودن نقش کاربری"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("افزودن شماره سریال؟"),
        "address": MessageLookupByLibrary.simpleMessage("آدرس"),
        "all": MessageLookupByLibrary.simpleMessage("همه"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("تمامی ویژگی‌های اساسی"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("قبلاً حساب کاربری دارید؟"),
        "amount": MessageLookupByLibrary.simpleMessage("مقدار"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "پشتیبانی از اپلیکیشن‌های Android و iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "آیا میخواهید این پیش فاکتور را ایجاد کنید؟"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "آیا می‌خواهید این مشتری را حذف کنید؟"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "آیا می‌خواهید این محصول را حذف کنید؟"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "آیا می‌خواهید این پیش فاکتور را حذف کنید؟"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "آیا می‌خواهید این فروش را بازگردانید؟"),
        "balance": MessageLookupByLibrary.simpleMessage("تراز"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("واحد پولی حساب بانکی"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("حساب‌های بانکی"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("اطلاعات بانکی"),
        "bankName": MessageLookupByLibrary.simpleMessage("نام بانک"),
        "between": MessageLookupByLibrary.simpleMessage("بین"),
        "billTo": MessageLookupByLibrary.simpleMessage("به:"),
        "branchName": MessageLookupByLibrary.simpleMessage("نام شعبه"),
        "brand": MessageLookupByLibrary.simpleMessage("برند"),
        "brandName": MessageLookupByLibrary.simpleMessage("نام برند"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("دسته‌بندی تجاری"),
        "buy": MessageLookupByLibrary.simpleMessage("خرید"),
        "buyPremiumPlan": MessageLookupByLibrary.simpleMessage("خرید پلن ویژه"),
        "buySms": MessageLookupByLibrary.simpleMessage("خرید پیامک"),
        "calculator": MessageLookupByLibrary.simpleMessage("ماشین حساب:"),
        "camera": MessageLookupByLibrary.simpleMessage("دوربین"),
        "cancel": MessageLookupByLibrary.simpleMessage("لغو"),
        "capacity": MessageLookupByLibrary.simpleMessage("ظرفیت"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("نقد و بانک"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("نقد در دست"),
        "categories": MessageLookupByLibrary.simpleMessage("دسته‌بندی‌ها"),
        "category": MessageLookupByLibrary.simpleMessage("دسته"),
        "categoryName": MessageLookupByLibrary.simpleMessage("نام دسته"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("مبلغ تغییر"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("مبلغ قابل تغییر"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("بررسی گارانتی"),
        "choseAplan":
            MessageLookupByLibrary.simpleMessage("یک طرح را انتخاب کنید"),
        "collectDue": MessageLookupByLibrary.simpleMessage("جمع‌آوری مستحق >"),
        "color": MessageLookupByLibrary.simpleMessage("رنگ"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("نام شرکت"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("آدرس شرکت"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("توضیحات شرکت"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("آدرس ایمیل شرکت"),
        "companyName": MessageLookupByLibrary.simpleMessage("نام شرکت"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("شماره تلفن شرکت"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("آدرس وبسایت شرکت"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("تایید رمز عبور"),
        "continu": MessageLookupByLibrary.simpleMessage("ادامه دهید"),
        "convertToSale": MessageLookupByLibrary.simpleMessage("تبدیل به فروش"),
        "create": MessageLookupByLibrary.simpleMessage("ایجاد"),
        "createPayment": MessageLookupByLibrary.simpleMessage("ایجاد پرداخت"),
        "createdBy": MessageLookupByLibrary.simpleMessage("ساخته شده توسط"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("مرکز خلاقیت"),
        "currency": MessageLookupByLibrary.simpleMessage("واحد پول"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("طرح فعلی"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("برندسازی فاکتور سفارشی"),
        "customer": MessageLookupByLibrary.simpleMessage("مشتری"),
        "customerDue": MessageLookupByLibrary.simpleMessage("بدهی مشتری"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("فاکتورهای مشتری"),
        "customerList": MessageLookupByLibrary.simpleMessage("لیست مشتری‌ها"),
        "customerName": MessageLookupByLibrary.simpleMessage("نام مشتری"),
        "customerOfTheMonth": MessageLookupByLibrary.simpleMessage("مشتری ماه"),
        "customerType": MessageLookupByLibrary.simpleMessage("نوع مشتری"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("مشتری: مشتری حضوری"),
        "customers": MessageLookupByLibrary.simpleMessage("مشتری‌ها"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("جمع‌آوری روزانه"),
        "dailySales": MessageLookupByLibrary.simpleMessage("فروش روزانه"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("تراکنش‌های روزانه"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("داشبورد"),
        "date": MessageLookupByLibrary.simpleMessage("تاریخ"),
        "dateTime": MessageLookupByLibrary.simpleMessage("تاریخ و زمان"),
        "dealer": MessageLookupByLibrary.simpleMessage("نماینده"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("قیمت نماینده"),
        "delete": MessageLookupByLibrary.simpleMessage("حذف"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("هزینه تحویل"),
        "description": MessageLookupByLibrary.simpleMessage("توضیحات"),
        "details": MessageLookupByLibrary.simpleMessage("جزئیات >"),
        "discount": MessageLookupByLibrary.simpleMessage("تخفیف"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("قیمت تخفیف"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("دانلود PDF"),
        "due": MessageLookupByLibrary.simpleMessage("بدهی"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("مبلغ معوق"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "مبلغ بدهی در صورت وجود نمایش داده خواهد شد"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("جمع‌آوری بدهی"),
        "dueList": MessageLookupByLibrary.simpleMessage("لیست بدهی‌ها"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("تراکنش‌های بدهی"),
        "edit": MessageLookupByLibrary.simpleMessage("ویرایش"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("ویرایش/افزودن سریال:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("پروفایل خود را ویرایش کنید"),
        "email": MessageLookupByLibrary.simpleMessage("ایمیل"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("مقدار را وارد کنید"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("نام برند را وارد کنید"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("نام دسته را وارد کنید"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("توضیحات شرکت را وارد کنید"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "آدرس ایمیل شرکت را وارد کنید"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "شماره تلفن شرکت را وارد کنید"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "آدرس وبسایت شرکت را وارد کنید"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("نام مشتری را وارد کنید"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("قیمت نماینده را وارد کنید"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("قیمت تخفیف را وارد کنید"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("دسته هزینه را وارد کنید"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("تاریخ هزینه را وارد کنید"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("دسته درآمد را وارد کنید"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("تاریخ درآمد را وارد کنید"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("نام تولیدکننده را وارد کنید"),
        "enterName": MessageLookupByLibrary.simpleMessage("نام را وارد کنید"),
        "enterNames": MessageLookupByLibrary.simpleMessage("نام را وارد کنید"),
        "enterNote":
            MessageLookupByLibrary.simpleMessage("یادداشت را وارد کنید"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("موجودی ابتدایی را وارد کنید"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("مبلغ پرداختی را وارد کنید"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("وارد کردن رمز عبور"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("مبلغ پرداختی را وارد کنید"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("قیمت را وارد کنید"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("ظرفیت محصول را وارد کنید"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("کد محصول را وارد کنید"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("رنگ محصول را وارد کنید"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("نام محصول را وارد کنید"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("تعداد محصول را وارد کنید"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("اندازه محصول را وارد کنید"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("نوع محصول را وارد کنید"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("واحد محصول را وارد کنید"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("وزن محصول را وارد کنید"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("قیمت خرید را وارد کنید"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("شماره مرجع را وارد کنید"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("قیمت فروش را وارد کنید"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("شماره سریال را وارد کنید"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("محتوای پیام را وارد کنید"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("مقدار موجودی را وارد کنید"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("وارد کردن شناسه تراکنش"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("نام واحد را وارد کنید"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("وارد کردن نام نقش کاربری"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("وارد کردن عنوان کاربری"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("گارانتی محصول را وارد کنید"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "قیمت عمده فروشی را وارد کنید"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("مبلغ خود را وارد کنید"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("آدرس خود را وارد کنید"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("آدرس شرکت خود را وارد کنید"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("نام شرکت خود را وارد کنید"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("نام شرکت خود را وارد کنید"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("آدرس ایمیل خود را وارد کنید"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("رمز عبور خود را وارد کنید"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "رمز عبور خود را دوباره وارد کنید"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("شماره تلفن خود را وارد کنید"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "نام فروشگاه خود را وارد کنید"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("نام دسته را وارد کنید"),
        "expense": MessageLookupByLibrary.simpleMessage("هزینه"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("تاریخ هزینه"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("جزئیات هزینه"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("هزینه برای"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("لیست دسته هزینه"),
        "expenses": MessageLookupByLibrary.simpleMessage("هزینه‌ها"),
        "fivePurchase":
            MessageLookupByLibrary.simpleMessage("پنج محصول پرفروش ماه"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("برای استفاده‌های نامحدود"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage(
            "رمز عبور را فراموش کرده‌اید؟"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("پشتیبان‌گیری داده رایگان"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "به‌روزرسانی مادام‌العمر رایگان"),
        "freePackage": MessageLookupByLibrary.simpleMessage("پکیج رایگان"),
        "freePlan": MessageLookupByLibrary.simpleMessage("پلن رایگان"),
        "getStarted": MessageLookupByLibrary.simpleMessage("شروع کنید"),
        "govermentId": MessageLookupByLibrary.simpleMessage("شناسه دولتی"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("مجموع کل"),
        "hold": MessageLookupByLibrary.simpleMessage("نگه داشتن"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("شماره نگه داشتن"),
        "identityVerify": MessageLookupByLibrary.simpleMessage("تأیید هویت"),
        "inc": MessageLookupByLibrary.simpleMessage("درآمد"),
        "income": MessageLookupByLibrary.simpleMessage("درآمد"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("دسته درآمد"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("فهرست دسته‌های درآمد"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("تاریخ درآمد"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("جزئیات درآمد"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("درآمد برای"),
        "incomeList": MessageLookupByLibrary.simpleMessage("لیست درآمد"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("افزایش موجودی"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("حریم خصوصی فوری"),
        "invoice": MessageLookupByLibrary.simpleMessage("فاکتور"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("فاکتور:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("شماره فاکتور..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("شماره فاکتور"),
        "item": MessageLookupByLibrary.simpleMessage("آیتم"),
        "itemName": MessageLookupByLibrary.simpleMessage("نام مورد"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("تأیید هویت KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("جزئیات دفتر حساب"),
        "ledger": MessageLookupByLibrary.simpleMessage("دفتر حساب"),
        "left": MessageLookupByLibrary.simpleMessage("چپ"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("حساب‌های وام"),
        "logOut": MessageLookupByLibrary.simpleMessage("خروج"),
        "login": MessageLookupByLibrary.simpleMessage("ورود"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("موقعیت لوگو در فاکتور؟"),
        "loss": MessageLookupByLibrary.simpleMessage("ضرر"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("ضرر/سود"),
        "lossminus": MessageLookupByLibrary.simpleMessage("ضرر (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("موجودی کم"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("موجودی کم"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "تاثیرگذاری دائمی را بر مشتریان خود با فاکتورهای با برند شخصی بگذارید. بروزرسانی نامحدود ما امکان شخصی‌سازی فاکتورهای شما را با اضافه کردن لمسه حرفه‌ای که هویت برند شما را تقویت می‌کند و وفاداری مشتری را ترویج می‌دهد، ارائه می‌دهد."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("تولیدکننده"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("پنل ورود به Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("پنل عضویت در Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "اپلیکیشن تلفن همراه + رایانه شخصی"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("رسید پولی"),
        "nam": MessageLookupByLibrary.simpleMessage("نام*"),
        "name": MessageLookupByLibrary.simpleMessage("نام"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("نام یا کد یا دسته بندی"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("مشتریان جدید"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("مشتریان جدید"),
        "newIncome": MessageLookupByLibrary.simpleMessage("درآمد جدید"),
        "no": MessageLookupByLibrary.simpleMessage("خیر"),
        "noConnection": MessageLookupByLibrary.simpleMessage("بدون اتصال"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("مشتری یافت نشد"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("هیچ تراکنش مستحقی یافت نشد"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("هیچ دسته هزینه‌ای یافت نشد"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("هیچ دسته درآمدی یافت نشد"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("هیچ درآمدی یافت نشد"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("هیچ فاکتوری یافت نشد"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("محصولی یافت نشد"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("هیچ تراکنش خریدی یافت نشد"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("هیچ پیش فاکتوری یافت نشد"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("گزارشی یافت نشد"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("هیچ تراکنش فروشی یافت نشد"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("شماره سریالی یافت نشد"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("هیچ تامین‌کننده‌ای یافت نشد"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("هیچ تراکنشی یافت نشد"),
        "noUserFound": MessageLookupByLibrary.simpleMessage("کاربری یافت نشد"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("هیچ نقش کاربری یافت نشد"),
        "note": MessageLookupByLibrary.simpleMessage("یادداشت"),
        "ok": MessageLookupByLibrary.simpleMessage("تایید"),
        "openCheques": MessageLookupByLibrary.simpleMessage("چک‌های باز"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("موجودی ابتدایی"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " یا PNG، JPG را بکشید و رها کنید"),
        "orders": MessageLookupByLibrary.simpleMessage("سفارش‌ها"),
        "other": MessageLookupByLibrary.simpleMessage("سایر"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("درآمد دیگر"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("ویژگی‌های پکیج"),
        "paid": MessageLookupByLibrary.simpleMessage("پرداخت شده"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("مقدار پرداخت شده"),
        "partyName": MessageLookupByLibrary.simpleMessage("نام شخص یا شرکت"),
        "partyType": MessageLookupByLibrary.simpleMessage("نوع شخص یا شرکت"),
        "password": MessageLookupByLibrary.simpleMessage("رمز عبور"),
        "payCash": MessageLookupByLibrary.simpleMessage("پرداخت نقدی"),
        "payable": MessageLookupByLibrary.simpleMessage("قابل پرداخت"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("مبلغ پرداختی"),
        "payment": MessageLookupByLibrary.simpleMessage("پرداخت"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("ورودی پرداخت"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("خروجی پرداخت"),
        "paymentType": MessageLookupByLibrary.simpleMessage("نوع پرداخت"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("نوع پرداخت"),
        "phone": MessageLookupByLibrary.simpleMessage("تلفن"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("شماره تلفن"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("تأیید شماره تلفن"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("لطفاً یک فروش اضافه کنید"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("لطفاً مشتری را اضافه کنید"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "لطفاً اتصال اینترنت خود را بررسی کنید"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "لطفاً اپلیکیشن موبایل ما را دانلود کنید و یک بسته راه‌اندازی را برای استفاده از نسخه دسکتاپ خریداری کنید"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "لطفاً موجودی محصول را وارد کنید"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "لطفاً داده‌های معتبر وارد کنید"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "لطفاً یک مشتری را انتخاب کنید"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "لطفاً داده‌های معتبر وارد کنید"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("پنل ثبت نام در Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("تمرین"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("پشتیبانی مشتریان ویژه"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("پلن ویژه"),
        "preview": MessageLookupByLibrary.simpleMessage("پیش‌نمایش"),
        "previousDue": MessageLookupByLibrary.simpleMessage("مانده قبلی:"),
        "price": MessageLookupByLibrary.simpleMessage("قیمت"),
        "print": MessageLookupByLibrary.simpleMessage("چاپ"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("چاپ فاکتور"),
        "printPdf": MessageLookupByLibrary.simpleMessage("چاپ PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("سیاست حریم خصوصی"),
        "product": MessageLookupByLibrary.simpleMessage("محصول"),
        "productCategory": MessageLookupByLibrary.simpleMessage("دسته محصول"),
        "productColor": MessageLookupByLibrary.simpleMessage("رنگ محصول"),
        "productList": MessageLookupByLibrary.simpleMessage("لیست محصولات"),
        "productName": MessageLookupByLibrary.simpleMessage("نام محصول"),
        "productSize": MessageLookupByLibrary.simpleMessage("اندازه محصول"),
        "productStock": MessageLookupByLibrary.simpleMessage("موجودی محصول"),
        "productType": MessageLookupByLibrary.simpleMessage("نوع محصول"),
        "productUnit": MessageLookupByLibrary.simpleMessage("واحد محصول"),
        "productWeight": MessageLookupByLibrary.simpleMessage("وزن محصول"),
        "productcapacity": MessageLookupByLibrary.simpleMessage("ظرفیت محصول"),
        "prof": MessageLookupByLibrary.simpleMessage("پروفایل"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("ویرایش پروفایل"),
        "profit": MessageLookupByLibrary.simpleMessage("سود"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("سود(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("سود(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("خرید"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("لیست خرید"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("خرید پلن ویژه"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("قیمت خرید"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("تراکنش خرید"),
        "quantity": MessageLookupByLibrary.simpleMessage("تعداد*"),
        "quotation": MessageLookupByLibrary.simpleMessage("پیش فاکتور"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("لیست پیش فاکتورها"),
        "recentSale": MessageLookupByLibrary.simpleMessage("فروش‌های اخیر"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("مقدار دریافتی"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("شماره مرجع"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("شماره مرجع"),
        "registration": MessageLookupByLibrary.simpleMessage("ثبت نام"),
        "remaining": MessageLookupByLibrary.simpleMessage("مانده:"),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("مانده باقیمانده"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("باقیمانده بدهی"),
        "reports": MessageLookupByLibrary.simpleMessage("گزارش‌ها"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("بازیابی رمز عبور"),
        "retailer": MessageLookupByLibrary.simpleMessage("خرده فروش"),
        "revenue": MessageLookupByLibrary.simpleMessage("درآمد"),
        "right": MessageLookupByLibrary.simpleMessage("راست"),
        "sAmount": MessageLookupByLibrary.simpleMessage("مقدار فروش"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "حفاظت از داده‌های کسب‌وکار شما بدون دغدغه. بروزرسانی نامحدود Pos Saas ما شامل پشتیبان‌گیری رایگان از داده است، تضمین می‌کند که اطلاعات ارزشمند شما در مقابل هر نوع حادثه غیرمنتظره محافظت می‌شود. روی رشد واقعی کسب‌وکار خود تمرکز کنید."),
        "sale": MessageLookupByLibrary.simpleMessage("فروش"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("مقدار فروش"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("جزئیات فروش"),
        "saleList": MessageLookupByLibrary.simpleMessage("لیست فروش"),
        "salePrice": MessageLookupByLibrary.simpleMessage("قیمت فروش"),
        "salePrices": MessageLookupByLibrary.simpleMessage("قیمت فروش*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("بازگشت فروش"),
        "saleTransaction": MessageLookupByLibrary.simpleMessage("تراکنش فروش"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "تراکنش‌های فروش (تاریخچه پیشنهادات فروش)"),
        "sales": MessageLookupByLibrary.simpleMessage("فروش‌ها"),
        "salesList": MessageLookupByLibrary.simpleMessage("لیست فروش"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("ذخیره و انتشار"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("ذخیره و انتشار"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("ذخیره تغییرات"),
        "search": MessageLookupByLibrary.simpleMessage("جستجو..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("جستجو در هر چیزی..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("جستجو بر اساس فاکتور..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("جستجو بر اساس فاکتور یا نام"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("جستجو بر اساس نام"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "جستجو بر اساس نام یا تلفن..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("جستجوی شماره سریال"),
        "selectParties": MessageLookupByLibrary.simpleMessage("انتخاب طرف‌ها"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("برند محصول را انتخاب کنید"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("انتخاب شماره سریال"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("انتخاب تغییرات:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("زمان گارانتی را انتخاب کنید"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("زبان خود را انتخاب کنید"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("ارسال پیام"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("شماره سریال"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("شماره سریال"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("هزینه خدمات"),
        "setting": MessageLookupByLibrary.simpleMessage("تنظیمات"),
        "share": MessageLookupByLibrary.simpleMessage("اشتراک گذاری"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("حمل و نقل/سایر"),
        "shopName": MessageLookupByLibrary.simpleMessage("نام فروشگاه"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("موجودی ابتدایی فروشگاه"),
        "show": MessageLookupByLibrary.simpleMessage("نمایش >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("نمایش لوگو در فاکتور؟"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("حمل و نقل/سرویس"),
        "size": MessageLookupByLibrary.simpleMessage("اندازه"),
        "statistic": MessageLookupByLibrary.simpleMessage("آمار"),
        "status": MessageLookupByLibrary.simpleMessage("وضعیت"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "با بروزرسانی نامحدود Pos Saas ما، همیشه آخرین ابزار و ویژگی‌ها در دسترس شما قرار دارد و تضمین می‌شود که کسب و کار شما به‌روز باقی بماند."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "با بروزرسانی نامحدود Pos Saas ما، همیشه آخرین ابزار و ویژگی‌ها در دسترس شما قرار دارد و تضمین می‌شود که کسب و کار شما به‌روز باقی بماند."),
        "stock": MessageLookupByLibrary.simpleMessage("موجودی"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("موجودی انبار"),
        "stockReport": MessageLookupByLibrary.simpleMessage("گزارش موجودی"),
        "stockValue": MessageLookupByLibrary.simpleMessage("ارزش موجودی"),
        "stockValues": MessageLookupByLibrary.simpleMessage("ارزش موجودی"),
        "subTotal": MessageLookupByLibrary.simpleMessage("زیرمجموعه"),
        "subciption": MessageLookupByLibrary.simpleMessage("اشتراک"),
        "submit": MessageLookupByLibrary.simpleMessage("ثبت"),
        "supplier": MessageLookupByLibrary.simpleMessage("تأمین‌کنندگان"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("بدهی تامین‌کننده"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("فاکتور تامین‌کننده"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("لیست تامین‌کنندگان"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("کد SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("کل فروش"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "عکس گواهینامه رانندگی، کارت ملی یا پاسپورت را بگیرید"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("شرایط استفاده"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "نام همه چیز را می‌گوید. با Pos Saas POS Unlimited، محدودیتی بر روی استفاده شما وجود ندارد. برایشمار کردن دست کم تراکنش‌ها یا تجربه شلوغی از مشتریان، شما می‌توانید با اطمینان عمل کنید و بدانید که تحت محدودیت‌ها نیستید."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("این مشتری هیچ بدهی ندارد"),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("این مشتری بدهی قبلی دارد"),
        "to": MessageLookupByLibrary.simpleMessage("تا"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("محصول برتر فروش"),
        "total": MessageLookupByLibrary.simpleMessage("مجموع"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("مجموع مبلغ"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("تخفیف کل"),
        "totalDue": MessageLookupByLibrary.simpleMessage("مجموع مستحق"),
        "totalDues": MessageLookupByLibrary.simpleMessage("کل بدهی"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("مجموع هزینه"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("مجموع درآمد"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("مجموع آیتم: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("کل ضرر"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("کل پرداخت شده"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("مجموع قابل پرداخت"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("کل پرداخت‌ها برونداد"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("قیمت کل"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("مجموع محصول"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("کل سود"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("مجموع خرید"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("مجموع مبلغ بازگشتی"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("مجموع بازگشت‌ها"),
        "totalSale": MessageLookupByLibrary.simpleMessage("مجموع فروش"),
        "totalSales": MessageLookupByLibrary.simpleMessage("مجموع فروش"),
        "totalVat": MessageLookupByLibrary.simpleMessage("مالیات کل"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("کل پرداخت‌ها در"),
        "transaction": MessageLookupByLibrary.simpleMessage("تراکنش"),
        "transactionId": MessageLookupByLibrary.simpleMessage("شناسه تراکنش"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("گزارش تراکنش"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("تلاش مجدد"),
        "type": MessageLookupByLibrary.simpleMessage("نوع"),
        "unPaid": MessageLookupByLibrary.simpleMessage("پرداخت نشده"),
        "unit": MessageLookupByLibrary.simpleMessage("واحد"),
        "unitName": MessageLookupByLibrary.simpleMessage("نام واحد"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("قیمت واحد"),
        "unlimited": MessageLookupByLibrary.simpleMessage("نامحدود"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("صورتحساب‌های نامحدود"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("استفاده نامحدود"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "پتانسیل کامل Pos Saas POS را با جلسات آموزش شخصی توسط تیم متخصص ما به دست آورید. از مباحث پایه تا تکنیک‌های پیشرفته، ما تضمین می‌کنیم که شما در استفاده از هر جنبه از سیستم برای بهینه‌سازی فرآیندهای کسب‌وکارتان ماهر شوید."),
        "updateNow": MessageLookupByLibrary.simpleMessage("به‌روزرسانی اکنون"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "ابتدا طرح خود را به روز کنید\nسقف فروش به پایان رسیده است."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("ارتقاء در اپلیکیشن موبایل"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("تصویری بارگذاری کنید"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("آپلود لوگوی فاکتور"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("آپلود سند"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("آپلود فایل"),
        "userName": MessageLookupByLibrary.simpleMessage("نام کاربری"),
        "userRole": MessageLookupByLibrary.simpleMessage("نقش کاربری"),
        "userRoleName": MessageLookupByLibrary.simpleMessage("نام نقش کاربری"),
        "userTitle": MessageLookupByLibrary.simpleMessage("عنوان کاربری"),
        "vatOrgst":
            MessageLookupByLibrary.simpleMessage("مالیات/مالیت مصرف کننده"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("تأیید شماره تلفن"),
        "view": MessageLookupByLibrary.simpleMessage("مشاهده"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("مشتری حضوری"),
        "warranty": MessageLookupByLibrary.simpleMessage("گارانتی"),
        "warrantys": MessageLookupByLibrary.simpleMessage("گارانتی"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "ما برای شروع نیاز به ثبت شماره تلفن شما داریم!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "ما اهمیت عملیات بی‌درز را درک می‌کنیم. به همین دلیل پشتیبانی ۲۴ ساعته ما برای کمک به شما در هر سوال سریع یا مسئله جامعی در دسترس است. با ما در هر زمان و هر مکان از طریق تماس تلفنی یا WhatsApp تماس بگیرید و تجربه خدمات مشتری بی‌نظیری داشته باشید."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("قیمت عمده فروشی"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("عمده‌فروش"),
        "wholesale": MessageLookupByLibrary.simpleMessage("عمده فروشی"),
        "wight": MessageLookupByLibrary.simpleMessage("وزن"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("بله، بازگردانی"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "شما باید مجددا وارد حساب کاربری خود شوید."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "قبل از خرید پیام‌ها باید هویت خود را تأیید کنید"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("لیست تمامی فروش‌های شما"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("تمامی فروش‌های شما"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("شما در حال استفاده از"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("فروش‌های بدهی شما"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "قبل از خرید پیام‌ها باید هویت خود را تأیید کنید"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("پکیج شما"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("پرداخت شما لغو شده است"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "پرداخت شما با موفقیت انجام شد"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("پرداخت شما لغو شده است")
      };
}
