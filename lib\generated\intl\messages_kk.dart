// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a kk locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'kk';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("САТЫП АЛУ ҚОСУ"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("САНАТ"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("Төлем"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("ТРМ Сату"),
        "PRICE": MessageLookupByLibrary.simpleMessage("БАҒА"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("ТОВАР АТАУЫ"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Логин панели"),
        "QTY": MessageLookupByLibrary.simpleMessage("САНЫ"),
        "STATUS": MessageLookupByLibrary.simpleMessage("МӘНІ"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("БАРЛЫҒЫ МӘНІ"),
        "UserTitle":
            MessageLookupByLibrary.simpleMessage("Пайдаланушы тақырыбы"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Бағдарлама туралы"),
        "accountName": MessageLookupByLibrary.simpleMessage("Есеп аты"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Есеп нөмірі"),
        "action": MessageLookupByLibrary.simpleMessage("Әрекет"),
        "add": MessageLookupByLibrary.simpleMessage("Қосу"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Бренд қосу"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Категория қосу"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Тапсырысшы Қосу"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Сипаттама қосу...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("Құжатты қосу"),
        "addItem": MessageLookupByLibrary.simpleMessage("Элементті қосу"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Элемент санатын қосу"),
        "addNew": MessageLookupByLibrary.simpleMessage("Жаңа қосу"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Жаңа пайдаланушы қосу"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Өнім қосу"),
        "addSuccessful": MessageLookupByLibrary.simpleMessage("Сәтті қосу"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Тапсырыс берушіні қосу"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Бірлік қосу"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("Шығыс Тізімін қосу/жаңарту"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("Табыс тізімін қосу/жаңарту"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Пайдаланушы рөлін қосу"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Сериялық нөмірді қосу?"),
        "address": MessageLookupByLibrary.simpleMessage("Мекен-жай"),
        "all": MessageLookupByLibrary.simpleMessage("Барлығы"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Барлық негізгі мүмкіндіктер"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Алдымен тіркелгенсіз бе?"),
        "amount": MessageLookupByLibrary.simpleMessage("Сомма"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Android және iOS қолдауы"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Сіз бұл котировканы жасау келісесіз бе?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Бұл тапсырысшын егер жою керек пе?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Сіз бұл тауарды жою келгенініздіңізді білесіз бе"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Бұл котировкаға жазылу керек пе?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Бұл сатып алуды қайтару керек пе?"),
        "balance": MessageLookupByLibrary.simpleMessage("Баланс"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Банк есептелу валютасы"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Банктік Есептіктер"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Банк ақпараты"),
        "bankName": MessageLookupByLibrary.simpleMessage("Банк аты"),
        "between": MessageLookupByLibrary.simpleMessage("Аралық"),
        "billTo": MessageLookupByLibrary.simpleMessage("Төлем адасуы:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Филиал аты"),
        "brand": MessageLookupByLibrary.simpleMessage("Бренд"),
        "brandName": MessageLookupByLibrary.simpleMessage("Бренд атауы"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Бизнес Категориясы"),
        "buy": MessageLookupByLibrary.simpleMessage("Сатып алу"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Премиум план сатып алу"),
        "buySms": MessageLookupByLibrary.simpleMessage("SMS сатып алу"),
        "calculator": MessageLookupByLibrary.simpleMessage("Калькулятор:"),
        "camera": MessageLookupByLibrary.simpleMessage("Камера"),
        "cancel": MessageLookupByLibrary.simpleMessage("Болдырмау"),
        "capacity": MessageLookupByLibrary.simpleMessage("Шұрындылық"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Ақылы & Банк"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Қолдаған Ақша"),
        "categories": MessageLookupByLibrary.simpleMessage("Санаттар"),
        "category": MessageLookupByLibrary.simpleMessage("Категория"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Категория Аты"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Алмау Сомасы"),
        "changeableAmount": MessageLookupByLibrary.simpleMessage(
            "Өзгерту мүмкін болатын Сумма"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Гарантияны тексеру"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Тарифті таңдаңыз"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Төлемді тіркеу >"),
        "color": MessageLookupByLibrary.simpleMessage("Түс"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Компания Аты"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Компания мекен-жайы"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Компания сипаттамасы"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Компания электрондық пошта мекенжайы"),
        "companyName": MessageLookupByLibrary.simpleMessage("Компания Аты"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Компания телефоны"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Компания веб-сайты URL"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Құпиясөзді растау"),
        "continu": MessageLookupByLibrary.simpleMessage("Жалғастыру"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Сатып алудың басын басу"),
        "create": MessageLookupByLibrary.simpleMessage("Жасау"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Төлем жасау"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Жасалған"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Креативтік Хаб"),
        "currency": MessageLookupByLibrary.simpleMessage("Валюта"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Ағымдағы тариф"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Жеке тапсырыс брендингі"),
        "customer": MessageLookupByLibrary.simpleMessage("Тапсырышшы"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Клиенттік қарыз"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Клиенттік төлемдер"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Тапсырысшы Тізімі"),
        "customerName": MessageLookupByLibrary.simpleMessage("Тапсырысшы Аты"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Айдың Тапсырышшысы"),
        "customerType": MessageLookupByLibrary.simpleMessage("Клиенттік түр"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Тапсырысшы: Турау Клиент"),
        "customers": MessageLookupByLibrary.simpleMessage("Клиенттер"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("Күнгі Төлем"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Күнгі Сатып Алу"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Күн сайынғы іс-шаралар"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Баспана"),
        "date": MessageLookupByLibrary.simpleMessage("Күні"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Уақыт"),
        "dealer": MessageLookupByLibrary.simpleMessage("Дилер"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Дилер бағасы"),
        "delete": MessageLookupByLibrary.simpleMessage("Жою"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Жеткізу төлемі"),
        "description": MessageLookupByLibrary.simpleMessage("Сипаттама"),
        "details": MessageLookupByLibrary.simpleMessage("Мәліметтер >"),
        "discount": MessageLookupByLibrary.simpleMessage("Жеңілдік"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Жеңілдік бағасы"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("PDF жүктеу"),
        "due": MessageLookupByLibrary.simpleMessage("Боры"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Төлемге тиіс сумма"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Егер қолжетімді болса, өткен денгей көрсетілетін жер"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Қолжетімдіктер Топталуы"),
        "dueList":
            MessageLookupByLibrary.simpleMessage("Қолжетімдіктер тізімі"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Төлем алу іс-шаралары"),
        "edit": MessageLookupByLibrary.simpleMessage("Түзету"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Өңдеу/Серияны қосу:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Профильіңізді түзетіңіз"),
        "email": MessageLookupByLibrary.simpleMessage("Электронды пошта"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Соманы енгізіңіз"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Бренд атауын енгізіңіз"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Санат атауын енгізіңіз"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Компания сипаттамасын енгізіңіз"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Компания электрондық пошта мекенжайын енгізіңіз"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Компания телефонын енгізіңіз"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Компания веб-сайты URL-сын енгізіңіз"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage(
            "Тапсырысшының атын енгізіңіз"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Дилер бағасын енгізіңіз"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Жеңілдік бағасын енгізіңіз"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Шығыс Категориясын Енгізіңіз"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Шығыс Күнін Енгізіңіз"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("Кіру Категориясын Енгізіңіз"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Табыс күнін енгізіңіз"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Өнім табысының атауын енгізіңіз"),
        "enterName": MessageLookupByLibrary.simpleMessage("Атын Енгізіңіз"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Атын енгізіңіз"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Ескерту Енгізіңіз"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Ашықтық балансын енгізіңіз"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Төленген соманы енгізіңіз"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Құпия сөзді енгізіңіз"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Төлем сомасын енгізіңіз"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Бағасын енгізіңіз"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Товардың қабілетін енгізіңіз"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Өнім кодын енгізіңіз"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Товардың түсін енгізіңіз"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Өнімнің атауын енгізіңіз"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("Өнім санын енгізіңіз"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Товардың өлшемін енгізіңіз"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Өнім түрін енгізіңіз"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Товар бірлігін енгізіңіз"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Товардың салмағын енгізіңіз"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Сатып алу бағасын енгізіңіз"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Сілтеме Нөмірін Енгізіңіз"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Сатып алу бағасын енгізіңіз"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Сериялық нөмірді енгізіңіз"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("Хабар мазмұнын енгізіңіз"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Сақталу санын енгізіңіз"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Транзакцияның ID нөмірін енгізіңіз"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Бірлік атауын енгізіңіз"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Пайдаланушы рөл атын енгізіңіз"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Пайдаланушы тақырыбын енгізіңіз"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Гарантияны енгізіңіз"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Опттық бағасын енгізіңіз"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Соманы енгізіңіз"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Мекен-жайыңызды енгізіңіз"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Компания мекен-жайын енгізіңіз"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Компаниянызды енгізіңіз"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Компаниянызды енгізіңіз"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Электронды пошта мекен-жайын енгізіңіз"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Құпиясөзді енгізіңіз"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Құпиясөзді қайтадан енгізіңіз"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Телефон нөмірін енгізіңіз"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Дүкен атын енгізіңіз"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Категория Атын Енгізіңіз"),
        "expense": MessageLookupByLibrary.simpleMessage("Лауазым"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Шығыс Күні"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Шығыс Толықтыру"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Шығыс үшін"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("Шығыс Категория Тізімі"),
        "expenses": MessageLookupByLibrary.simpleMessage("Лауазымдар"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Айдың төп бес сатып алу продукті"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Шектімсіз пайдалану үшін"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Құпиясөзді ұмыттыңыз ба?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Тегін деректер жасау қолдау"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Тегін өмір бойы жаңарту"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Тегін пакет"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Тегін план"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Бастау"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Ұлттық куәлік"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Жалпы басым"),
        "hold": MessageLookupByLibrary.simpleMessage("Тұту"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Тұту нөмірі"),
        "identityVerify": MessageLookupByLibrary.simpleMessage("Тексеру жасау"),
        "inc": MessageLookupByLibrary.simpleMessage("Тиісінік"),
        "income": MessageLookupByLibrary.simpleMessage("Кіру"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Табыс категориясы"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Түсінікті амалдар тізімі"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Табыс күні"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Түсінік деталдары"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Табыс үшін"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Табыс тізімі"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Тауардың жасауын кеңейту"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Жедел Құпиялылық"),
        "invoice": MessageLookupByLibrary.simpleMessage("Счет"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Счет:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Тіркесім НӘ.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Төлем №"),
        "item": MessageLookupByLibrary.simpleMessage("Тауар"),
        "itemName": MessageLookupByLibrary.simpleMessage("Өнімнің аты"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("КМУ тексеру"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Леджер деталдары"),
        "ledger": MessageLookupByLibrary.simpleMessage("Леджер"),
        "left": MessageLookupByLibrary.simpleMessage("Сол жақ"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Қарыздык Есептіктер"),
        "logOut": MessageLookupByLibrary.simpleMessage("Шығу"),
        "login": MessageLookupByLibrary.simpleMessage("Кіру"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Логотипті салымда орналастыру?"),
        "loss": MessageLookupByLibrary.simpleMessage("Зарар"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Зарар/Кепіл"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Зиян(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Төменгі қойым"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Аз Склад"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Сіздің клиенттеріңізді қосымша тапсырыстармен қалау мүмкін. Біздің Шекті жаңарту брендингі жеке мүмкіндіктерді қостайтын жеке артықшылығы бар - сіздің брендіңізді тақтайтын профессионалдық ұстаздарымыздың басқаруына жаңалық қосу мен клиенттердің сыйлықтықты жаңарту."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Өнім табысы"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Кіру Панелі"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Тіркелу Панелі"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Мобильді қолдама\n+\nНегізгі бет"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Ақша төлемі"),
        "nam": MessageLookupByLibrary.simpleMessage("Аты*"),
        "name": MessageLookupByLibrary.simpleMessage("Аты"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Жаңа Клиенттер"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Жаңа Клиенттер"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Жаңа табыс"),
        "no": MessageLookupByLibrary.simpleMessage("Жоқ"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Байланыс жоқ"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Тапсырысшы табылмады"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Борынша Транзакция Табылмады"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("Шығыс Категория Табылмады"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("Түсінікті амал табылған жоқ"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Табыс табылған жоқ"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Төлем табылмады"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Товар табылмады"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Сатып алу іс-шаралары табылмады"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Котировка табылмады"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Есептілік табылмады"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("Сатып алу жүргізу табылмады"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Сериялық нөмір табылмады"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Тапсырыс беруші табылмады"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Өткен кезеңдер табылған жоқ"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Пайдаланушы табылмады"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Пайдаланушы рөлі табылмады"),
        "note": MessageLookupByLibrary.simpleMessage("Ескерту"),
        "ok": MessageLookupByLibrary.simpleMessage("Окей"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Ашық Шектер"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Ашықтық Балансы"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            " немесе PNG, JPG файлдарды тартып жіберіңіз"),
        "orders": MessageLookupByLibrary.simpleMessage("Тапсырыстар"),
        "other": MessageLookupByLibrary.simpleMessage("Басқа"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Басқа Кіру"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Пакеттік мүмкіндік"),
        "paid": MessageLookupByLibrary.simpleMessage("Төленген"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Төленген сома"),
        "partyName": MessageLookupByLibrary.simpleMessage("Тарап атауы"),
        "partyType": MessageLookupByLibrary.simpleMessage("Тарап Түрі"),
        "password": MessageLookupByLibrary.simpleMessage("Құпиясөз"),
        "payCash": MessageLookupByLibrary.simpleMessage("Қолма қол ақша төлеу"),
        "payable": MessageLookupByLibrary.simpleMessage("Төлеуі керек"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Төленген Сумма"),
        "payment": MessageLookupByLibrary.simpleMessage("Төлем"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Төлем ішінгі"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Төлем шығысы"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Төлем түрі"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Төлем түрлері"),
        "phone": MessageLookupByLibrary.simpleMessage("Телефон"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Телефон Нөмірі"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Телефонды тексеру"),
        "pleaseAddASale": MessageLookupByLibrary.simpleMessage("Сату Қосыңыз"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Клиентті қосу керек"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Інтернет байланысыңызды тексеріңіз"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Біздің мобильді қосымшаны жүктеу және жұмыс жасау үшін пакетке жазылыңыз"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Тауардың жасауын енгізіңіз"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Тура деректерді енгізіңіз"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Тапсырышшы таңдаңыз"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Тура деректерді енгізіңіз"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Тіркелу панели"),
        "practies": MessageLookupByLibrary.simpleMessage("Практикалар"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Премиум қолдау қызметі"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Премиум план"),
        "preview": MessageLookupByLibrary.simpleMessage("Алдын-ала қарау"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Алдыңғы төлем:"),
        "price": MessageLookupByLibrary.simpleMessage("Баға"),
        "print": MessageLookupByLibrary.simpleMessage("Шығару"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("Төлемшотты басу"),
        "printPdf": MessageLookupByLibrary.simpleMessage("PDF басып шығару"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Құпиялылық саясаты"),
        "product": MessageLookupByLibrary.simpleMessage("Өнім"),
        "productCategory": MessageLookupByLibrary.simpleMessage("Товар санаты"),
        "productColor": MessageLookupByLibrary.simpleMessage("Товардың түсі"),
        "productList": MessageLookupByLibrary.simpleMessage("Өнім тізімі"),
        "productName": MessageLookupByLibrary.simpleMessage("Товар атауы"),
        "productSize": MessageLookupByLibrary.simpleMessage("Товардың өлшемі"),
        "productStock": MessageLookupByLibrary.simpleMessage("Тауар жасауы"),
        "productType": MessageLookupByLibrary.simpleMessage("Өнім түрі"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Өнім бірлігі"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Товардың салмағы"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Товардың қабілеті"),
        "prof": MessageLookupByLibrary.simpleMessage("Профиль"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Профильді өңдеу"),
        "profit": MessageLookupByLibrary.simpleMessage("Кепіл"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Кепіл(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Кепіл(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Сатып алу"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Сатып алу тізімі"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Премиум планнан сатып алу"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Сатып алу бағасы"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Сатып алу жүргізу"),
        "quantity": MessageLookupByLibrary.simpleMessage("Саны*"),
        "quotation": MessageLookupByLibrary.simpleMessage("Котировка"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Котировка тізімі"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Соңғы сатылулар"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Жиналған сома"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Сілтеме №"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Сілтеме Нөмірі"),
        "registration": MessageLookupByLibrary.simpleMessage("Тіркелу"),
        "remaining": MessageLookupByLibrary.simpleMessage("Қалған: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Қалған баланс"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Қалған қарыз"),
        "reports": MessageLookupByLibrary.simpleMessage("Есептер"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Құпиясөзді қалпына келтіру"),
        "retailer": MessageLookupByLibrary.simpleMessage("Дистрибьютор"),
        "revenue": MessageLookupByLibrary.simpleMessage("Түсінік"),
        "right": MessageLookupByLibrary.simpleMessage("Оң жақ"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Сатылу сомасы"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Сіздің бизнес деректеріңізді оқтасыздар. Біздің Pos Saas POS Шекті жаңартуы, сіздің арнайы деректеріңізді қору, дайындалмайтын событыларға қарсы қорғау қамтамасыз етуі мүмкін. Сіздің бизнес дамуыңыздың сауатыңа байланысыны жасаңыз."),
        "sale": MessageLookupByLibrary.simpleMessage("Сату"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Сатылым сомасы"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Сату Толықтырулары"),
        "saleList": MessageLookupByLibrary.simpleMessage("Сатып алу тізімі"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Сатып беру бағасы"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Сату бағасы*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Сатып алу қайтару"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Сатып алу жүргізу"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Сатып алу жасау (Сауда тарихы)"),
        "sales": MessageLookupByLibrary.simpleMessage("Сатулар"),
        "salesList": MessageLookupByLibrary.simpleMessage("Сату тізімі"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Сақтау және жариялау"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Сақтау және Жариялау"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Өзгерістерді сақтау"),
        "search": MessageLookupByLibrary.simpleMessage("Іздеу..."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Іздеу Не болса..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Тапсырыс бойынша іздеу...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Счет немесе аты бойынша іздеу"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Аты бойынша іздеу"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Аты немесе телефон бойынша іздеу..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Сериялық нөмірді іздеу"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Тараптарды таңдау"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Өнім брендін таңдау"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Сериялық нөмірді таңдау"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Өзгерістерді таңдау:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Гарантия уақытын таңдау"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Тіліңізді таңдаңыз"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Хабар жіберу"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Сериялық нөмір"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Сериялық нөмір"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Қызмет төлемі"),
        "setting": MessageLookupByLibrary.simpleMessage("Параметрлер"),
        "share": MessageLookupByLibrary.simpleMessage("Бөлісу"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Жеткізу/Басқа"),
        "shopName": MessageLookupByLibrary.simpleMessage("Дүкен Аты"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Дүкенін ашу балансы"),
        "show": MessageLookupByLibrary.simpleMessage("Көрсету >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Логотипті салымда көрсету?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Жеткізу/қызмет"),
        "size": MessageLookupByLibrary.simpleMessage("Өлшем"),
        "statistic": MessageLookupByLibrary.simpleMessage("Статистика"),
        "status": MessageLookupByLibrary.simpleMessage("Мәртебе"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Технологиялардың алғашқы жолында болу. Біздің ПОС Saas POS Шекті шинжүйек жаңарту, сіздің қолдарыңызда әрқайсысында соңғы құралдар мен мүмкіндіктер бар. Сіздің бизнесіңізді сақтау тиімді болады."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Технологиялардың алғашқы жолында болу. Біздің ПОС Sass POS Шекті жаңарту, сіздің қолдарыңызда әрқайсысында соңғы құралдар мен мүмкіндіктер бар, сіздің бизнесіңізді жаңарту туралы кепілдік жасау."),
        "stock": MessageLookupByLibrary.simpleMessage("Сақталу"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Склад Деректері"),
        "stockReport": MessageLookupByLibrary.simpleMessage(
            "Товардың қалдығы туралы есептілік"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Склад Құны"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Есеп қойылымдары"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Барлығы"),
        "subciption": MessageLookupByLibrary.simpleMessage("Жазылу"),
        "submit": MessageLookupByLibrary.simpleMessage("Жіберу"),
        "supplier": MessageLookupByLibrary.simpleMessage("Тапсырыс берушілер"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Тапсырыс берушінің қарызы"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Тапсырыс беруші төлемі"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Тапсырыс берушілер тізімі"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT коды"),
        "tSale": MessageLookupByLibrary.simpleMessage("Жалпы сатылу"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Жолаушы куәлігі, ұлттық куәлік немесе паспорт фотосын жаттыңыз"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Пайдалану шарттары"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Атты әрбір несіге айналдырады. Pos Saas POS Шекті, сіздің пайдалануыңызда шектіліктер жоқ. Сіз бір несіге қолдарыңызды қабылдау немесе клиенттердің сүйеуімен жиыладым дейін жұмыс істейсіз деп ойлайсыз, сізді бір несіге қиін емесіңіздің беттік мүмкіндіктерімен шектелмейдігіңізді білуге және сенімділік арттыруға ұмтыларымыздың арқылы."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Бұл тапсырышшыда қолжетім жоқ"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Бұл тапсырысшыда алдыңғы боры бар"),
        "to": MessageLookupByLibrary.simpleMessage("дейін"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Ең Үздік Сату Тауары"),
        "total": MessageLookupByLibrary.simpleMessage("жалпы"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Барлығы сомасы"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Жалпы жеу"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Жалпы Төлем"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Жалпы қарыздар"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Жалпы Шығыс"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Жалпы табыс"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Барлық Тауар: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Жалпы зарар"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Барлығы төленді"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Барлығы төленетін"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Төлем шығысын барлығы"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Жалпы баға"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("Барлық өнімдер"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Жалпы кепіл"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Жалпы сатып алу"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Жалпы қайтару сомасы"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Жалпы қайтарулар"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Жалпы сатылым"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Жалпы Сатып Алу"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Жалпы алків"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Төлем ішінгі барлығы"),
        "transaction": MessageLookupByLibrary.simpleMessage("Транзакция"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Транзакция ID"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Жүктеу тізімі"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Қайта сынау"),
        "type": MessageLookupByLibrary.simpleMessage("Тип"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Төленген жоқ"),
        "unit": MessageLookupByLibrary.simpleMessage("Бірлік"),
        "unitName": MessageLookupByLibrary.simpleMessage("Бірлік атауы"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Бірлік бағасы"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Шектімсіз"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Шектеусіз счеттер"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Шектімді пайдалану"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Пос Saas POS жүйесінің толық мүмкіндігін ашық. Біздің көптік тімдіктерге бағыштау күнделіктік оқушыларымыздың басты жаттығулары мен қосымшаларының барлық жолын пайдалану үшін сізді оқытамыз. Негіздеме де, көтерілген техникаларға дейініміңізді алу үшін сізге оқыту үшін ықпал етуге дайындайды."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Қазір жаңарту"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Өнімділікті жаңартыңыз\\nСатым мерзімі аяқталды."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Мобильді Қолданбада Жаңарту"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("Сурет жүктеу"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Салым логотипін жүктеу"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Құжаттарды жүктеу"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Файлды жүктеу"),
        "userName": MessageLookupByLibrary.simpleMessage("Пайдаланушы аты"),
        "userRole": MessageLookupByLibrary.simpleMessage("Пайдаланушы рөлі"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Пайдаланушы рөл аты"),
        "userTitle":
            MessageLookupByLibrary.simpleMessage("Пайдаланушы тақырыбы"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("Көлем/ГСМ"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Телефон нөмірін тексеру"),
        "view": MessageLookupByLibrary.simpleMessage("Көру"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Турау Клиент"),
        "warranty": MessageLookupByLibrary.simpleMessage("Гарантия"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Гарантия"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Бастапқы болу үшін сіздің телефон нөміріңізді тіркеусіз!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Біз ықпалды жұмыс істеу ең маңызды болғанын түсінеміз. Сондықтан біздің сағатына бір уақыт ішінде жауап беру қолдандауға қолдау жасау мүмкін, сізге қызметкерлерге ұсыныс немесе толығырақ мәселе болса дайын болу мүмкін. Бізге қандай уақыт, қайдаланып, қайтысы болмауы керек, дайындаған болу үшін қоңырау жасаңыз - қанша да, қайда да."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Жалпы сатым бағасы"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Бүкіл сатушы"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Опттық"),
        "wight": MessageLookupByLibrary.simpleMessage("Салмақ"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Иә, қайтару"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Сіздің тіркелуіңізді қайта кіру қажет."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Хабарларды сатып алу алдын КМУ тексеру керек"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Сіздің барлық сату тізіміңіз"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Сіздің Барлық Сатуларыңыз"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("Сіз пайдаланасыз"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Сіздің Өткен Сатуларыңыз"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Хабарларды сатып алу алдын КМУ тексеру керек"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Сіздің пакетіңіз"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Сіздің төлеміңіз болды"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Сіздің төлеміңіз сәтті аяқталды"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Төлеміз болды")
      };
}
