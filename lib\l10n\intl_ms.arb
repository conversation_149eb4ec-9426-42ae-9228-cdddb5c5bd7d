{"PosSaasLoginPanel": "Panel Log Masuk Pos Saas", "posSaasSingUpPanel": "Panel Pendaftaran Pos Saas", "addNewUser": "Tambah Pengguna Baru", "userRole": "<PERSON><PERSON><PERSON>", "addNew": "Tambah Baru", "orders": "<PERSON><PERSON><PERSON>", "revenue": "Pendapatan", "userName": "<PERSON><PERSON>", "noUserFound": "Tiada Pengguna Dijumpai", "all": "<PERSON><PERSON><PERSON>", "profileEdit": "Sunting Profil", "practies": "<PERSON><PERSON><PERSON>", "salesList": "<PERSON><PERSON><PERSON>", "enterPassword": "<PERSON><PERSON><PERSON><PERSON>", "noUserRoleFound": "Tiada Peranan <PERSON> Dijumpai", "addUserRole": "Tambah Peranan Pen<PERSON>una", "UserTitle": "Tajuk Pengguna", "enterUserTitle": "Masukkan Ta<PERSON>", "userTitle": "Tajuk Pengguna", "addSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "youHaveToRelogin": "Anda perlu LOG MASUK semula ke akaun anda.", "ok": "OK", "payCash": "<PERSON>ar <PERSON>", "freeLifeTimeUpdate": "<PERSON><PERSON><PERSON>", "androidIOSAppSupport": "Sokongan Aplikasi Android & iOS", "premiumCustomerSupport": "Sokongan Pelanggan Premium", "customInvoiceBranding": "Pengekalan Jentera Invois Pengekalan", "unlimitedUsage": "<PERSON><PERSON><PERSON><PERSON>", "freeDataBackup": "Pencadangan Data Percuma", "stayAtTheForFront": "Tetap di hadapan perkembangan teknologi tanpa sebarang kos tambahan. Kemaskini Tanpa Had Pos Saas POS kami memastikan anda sentiasa memiliki alat dan ciri terkini di hujung jari anda, menja<PERSON> pernia<PERSON>an anda sentiasa di hadapan.", "weUnderStand": "<PERSON><PERSON> memahami kepentingan operasi yang lancar. <PERSON><PERSON><PERSON> mengapa sokongan sepanjang masa kami sentiasa ada untuk membantu anda, sama ada untuk pertanyaan pantas atau kebimbangan menyeluruh. Hubungi kami pada bila-bila masa, di mana-mana melalui panggilan atau WhatsApp untuk mengalami perkhidmatan pelanggan yang tiada tandingan.", "unlockTheFull": "<PERSON><PERSON> potensi penuh Pos Saas POS dengan sesi latihan peribadi yang dipimpin oleh pasukan pakar kami. <PERSON>i asas hingga teknik lanjutan, kami memastikan anda mahir dalam menggunakan setiap aspek sistem untuk mengoptimumkan proses perniagaan anda.", "makeALastingImpression": "<PERSON><PERSON>t kesan yang berkekalan kepada pelanggan anda dengan invois berjenama. Kemaskini Tanpa Had kami menawarkan kelebihan unik penyesuaian invois anda, menambah sentuhan profesional yang memperkuat identiti jenama anda dan membina kesetiaan pelanggan.", "theNameSysIt": "<PERSON>a itu mencakup segalanya. Dengan Pos Saas POS Tanpa Had, tidak ada had pada penggunaan anda. Sama ada anda memproses beberapa transaksi atau menghadapi lonjakan pelanggan, anda boleh beroperasi dengan yakin, men<PERSON><PERSON><PERSON> anda tidak terikat oleh had.", "safegurardYourBusinessDate": "Lindungi data perniagaan anda dengan mudah. Kemaskini Tanpa Had Pos Saas POS kami termasuk pencadangan data percuma, memastikan maklumat berharga anda dilindungi daripada sebarang peristiwa yang tidak dijangka. Tumpukan kepada apa yang benar-benar penting - pertumbuhan perniagaan anda.", "buy": "Bel<PERSON>", "bankInformation": "Maklumat Bank", "bankName": "Nama Bank", "branchName": "<PERSON><PERSON>", "accountName": "<PERSON><PERSON>", "accountNumber": "<PERSON><PERSON><PERSON>", "bankAccountingCurrecny": "Mata <PERSON> Akaun Bank", "swiftCode": "Kod SWIFT", "enterTransactionId": "Masukkan ID Transaksi", "uploadDocument": "Muat Naik Dokumen", "uploadFile": "<PERSON>at <PERSON>", "aboutApp": "Mengenai Aplikasi", "termsOfUse": "<PERSON><PERSON>", "privacyPolicy": "Polisi Privasi", "userRoleName": "<PERSON><PERSON>", "enterUserRoleName": "<PERSON><PERSON><PERSON><PERSON>", "yourPackage": "<PERSON><PERSON>", "freePlan": "<PERSON><PERSON><PERSON>", "yourAreUsing": "<PERSON><PERSON>", "freePackage": "<PERSON><PERSON>", "premiumPlan": "Pelan Premium", "packageFeature": "<PERSON><PERSON>", "remaining": "Baki: ", "unlimited": "<PERSON><PERSON>", "forUnlimitedUses": "Untuk <PERSON>", "updateNow": "<PERSON><PERSON><PERSON>", "purchasePremiumPlan": "Beli Pelan Premium", "stayAtTheForeFrontOfTechnological": "Tetap di hadapan perkembangan teknologi tanpa sebarang kos tambahan. Kemaskini Tanpa Had Pos Sass POS kami memastikan anda sentiasa memiliki alat dan ciri terkini di hujung jari anda, menja<PERSON> pernia<PERSON>an anda sentiasa di hadapan.", "buyPremiumPlan": "Beli Pelan Premium", "mobilePlusDesktop": "Aplikasi Mudah Alih\n+\nDesktop", "transactionId": "ID Transaksi", "productStock": "Stok Produk", "pleaseEnterProductStock": "<PERSON><PERSON> masukkan stok produk", "increaseStock": "Tambah Stok", "areYouWantToDeleteThisProduct": "<PERSON><PERSON><PERSON> anda ingin memadam produk ini", "noConnection": "Tiada <PERSON>n", "pleaseCheckYourInternetConnectivity": "Sila periksa konektiviti Internet anda", "tryAgain": "Cuba Lagi", "currency": "<PERSON>", "businessCategory": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "enterYourCompanyName": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Nombor Telefon", "enterYourPhoneNumber": "Masukkan nombor telefon anda", "shopOpeningBalance": "Pembukaan Ba<PERSON>", "enterYOurAmount": "<PERSON><PERSON><PERSON><PERSON> jumlah anda", "continu": "Teruskan", "resetYourPassword": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "enterYourEmailAddress": "<PERSON><PERSON><PERSON><PERSON> alamat e-mel anda", "pleaseDownloadOurMobileApp": "Sila muat turun aplikasi mudah alih kami dan langgan pakej untuk menggunakan versi desktop", "mobiPosLoginPanel": "Panel Log Masuk Pos Saas", "enterYourPassword": "<PERSON><PERSON><PERSON><PERSON>", "login": "Log Ma<PERSON>k", "password": "<PERSON><PERSON>", "forgotPassword": "Lupa Kata Laluan?", "registration": "Pendaftaran", "editYourProfile": "Edit profil anda", "uploadAImage": "<PERSON>at naik imej", "orDragAndDropPng": " atau seret & lepas PNG, JPG", "comapnyName": "<PERSON><PERSON>", "enterYourCompanyNames": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "enterYourAddress": "<PERSON><PERSON><PERSON><PERSON>", "mobiPosSignUpPane": "Panel Pendaftaran Pos Saas", "confirmPassword": "<PERSON><PERSON><PERSON> kata laluan", "enterYourPasswordAgain": "<PERSON><PERSON><PERSON><PERSON> kata laluan anda sekali lagi", "alreadyHaveAnAccounts": "<PERSON><PERSON>h anda sudah mempunyai akaun?", "choseAplan": "<PERSON><PERSON><PERSON> pelan", "allBasicFeatures": "<PERSON><PERSON><PERSON>", "unlimitedInvoice": "<PERSON><PERSON><PERSON>", "getStarted": "<PERSON><PERSON>", "currentPlan": "<PERSON><PERSON><PERSON>", "selectYourLanguage": "<PERSON><PERSON><PERSON> bahasa anda", "shopName": "<PERSON><PERSON>", "enterYourShopName": "<PERSON><PERSON><PERSON><PERSON>", "phoneVerification": "Pengesahan Telefon", "weNeedToRegisterYourPhone": "<PERSON><PERSON> perlu mendaftarkan telefon anda sebelum memulakan!", "verifyPhoneNumber": "Sahkan Nombor Telefon", "customerName": "<PERSON><PERSON>", "enterCustomerName": "<PERSON><PERSON><PERSON><PERSON>", "openingBalance": "<PERSON><PERSON>", "enterOpeningBalance": "Ma<PERSON>kka<PERSON>", "type": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "saveAndPublish": "Simpan & Terbitkan", "customerList": "<PERSON><PERSON><PERSON>", "searchByNameOrPhone": "Cari mengikut <PERSON> atau Telefon...", "addCustomer": "Tambah Pelanggan", "partyName": "<PERSON><PERSON>", "partyType": "<PERSON><PERSON>", "phone": "Telefon", "due": "Tanggungan", "edit": "Edit", "delete": "Padam", "areYouWantToDeleteThisCustomer": "<PERSON><PERSON><PERSON> anda mahu memadam pelanggan ini?", "thisCustomerHavepreviousDue": "Pelanggan ini mempunyai hutang terdahulu", "noCustomerFound": "Tiada Pelanggan Dijumpai", "totalDue": "<PERSON><PERSON><PERSON>", "customers": "Pelanggan", "supplier": "<PERSON><PERSON><PERSON><PERSON>", "collectDue": "Kumpulkan Hutang >", "noDueTransantionFound": "Tiada Transaksi Hutang Dijumpai", "createPayment": "B<PERSON>t <PERSON>", "grandTotal": "<PERSON><PERSON><PERSON>", "payingAmount": "<PERSON><PERSON><PERSON>", "enterPaidAmount": "<PERSON><PERSON><PERSON><PERSON> jumlah yang <PERSON>ar", "changeAmount": "<PERSON><PERSON><PERSON>", "dueAmount": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON>", "submit": "Hantar", "enterExpanseCategory": "<PERSON><PERSON><PERSON><PERSON>", "pleaseEnterValidData": "Sila masukkan data yang sah", "categoryName": "<PERSON><PERSON>", "entercategoryName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Penerangan", "addDescription": "Tambah penerangan....", "expensecategoryList": "<PERSON><PERSON><PERSON>", "searchByInvoice": "Cari mengikut invois....", "addCategory": "Tambah Kategori", "action": "<PERSON><PERSON><PERSON>", "noExpenseCategoryFound": "<PERSON><PERSON><PERSON>", "expenseDetails": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "referenceNo": "No. Rujukan", "amount": "<PERSON><PERSON><PERSON>", "note": "Catatan", "nam": "<PERSON>a*", "income": "Pendapatan", "addUpdateExpenseList": "Tambah/Kemaskini <PERSON>", "expenseDate": "<PERSON><PERSON><PERSON>", "enterExpenseDate": "<PERSON><PERSON><PERSON><PERSON>", "expenseFor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterName": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "Nombor Rujukan", "enterReferenceNumber": "Masukkan Nombor Rujukan", "enterNote": "Masukkan Catatan", "between": "<PERSON><PERSON>", "to": "<PERSON>", "totalExpense": "<PERSON><PERSON><PERSON>", "totalSales": "<PERSON><PERSON><PERSON>", "purchase": "Bel<PERSON>", "newCustomers": "Pelanggan Baharu", "dailySales": "<PERSON><PERSON>", "dailyCollection": "<PERSON><PERSON><PERSON><PERSON>", "instantPrivacy": "<PERSON><PERSON><PERSON><PERSON>", "stockInventory": "Inventori Stok", "stockValue": "<PERSON><PERSON>", "lowStocks": "Stok Rendah", "other": "Lain-lain", "otherIncome": "Pendapatan Lain", "MOBIPOS": "<PERSON><PERSON>", "newCusotmers": "Pelanggan Baharu", "enterIncomeCategory": "<PERSON><PERSON><PERSON><PERSON>", "pleaseentervaliddata": "Sila masukkan data yang sah", "saveAndPublished": "Simpan & Terbitkan", "incomeCategoryList": "<PERSON><PERSON><PERSON>", "noIncomeCategoryFound": "Tiada Kategori Pendapatan Dijumpai", "incomeDetails": "Butiran <PERSON>", "paymentTypes": "<PERSON><PERSON>", "totalIncome": "Pendapatan Keseluruhan", "incomeList": "Senarai <PERSON>", "incomeCategory": "<PERSON><PERSON><PERSON>", "newIncome": "Pendapatan Baharu", "createdBy": "<PERSON><PERSON><PERSON>", "view": "Lihat", "noIncomeFound": "Tiada Pendapatan Dijumpai", "addUpdateIncomeList": "Tambah/Kemaskini Senarai Pendapatan", "incomeDate": "<PERSON><PERSON><PERSON>", "enterIncomeDate": "<PERSON><PERSON><PERSON><PERSON>", "incomeFor": "Pendapatan Untuk", "enterNames": "<PERSON><PERSON><PERSON><PERSON>", "enterAmount": "<PERSON><PERSON><PERSON><PERSON>", "printInvoice": "Cetak Invois", "moneyReciept": "<PERSON><PERSON><PERSON>", "billTo": "Kepada:", "invoiceNo": "No. Invois", "totalDues": "<PERSON><PERSON><PERSON>", "paidAmount": "<PERSON><PERSON><PERSON>", "remainingDue": "<PERSON>tang <PERSON>", "deliveryCharge": "<PERSON><PERSON>", "INVOICE": "INVOIS", "product": "Produk", "quantity": "<PERSON><PERSON><PERSON>", "unitPrice": "<PERSON><PERSON>", "totalPrice": "<PERSON><PERSON><PERSON>", "subTotal": "<PERSON><PERSON><PERSON>", "totalVat": "<PERSON><PERSON><PERSON>", "totalDiscount": "<PERSON><PERSON><PERSON>", "payable": "<PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON><PERSON>", "serviceCharge": "<PERSON><PERSON>", "totalSale": "<PERSON><PERSON><PERSON>", "totalPurchase": "<PERSON><PERSON><PERSON>", "recivedAmount": "<PERSON><PERSON><PERSON>", "customerDue": "<PERSON><PERSON><PERSON>", "supplierDue": "<PERSON><PERSON><PERSON>", "selectParties": "<PERSON><PERSON><PERSON>", "details": "Butiran >", "show": "Tunjuk >", "noTransactionFound": "Tiada Transaksi Dijumpai", "ledgeDetails": "<PERSON><PERSON><PERSON>", "status": "Status", "itemName": "<PERSON><PERSON>", "purchasePrice": "<PERSON><PERSON>", "salePrice": "<PERSON><PERSON>", "profit": "<PERSON><PERSON><PERSON><PERSON>", "loss": "Kerugian", "total": "<PERSON><PERSON><PERSON>", "totalProfit": "<PERSON><PERSON><PERSON><PERSON>", "totalLoss": "<PERSON><PERSON><PERSON>", "unPaid": "Tidak Dibayar", "lossOrProfit": "Keuntungan/Kerugian", "saleAmount": "<PERSON><PERSON><PERSON>", "profitPlus": "<PERSON><PERSON><PERSON><PERSON>(+)", "profitMinus": "Kerugian(-)", "yourPaymentIsCancelled": "<PERSON><PERSON><PERSON><PERSON> anda telah di<PERSON>an", "yourPaymentIsSuccessfully": "<PERSON><PERSON><PERSON><PERSON> anda telah berjaya", "hold": "<PERSON><PERSON>", "holdNumber": "No<PERSON><PERSON>", "selectSerialNumber": "<PERSON><PERSON><PERSON>", "serialNumber": "Nombor Siri", "searchSerialNumber": "Cari Nombor Siri", "noSerialNumberFound": "Tiada Nombor Siri Dijumpai", "nameCodeOrCateogry": "<PERSON><PERSON> atau <PERSON> atau Kategor<PERSON>", "vatOrgst": "Cukai <PERSON>/GST", "discount": "<PERSON><PERSON><PERSON>", "areYouWantToCreateThisQuation": "<PERSON><PERSON>h anda ingin membuat sebut harga ini?", "updateYourPlanFirst": "<PERSON><PERSON> kema<PERSON>i rancangan anda terlebih dahulu\\nHad Jualan telah tamat.", "quotation": "<PERSON><PERSON>", "addProduct": "Tambah Produk", "totalProduct": "<PERSON><PERSON><PERSON>", "shpingOrServices": "Penghantaran/Perkhidmatan", "addItemCategory": "Tambah Kategori Item", "selectVariations": "<PERSON><PERSON><PERSON>:", "size": "Saiz", "color": "<PERSON><PERSON>", "wight": "<PERSON><PERSON>", "capacity": "Kapasiti", "warranty": "Jaminan", "addBrand": "Tambah Jenama", "brandName": "<PERSON><PERSON>", "enterBrandName": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "Tambah Unit", "unitName": "Nama Unit", "enterUnitName": "Masukkan Nama Unit", "productNam": "<PERSON><PERSON>*", "enterProductName": "<PERSON><PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON>", "enterProductType": "<PERSON><PERSON><PERSON><PERSON>", "productWaranty": "Jaminan <PERSON>", "enterWarranty": "<PERSON><PERSON><PERSON><PERSON>", "warrantys": "Jaminan", "selectWarrantyTime": "<PERSON><PERSON><PERSON>", "brand": "Jenama", "selectProductBrand": "<PERSON><PERSON><PERSON>", "productCod": "<PERSON><PERSON>*", "enterProductCode": "Masukkan Kod Produk", "enterProductQuantity": "<PERSON><PERSON><PERSON><PERSON>", "Quantity": "Ku<PERSON><PERSON>*", "productUnit": "Unit Produk", "enterPurchasePrice": "<PERSON><PERSON><PERSON><PERSON>", "salePrices": "<PERSON><PERSON>*", "dealerPrice": "<PERSON><PERSON>", "enterDealePrice": "<PERSON><PERSON><PERSON><PERSON>", "wholeSaleprice": "<PERSON><PERSON>", "enterPrice": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Pembuat", "enterManufacturerName": "<PERSON><PERSON><PERSON><PERSON>", "serialNumbers": "Nombor Siri", "enterSerialNumber": "Masukkan Nombor Siri", "nosSerialNumberFound": "Tiada Nombor Siri Dijumpai", "productList": "<PERSON><PERSON><PERSON>", "searchByName": "<PERSON><PERSON> men<PERSON>", "retailer": "Peruncit", "dealer": "Peniaga", "wholesale": "Runcit", "expense": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalPayable": "<PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON>", "searchByInvoiceOrName": "<PERSON>i mengikut invois atau nama", "invoice": "Invois", "lossminus": "Kerugian(-)", "yourPaymentIscancelled": "<PERSON><PERSON><PERSON><PERSON> anda telah di<PERSON>an", "previousDue": "Terdahulu:", "calculator": "Kalkulator:", "dashBoard": "<PERSON><PERSON>", "price": "<PERSON><PERSON>", "create": "Buat", "payment": "Pembayaran", "enterPayingAmount": "<PERSON><PERSON><PERSON><PERSON>", "enterCategoryName": "<PERSON><PERSON><PERSON><PERSON>", "productSize": "<PERSON>z Produk", "enterProductSize": "Masukkan Saiz Produk", "productColor": "<PERSON><PERSON>", "enterProductColor": "<PERSON><PERSON><PERSON><PERSON>", "productWeight": "<PERSON><PERSON>", "enterProductWeight": "<PERSON><PERSON><PERSON><PERSON>", "productcapacity": "Kapasiti Produk", "enterProductCapacity": "<PERSON><PERSON><PERSON><PERSON> Produk", "enterSalePrice": "<PERSON><PERSON><PERSON><PERSON>", "add": "Tambah", "productCategory": "<PERSON><PERSON><PERSON>", "enterProductUnit": "Masukkan Unit Produk", "productName": "<PERSON><PERSON>", "noProductFound": "Tiada Produk Dijumpai", "addingSerialNumber": "Menambah Nombor Siri?", "unit": "Unit", "editOrAddSerial": "Edit/Tambah Siri:", "enterWholeSalePrice": "<PERSON><PERSON><PERSON><PERSON>", "invoiceCo": "Invois:", "categories": "<PERSON><PERSON><PERSON>", "purchaseList": "<PERSON><PERSON><PERSON>", "print": "Cetak", "noPurchaseTransactionFound": "Tiada transaksi pembelian dijumpai", "quotationList": "<PERSON><PERSON><PERSON>", "areYouWantToDeleteThisQuotion": "<PERSON><PERSON><PERSON> anda mahu memadam <PERSON> ini?", "convertToSale": "<PERSON><PERSON>", "noQuotionFound": "Tiada Ku<PERSON> Diju<PERSON>", "stockReport": "<PERSON><PERSON><PERSON>", "PRODUCTNAME": "NAMA PRODUK", "CATEGORY": "KATEGORI", "PRICE": "HARGA", "QTY": "JUMLAH", "STATUS": "STATUS", "TOTALVALUE": "JUMLAH NILAI", "noReportFound": "TIADA Laporan Dijumpai", "remainingBalance": "Baki Baki", "totalpaymentIn": "<PERSON><PERSON><PERSON>", "totalPaymentOut": "<PERSON><PERSON><PERSON>", "dailyTransaction": "<PERSON><PERSON><PERSON>", "paymentIn": "Pembayaran Masuk", "paymentOut": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Baki", "totalPaid": "<PERSON><PERSON><PERSON>", "dueTransaction": "Transaksi Tertunggak", "downloadPDF": "Lataa PDF", "customerType": "Asiakastyyppi", "pleaseAddCustomer": "Lisää asiakas", "purchaseTransaction": "Ostotapahtuma", "printPdf": "Tulosta PDF", "saleTransactionQuatationHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Tarjousmyyntihistoria)", "ADDSALE": "LISÄÄ MYYNTI", "search": "Hae...", "transactionReport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saleTransaction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "totalReturns": "Palautukset yhteensä", "totalReturnAmount": "Palautussumma yhteensä", "saleReturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noSaleTransaactionFound": "Myyntitapahtumia ei lö<PERSON>yt", "saleList": "<PERSON><PERSON><PERSON><PERSON>", "reports": "Raportit", "areYouWantToReturnThisSale": "<PERSON><PERSON><PERSON><PERSON> palauttaa tämän myynnin?", "no": "<PERSON>i", "yesReturn": "Kyllä, palauta", "setting": "Asetukset", "uploadAnInvoiceLogo": "Lataa laskun logo", "showLogoInInvoice": "Näytä logo laskussa?", "logoPositionInInvoice": "Logon sijainti las<PERSON>?", "left": "<PERSON><PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>", "companyAddress": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "enterYourCompanyAddress": "Syötä y<PERSON>ksen osoite", "companyPhoneNumber": "<PERSON><PERSON><PERSON><PERSON>", "companyEmailAddress": "Y<PERSON>ksen sähköpostiosoite", "enterCompanyPhoneNumber": "Syötä y<PERSON>ksen pu<PERSON>innumero", "enterCompanyEmailAddress": "Syötä yrityksen sähköpostiosoite", "companyWebsiteUrl": "<PERSON><PERSON><PERSON>en verkkosivuston osoite", "enterCompanyWebsiteUrl": "Syötä yrityksen verkkosivuston osoite", "companyDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "enterCompanyDesciption": "Syötä y<PERSON>en kuvaus", "saveChanges": "<PERSON><PERSON><PERSON>", "kycVerification": "KYC-tarkistus", "identityVerify": "<PERSON><PERSON><PERSON><PERSON>", "yourNeedToIdentityVerify": "Sinun on vahvistettava henkilöllisyytesi ennen viestien ostamista", "govermentId": "Virallin<PERSON> henkilö<PERSON>", "takeADriveLisense": "<PERSON><PERSON>, he<PERSON><PERSON><PERSON><PERSON><PERSON> tai passikuva", "addDucument": "Lisää asiakirja", "youNeedToIdentityVerifySms": "Sinun on vahvistettava henkilöllisyytesi ennen viestien ostamista", "wholeSeller": "Tukkumyyjä", "enterSmsContent": "Syötä viestin si<PERSON>", "sendMessage": "Lähetä viesti", "buySms": "<PERSON>sta tekstiviestej<PERSON>", "supplierList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addSupplier": "Lisää toimittaja", "noSupplierFound": "Toimittajia ei lö<PERSON>ynyt", "checkWarranty": "Tarkista takuu", "customerInvoices": "Asiakaslaskut", "supplierInvoice": "<PERSON><PERSON><PERSON><PERSON><PERSON> lasku", "addItem": "<PERSON><PERSON><PERSON><PERSON> tuote", "noInvoiceFound": "Laskuja ei löytynyt", "stock": "<PERSON><PERSON><PERSON>", "enterStockAmount": "Syötä varaston määrä", "discountPrice": "Alennettu hinta", "enterDiscountPrice": "Syötä alennettu hinta", "dateTime": "Päivämäärä ja aika", "walkInCustomer": "Astumiskävijä", "saleDetails": "Myyntitiedot", "customerWalkIncostomer": "Asiakas: Astumiskävijä", "item": "<PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "totalItem2": "Kokonaismäärä: 2", "shipingOrOther": "Toimit<PERSON>/<PERSON>u", "yourDueSales": "<PERSON><PERSON><PERSON>", "yourAllSales": "<PERSON><PERSON><PERSON>", "invoiceHint": "Laskun numero...", "customer": "Asiakas", "dueAmountWillShowHere": "Avoin summa näytetään täss<PERSON>, jos sa<PERSON>", "thisCustmerHasNoDue": "T<PERSON>llä asiakkaalla ei ole avointa summaa", "pleaseSelectACustomer": "Valitse asiakas", "pleaseAddASale": "<PERSON><PERSON><PERSON><PERSON>", "yourAllSaleList": "<PERSON><PERSON><PERSON>", "changeableAmount": "<PERSON><PERSON><PERSON><PERSON> summa", "sales": "<PERSON><PERSON><PERSON>", "dueList": "Avoin lista", "ledger": "Päiväkirja", "transaction": "Tapahtuma", "subciption": "<PERSON><PERSON><PERSON>", "upgradeOnMobileApp": "Päivitä mobiilisovelluksessa", "POSSale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchAnyThing": "Etsi mitä tahansa...", "sale": "<PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashAndBank": "<PERSON><PERSON><PERSON><PERSON> ja pankki", "cashInHand": "Käteinen kädessä", "bankAccounts": "Pankkitilit", "creativeHub": "Luovuuden keskus", "openCheques": "Avoimet sekit", "loanAccounts": "Lainatilit", "share": "Jaa", "preview": "Esikatselu", "dueCollection": "Saamisten kerääminen", "customerOfTheMonth": "<PERSON><PERSON><PERSON><PERSON>", "topSellingProduct": "<PERSON><PERSON>n myyty tuote", "statistic": "Statistik", "stockValues": "<PERSON><PERSON>", "lowStock": "Stok Rendah", "fivePurchase": "Lima Produk Paling Me<PERSON>li-<PERSON><PERSON>", "recentSale": "Jualan Terkini", "tSale": "Jualan Keseluruhan", "sAmount": "<PERSON><PERSON><PERSON>", "expenses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inc": "Pendapatan", "prof": "Profil"}