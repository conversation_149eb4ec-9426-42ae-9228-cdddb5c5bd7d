name: salespro_admin
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=2.17.5 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter


  cupertino_icons: ^1.0.2
  nb_utils: ^7.0.4
  google_fonts: ^6.1.0
  flutter_feather_icons: ^2.0.0+1
  material_design_icons_flutter: ^7.0.7296
  font_awesome_flutter: ^10.1.0
  image_picker: ^1.0.2
  dotted_border: ^3.0.1
  flutter_simple_calculator: ^2.2.2
  url_strategy: ^0.3.0
  country_pickers: ^3.0.1
  firebase_core: ^3.4.0
  firebase_app_check: ^0.3.0+5
  flutter_riverpod: ^2.3.2
  firebase_database: ^11.1.0
  firebase_auth: ^5.2.0
  firebase_storage: ^12.2.0
  flutter_easyloading: ^3.0.5
  image_picker_web: ^4.0.0
  pdf: ^3.8.3
  cached_network_image: ^3.2.1
  simple_barcode_scanner: ^0.3.0
  path_provider: ^2.0.11
  open_file: ^3.2.1
  printing: ^5.9.3
  http_auth: ^1.0.1
  http: ^1.1.0
  auto_size_text: ^3.0.0
  date_time_format: ^2.0.1
  contacts_service: ^0.6.3
  permission_handler: ^11.3.1
  syncfusion_flutter_pdf: ^29.2.11
  number_to_character: ^0.0.12
  pinput: ^5.0.0
  intl: ^0.20.2
  provider: ^6.0.5
  dropdown_button2: ^2.1.4
  flag: ^7.0.0
  file_picker: ^10.2.0
  mailer: ^6.0.1
  connectivity_plus: ^6.0.5
  internet_connection_checker: ^3.0.1
  iconly: ^1.0.1
  flutter_typeahead: ^5.2.0
  flutter_svg: ^2.0.7
  barcode_widget: ^2.0.4
  excel: ^4.0.2
  restart_app: ^1.2.1
  fl_chart: ^1.0.0



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
flutter:
  uses-material-design: true
  assets:
    - images/
flutter_intl:
  enabled: true
