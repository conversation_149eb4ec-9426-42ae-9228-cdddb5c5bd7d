// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a nl locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'nl';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("VERKOOP TOEVOEGEN"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATEGORIE"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FACTUUR"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS-verkoop"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRIJS"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("PRODUCTNAAM"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Inlogpaneel"),
        "QTY": MessageLookupByLibrary.simpleMessage("AANTAL"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Hoeveelheid*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUS"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("TOTALE WAARDE"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Gebruikerstitel"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("Over de App"),
        "accountName": MessageLookupByLibrary.simpleMessage("Rekeningnaam"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Rekeningnummer"),
        "action": MessageLookupByLibrary.simpleMessage("Actie"),
        "add": MessageLookupByLibrary.simpleMessage("Toevoegen"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Merk toevoegen"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Categorie toevoegen"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Klant toevoegen"),
        "addDescription": MessageLookupByLibrary.simpleMessage(
            "Voeg een omschrijving toe..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Document toevoegen"),
        "addItem": MessageLookupByLibrary.simpleMessage("Item toevoegen"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("Itemcategorie toevoegen"),
        "addNew": MessageLookupByLibrary.simpleMessage("Nieuw Toevoegen"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Nieuwe Gebruiker Toevoegen"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Product toevoegen"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Succesvol Toegevoegd"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Leverancier toevoegen"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Eenheid toevoegen"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Uitgavelijst toevoegen/bijwerken"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Inkomstenlijst toevoegen/bijwerken"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Gebruikersrol Toevoegen"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Serienummer toevoegen?"),
        "address": MessageLookupByLibrary.simpleMessage("Adres"),
        "all": MessageLookupByLibrary.simpleMessage("Alle"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Alle basisfuncties"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Heeft u al een account?"),
        "amount": MessageLookupByLibrary.simpleMessage("Bedrag"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Ondersteuning voor Android en iOS App"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Wilt u deze offerte aanmaken?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Wilt u deze klant verwijderen?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Wilt u dit product verwijderen"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Wilt u deze offerte verwijderen?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Wilt u deze verkoop retourneren?"),
        "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Valuta van Bankrekening"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("Bankrekeningen"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("Bankgegevens"),
        "bankName": MessageLookupByLibrary.simpleMessage("Banknaam"),
        "between": MessageLookupByLibrary.simpleMessage("Tussen"),
        "billTo": MessageLookupByLibrary.simpleMessage("Factuuradres:"),
        "branchName": MessageLookupByLibrary.simpleMessage("Vestigingsnaam"),
        "brand": MessageLookupByLibrary.simpleMessage("Merk"),
        "brandName": MessageLookupByLibrary.simpleMessage("Merknaam"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Bedrijfscategorie"),
        "buy": MessageLookupByLibrary.simpleMessage("Kopen"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Koop Premium Plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Koop sms"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calculator:"),
        "camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annuleren"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capaciteit"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Contant en bank"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Contant in hand"),
        "categories": MessageLookupByLibrary.simpleMessage("Categorieën"),
        "category": MessageLookupByLibrary.simpleMessage("Categorie"),
        "categoryName": MessageLookupByLibrary.simpleMessage("Categorienaam"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Restbedrag"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Veranderbaar bedrag"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Garantie controleren"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Kies een plan"),
        "collectDue": MessageLookupByLibrary.simpleMessage(
            "Incasseer openstaande facturen >"),
        "color": MessageLookupByLibrary.simpleMessage("Kleur"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("Bedrijfsnaam"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("Bedrijfsadres"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Bedrijfsomschrijving"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("Bedrijfs e-mailadres"),
        "companyName": MessageLookupByLibrary.simpleMessage("Bedrijfsnaam"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Bedrijfstelefonnummer"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("Bedrijfswebsite-URL"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Bevestig wachtwoord"),
        "continu": MessageLookupByLibrary.simpleMessage("Doorgaan"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Converteren naar verkoop"),
        "create": MessageLookupByLibrary.simpleMessage("Aanmaken"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Betaling aanmaken"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Gemaakt door"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Creatieve hub"),
        "currency": MessageLookupByLibrary.simpleMessage("Valuta"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Huidig plan"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Aangepaste Factuur Branding"),
        "customer": MessageLookupByLibrary.simpleMessage("Klant"),
        "customerDue":
            MessageLookupByLibrary.simpleMessage("Klant openstaande bedrag"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Klantfacturen"),
        "customerList": MessageLookupByLibrary.simpleMessage("Klantenlijst"),
        "customerName": MessageLookupByLibrary.simpleMessage("Klantnaam"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Klant van de maand"),
        "customerType": MessageLookupByLibrary.simpleMessage("Klanttype"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Klant: Loop-in klant"),
        "customers": MessageLookupByLibrary.simpleMessage("Klanten"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Dagelijkse incasso"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Dagelijkse omzet"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Dagelijkse transactie"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "date": MessageLookupByLibrary.simpleMessage("Datum"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Datum Tijd"),
        "dealer": MessageLookupByLibrary.simpleMessage("Dealer"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Dealerprijs"),
        "delete": MessageLookupByLibrary.simpleMessage("Verwijderen"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("Verzendkosten"),
        "description": MessageLookupByLibrary.simpleMessage("Omschrijving"),
        "details": MessageLookupByLibrary.simpleMessage("Details >"),
        "discount": MessageLookupByLibrary.simpleMessage("Korting"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("Kortingsprijs"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Download PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Te betalen"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Openstaand bedrag"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Het openstaande bedrag wordt hier weergegeven als het beschikbaar is"),
        "dueCollection": MessageLookupByLibrary.simpleMessage(
            "Incasseren van openstaande facturen"),
        "dueList": MessageLookupByLibrary.simpleMessage("Openstaande lijst"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Openstaande transactie"),
        "edit": MessageLookupByLibrary.simpleMessage("Bewerken"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Bewerk/Voeg serienummer toe:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Bewerk je profiel"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Voer bedrag in"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Voer de merknaam in"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Voer de categorienaam in"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Voer de bedrijfsbeschrijving in"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Voer het bedrijfse-mailadres in"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Voer het bedrijfstelefoonnummer in"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Voer de bedrijfswebsite-URL in"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage(
            "Voer de naam van de klant in"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("Voer de dealerprijs in"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Voer de kortingsprijs in"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("Voer de uitgavecategorie in"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Voer de uitgavedatum in"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Voer de inkomstencategorie in"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Voer de inkomstendatum in"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Voer de naam van de fabrikant in"),
        "enterName": MessageLookupByLibrary.simpleMessage("Voer de naam in"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Voer naam in"),
        "enterNote":
            MessageLookupByLibrary.simpleMessage("Voer een notitie in"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Voer de openingsbalans in"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Voer het betaalde bedrag in"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Voer Wachtwoord In"),
        "enterPayingAmount": MessageLookupByLibrary.simpleMessage(
            "Voer het te betalen bedrag in"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Voer de prijs in"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Voer de productcapaciteit in"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Voer de productcode in"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("Voer de productkleur in"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Voer de productnaam in"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Voer de producthoeveelheid in"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Voer de productafmeting in"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Voer het producttype in"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Voer de producteenheid in"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Voer het productgewicht in"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Voer de inkoopprijs in"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Voer het referentienummer in"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Voer de verkoopprijs in"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Voer het serienummer in"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Voer de inhoud van het bericht in"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Voer het aantal voorraden in"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Voer Transactie-Id In"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Voer de eenheidsnaam in"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("Voer Gebruikersrol Naam In"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Voer Gebruikerstitel In"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Voer de garantie in"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "Voer de groothandelsprijs in"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Voer uw bedrag in"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Voer uw adres in"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("Voer uw bedrijfsadres in"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("Voer uw bedrijfsnaam in"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("Voer uw bedrijfsnaam in"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Voer uw e-mailadres in"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Voer uw wachtwoord in"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Voer uw wachtwoord opnieuw in"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Voer uw telefoonnummer in"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("Voer uw winkelnaam in"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("Voer de categorienaam in"),
        "expense": MessageLookupByLibrary.simpleMessage("Uitgave"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Uitgavedatum"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Uitgavedetails"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Uitgave voor"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Lijst met uitgavecategorieën"),
        "expenses": MessageLookupByLibrary.simpleMessage("Uitgaven"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top vijf aankoopproducten van de maand"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Voor Onbeperkt Gebruik"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Wachtwoord vergeten?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Gratis Gegevensback-up"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Gratis Levenslange Update"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Gratis Pakket"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Gratis Plan"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Beginnen"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Overheids-ID"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Totaalbedrag"),
        "hold": MessageLookupByLibrary.simpleMessage("On hold"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("Holdnummer"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Identiteit verifiëren"),
        "inc": MessageLookupByLibrary.simpleMessage("Inkomsten"),
        "income": MessageLookupByLibrary.simpleMessage("Inkomsten"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Inkomstencategorie"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("Inkomstencategorielijst"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Inkomstendatum"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Inkomstendetails"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Inkomsten voor"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Inkomstenlijst"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Voorraad Verhogen"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Directe privacy"),
        "invoice": MessageLookupByLibrary.simpleMessage("Factuur"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Factuur:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Factuurnummer.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Factuurnummer"),
        "item": MessageLookupByLibrary.simpleMessage("Item"),
        "itemName": MessageLookupByLibrary.simpleMessage("Itemnaam"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC Verificatie"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Boekhoudkundige details"),
        "ledger": MessageLookupByLibrary.simpleMessage("Ledger"),
        "left": MessageLookupByLibrary.simpleMessage("Links"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Leningrekeningen"),
        "logOut": MessageLookupByLibrary.simpleMessage("Uitloggen"),
        "login": MessageLookupByLibrary.simpleMessage("Inloggen"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("Logopositie in factuur?"),
        "loss": MessageLookupByLibrary.simpleMessage("Verlies"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Verlies/winst"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Verlies(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Lage Voorraad"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Lage voorraden"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Maak een blijvende indruk op uw klanten met op maat gemaakte facturen. Onze Onbeperkte Upgrade biedt het unieke voordeel van het aanpassen van uw facturen, waardoor een professionele uitstraling wordt toegevoegd die uw merkidentiteit versterkt en klantenloyaliteit bevordert."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Fabrikant"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Login Paneel"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas Aanmeldpaneel"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("Mobiele App\n+\nDesktop"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Geldontvangstbewijs"),
        "nam": MessageLookupByLibrary.simpleMessage("Naam*"),
        "name": MessageLookupByLibrary.simpleMessage("Naam"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Naam, code of categorie"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nieuwe klanten"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nieuwe klanten"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nieuwe inkomsten"),
        "no": MessageLookupByLibrary.simpleMessage("Nee"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Geen Verbinding"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Geen klant gevonden"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Geen openstaande transactie gevonden"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Geen uitgavecategorie gevonden"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Geen inkomstencategorie gevonden"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Geen inkomsten gevonden"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Geen factuur gevonden"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Geen product gevonden"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Geen inkooptransactie gevonden"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Geen offerte gevonden"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("Geen rapport gevonden"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Geen verkooptransactie gevonden"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Geen serienummer gevonden"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Geen leverancier gevonden"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Geen transactie gevonden"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Geen Gebruiker Gevonden"),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("Geen Gebruikersrol Gevonden"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("Geen serienummer gevonden"),
        "note": MessageLookupByLibrary.simpleMessage("Notitie"),
        "ok": MessageLookupByLibrary.simpleMessage("Oké"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Open cheques"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Openingsbalans"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage(" of sleep PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Bestellingen"),
        "other": MessageLookupByLibrary.simpleMessage("Overig"),
        "otherIncome":
            MessageLookupByLibrary.simpleMessage("Overige inkomsten"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("Pakketfunctie"),
        "paid": MessageLookupByLibrary.simpleMessage("Betaald"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Betaald bedrag"),
        "partyName": MessageLookupByLibrary.simpleMessage("Naam van de partij"),
        "partyType": MessageLookupByLibrary.simpleMessage("Soort partij"),
        "password": MessageLookupByLibrary.simpleMessage("Wachtwoord"),
        "payCash": MessageLookupByLibrary.simpleMessage("Contant Betalen"),
        "payable": MessageLookupByLibrary.simpleMessage("Te betalen"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("Te betalen bedrag"),
        "payment": MessageLookupByLibrary.simpleMessage("Betaling"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Inkomende betaling"),
        "paymentOut":
            MessageLookupByLibrary.simpleMessage("Uitgaande betaling"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Betalingsmethode"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Betalingsmethode"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefoon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Telefoonnummer"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Telefoonverificatie"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Voeg een verkoop toe"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Voeg een klant toe"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Controleer alstublieft uw internetverbinding"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Download onze mobiele app en abonneer u op een pakket om de desktopversie te gebruiken"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("Voer productvoorraad in"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Voer geldige gegevens in"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Selecteer een klant"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Voer geldige gegevens in"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas Aanmeldpaneel"),
        "practies": MessageLookupByLibrary.simpleMessage("Oefeningen"),
        "premiumCustomerSupport": MessageLookupByLibrary.simpleMessage(
            "Premium Klantenondersteuning"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium Plan"),
        "preview": MessageLookupByLibrary.simpleMessage("Voorbeeld"),
        "previousDue":
            MessageLookupByLibrary.simpleMessage("Vorige openstaande bedrag:"),
        "price": MessageLookupByLibrary.simpleMessage("Prijs"),
        "print": MessageLookupByLibrary.simpleMessage("Afdrukken"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Factuur afdrukken"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Print PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacybeleid"),
        "product": MessageLookupByLibrary.simpleMessage("Product"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Productcategorie"),
        "productCod": MessageLookupByLibrary.simpleMessage("Productcode*"),
        "productColor": MessageLookupByLibrary.simpleMessage("Productkleur"),
        "productList": MessageLookupByLibrary.simpleMessage("Productlijst"),
        "productNam": MessageLookupByLibrary.simpleMessage("Productnaam*"),
        "productName": MessageLookupByLibrary.simpleMessage("Productnaam"),
        "productSize": MessageLookupByLibrary.simpleMessage("Productafmeting"),
        "productStock": MessageLookupByLibrary.simpleMessage("Productvoorraad"),
        "productType": MessageLookupByLibrary.simpleMessage("Producttype"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Producteenheid"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Productgarantie"),
        "productWeight": MessageLookupByLibrary.simpleMessage("Productgewicht"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Productcapaciteit"),
        "prof": MessageLookupByLibrary.simpleMessage("Profiel"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Profiel Bewerken"),
        "profit": MessageLookupByLibrary.simpleMessage("Winst"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Winst(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Winst(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Aankopen"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Inkooplijst"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Premium Plan Kopen"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Aankoopprijs"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Aankooptransactie"),
        "quantity": MessageLookupByLibrary.simpleMessage("Hoeveelheid"),
        "quotation": MessageLookupByLibrary.simpleMessage("Offerte"),
        "quotationList": MessageLookupByLibrary.simpleMessage("Offertelijst"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Recente Verkopen"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Ontvangen bedrag"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("Referentienummer"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Referentienummer"),
        "registration": MessageLookupByLibrary.simpleMessage("Registratie"),
        "remaining": MessageLookupByLibrary.simpleMessage("Resterend: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Resterende saldo"),
        "remainingDue": MessageLookupByLibrary.simpleMessage(
            "Resterende openstaande bedragen"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapporten"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Reset uw wachtwoord"),
        "retailer": MessageLookupByLibrary.simpleMessage("Retailer"),
        "revenue": MessageLookupByLibrary.simpleMessage("Inkomsten"),
        "right": MessageLookupByLibrary.simpleMessage("Rechts"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Verkoopbedrag"),
        "sale": MessageLookupByLibrary.simpleMessage("Verkoop"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Verkoopbedrag"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Verkoopdetails"),
        "saleList": MessageLookupByLibrary.simpleMessage("Verkooplijst"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Verkoopprijs"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Verkoopprijs*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Verkoopretour"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Verkooptransactie"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Verkooptransacties (Offerte Verkoopgeschiedenis)"),
        "sales": MessageLookupByLibrary.simpleMessage("Verkopen"),
        "salesList": MessageLookupByLibrary.simpleMessage("Verkooplijst"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Opslaan en publiceren"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Opslaan & publiceren"),
        "saveChanges":
            MessageLookupByLibrary.simpleMessage("Wijzigingen opslaan"),
        "search": MessageLookupByLibrary.simpleMessage("Zoeken......."),
        "searchAnyThing": MessageLookupByLibrary.simpleMessage("Zoek iets..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Zoeken op factuur..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Zoeken op factuur of naam"),
        "searchByName": MessageLookupByLibrary.simpleMessage("Zoeken op naam"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Zoeken op naam of telefoon..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Zoek serienummer"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Partijen selecteren"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("Selecteer productmerk"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Selecteer serienummer"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Selecteer variaties:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("Selecteer garantieperiode"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Selecteer uw taal"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Stuur bericht"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serienummer"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("Serienummers"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("Servicekosten"),
        "setting": MessageLookupByLibrary.simpleMessage("Instelling"),
        "share": MessageLookupByLibrary.simpleMessage("Delen"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Verzending/Overig"),
        "shopName": MessageLookupByLibrary.simpleMessage("Winkelnaam"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("Winkelopeningsbalans"),
        "show": MessageLookupByLibrary.simpleMessage("Tonen >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("Logo in factuur weergeven?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Verzending/service"),
        "size": MessageLookupByLibrary.simpleMessage("Grootte"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistiek"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Blijf aan de voorhoede van technologische ontwikkelingen zonder extra kosten. Onze Pos Saas POS Onbeperkte Upgrade zorgt ervoor dat u altijd de nieuwste tools en functies binnen handbereik heeft, waardoor uw bedrijf toonaangevend blijft."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Blijf aan de voorhoede van technologische ontwikkelingen zonder extra kosten. Onze Pos Sass POS Onbeperkte Upgrade zorgt ervoor dat u altijd de nieuwste tools en functies binnen handbereik heeft, waardoor uw bedrijf toonaangevend blijft."),
        "stock": MessageLookupByLibrary.simpleMessage("Voorraad"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Voorraadinventaris"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Voorraadrapport"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Voorraadwaarde"),
        "stockValues": MessageLookupByLibrary.simpleMessage("Voorraadwaarden"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotaal"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abonnement"),
        "submit": MessageLookupByLibrary.simpleMessage("Verzenden"),
        "supplier": MessageLookupByLibrary.simpleMessage("Leveranciers"),
        "supplierDue": MessageLookupByLibrary.simpleMessage(
            "Leverancier openstaande bedrag"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Leveranciersfactuur"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Leverancierslijst"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT-code"),
        "tSale": MessageLookupByLibrary.simpleMessage("Totale Verkopen"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Maak een foto van een rijbewijs, identiteitskaart of paspoort"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Gebruiksvoorwaarden"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "De naam zegt het al. Met Pos Saas POS Onbeperkt is er geen limiet aan uw gebruik. Of u nu een handvol transacties verwerkt of te maken heeft met een toestroom van klanten, u kunt met vertrouwen opereren, wetende dat u niet beperkt wordt door limieten."),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "Deze klant heeft geen openstaande posten"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Deze klant heeft een eerdere schuld"),
        "to": MessageLookupByLibrary.simpleMessage("Tot"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Best verkochte product"),
        "total": MessageLookupByLibrary.simpleMessage("Totaal"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Totaalbedrag"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("Totaal korting"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Totaal te betalen"),
        "totalDues":
            MessageLookupByLibrary.simpleMessage("Totaal openstaande bedragen"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Totale uitgaven"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Totale inkomsten"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("Totaal item: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Totale verlies"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Totaal betaald"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("Totaal te betalen"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Totale uitgaande betalingen"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Totale prijs"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Totaal aantal producten"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Totale winst"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Totale aankoop"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Totaal retourbedrag"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Totale retouren"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Totale verkoop"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Totale omzet"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Totaal BTW"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Totale inkomende betalingen"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transactie"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Transactie-Id"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Transactierapport"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Opnieuw Proberen"),
        "type": MessageLookupByLibrary.simpleMessage("Type"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Onbetaald"),
        "unit": MessageLookupByLibrary.simpleMessage("Eenheid"),
        "unitName": MessageLookupByLibrary.simpleMessage("Eenheidsnaam"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Eenheidsprijs"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Onbeperkt"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Onbeperkte facturen"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Onbeperkt Gebruik"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Ontgrendel het volledige potentieel van Pos Saas POS met gepersonaliseerde trainingsessies geleid door ons deskundige team. Van de basis tot geavanceerde technieken zorgen wij ervoor dat u goed thuis bent in het gebruik van elk facet van het systeem om uw bedrijfsprocessen te optimaliseren."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Nu Bijwerken"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Update eerst uw plan\\nDe verkooplimiet is overschreden."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("Upgrade op mobiele app"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Upload een afbeelding"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("Factuurlogo uploaden"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Document Uploaden"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Bestand Uploaden"),
        "userName": MessageLookupByLibrary.simpleMessage("Gebruikersnaam"),
        "userRole": MessageLookupByLibrary.simpleMessage("Gebruikersrol"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Gebruikersrol Naam"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Gebruikerstitel"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("BTW/GST"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verifieer telefoonnummer"),
        "view": MessageLookupByLibrary.simpleMessage("Bekijk"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("Loop in klant"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantie"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garanties"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "We moeten uw telefoon registreren voordat we kunnen beginnen!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "We begrijpen het belang van naadloze bedrijfsvoering. Daarom is onze rond-de-klokondersteuning beschikbaar om u te helpen, of het nu om een snelle vraag of een uitgebreide zorg gaat. Neem op elk moment, overal contact met ons op via telefoon of WhatsApp om ongeëvenaarde klantenservice te ervaren."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Groothandelsprijs"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Groothandel"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Groothandel"),
        "wight": MessageLookupByLibrary.simpleMessage("Gewicht"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Ja Retour"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "U moet OPNIEUW INLOGGEN op uw account."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "U moet zich verifiëren voordat u berichten kunt kopen"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("Uw volledige verkooplijst"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("Al uw verkopen"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("U gebruikt"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Uw openstaande verkopen"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "U moet zich verifiëren voordat u berichten kunt kopen"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Uw Pakket"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Uw betaling is geannuleerd"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("Uw betaling is succesvol"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Uw betaling is geannuleerd")
      };
}
