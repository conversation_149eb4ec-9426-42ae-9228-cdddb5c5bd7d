// Desktop-specific implementations
import 'package:url_launcher/url_launcher.dart';

class PlatformHelper {
  static void setBeforeUnloadListener() {
    // No-op for desktop
  }
  
  static void redirectToUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
  
  static void downloadFile(String url, String filename) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
  
  static void openInNewTab(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
  
  static String getCurrentUrl() {
    return '';
  }
  
  static List<String> getPathSegments() {
    return [];
  }
}
