// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("AGREGAR VENTA"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATEGORÍA"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FACTURA"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("Venta POS"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRECIO"),
        "PRODUCTNAME":
            MessageLookupByLibrary.simpleMessage("NOMBRE DEL PRODUCTO"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Panel de inicio de sesión de Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("CANT"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Cantidad*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("ESTADO"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("VALOR TOTAL"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("Título de usuario"),
        "aboutApp":
            MessageLookupByLibrary.simpleMessage("Acerca de la aplicación"),
        "accountName":
            MessageLookupByLibrary.simpleMessage("Nombre de la cuenta"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Número de cuenta"),
        "action": MessageLookupByLibrary.simpleMessage("Acción"),
        "add": MessageLookupByLibrary.simpleMessage("Agregar"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Agregar marca"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Agregar categoría"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Agregar cliente"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Agregar descripción..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Agregar documento"),
        "addItem": MessageLookupByLibrary.simpleMessage("Agregar artículo"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage(
            "Agregar categoría de artículo"),
        "addNew": MessageLookupByLibrary.simpleMessage("Agregar nuevo"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("Agregar nuevo usuario"),
        "addProduct": MessageLookupByLibrary.simpleMessage("Agregar producto"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Agregado exitosamente"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Agregar proveedor"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Agregar unidad"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Agregar/Actualizar lista de gastos"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Agregar/Actualizar lista de ingresos"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("Agregar rol de usuario"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("¿Agregar número de serie?"),
        "address": MessageLookupByLibrary.simpleMessage("Dirección"),
        "all": MessageLookupByLibrary.simpleMessage("Todo"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("Todas las funciones básicas"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("¿Ya tiene una cuenta?"),
        "amount": MessageLookupByLibrary.simpleMessage("Importe"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Soporte para aplicaciones Android e iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "¿Desea crear esta cotización?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "¿Desea eliminar a este cliente?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "¿Deseas eliminar este producto?"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "¿Desea eliminar esta cotización?"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("¿Desea devolver esta venta?"),
        "balance": MessageLookupByLibrary.simpleMessage("Equilibrio"),
        "bankAccountingCurrecny": MessageLookupByLibrary.simpleMessage(
            "Moneda de la cuenta bancaria"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Cuentas bancarias"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Información bancaria"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nombre del banco"),
        "between": MessageLookupByLibrary.simpleMessage("Entre"),
        "billTo": MessageLookupByLibrary.simpleMessage("Factura a:"),
        "branchName":
            MessageLookupByLibrary.simpleMessage("Nombre de la sucursal"),
        "brand": MessageLookupByLibrary.simpleMessage("Marca"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nombre de la marca"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Categoría de negocio"),
        "buy": MessageLookupByLibrary.simpleMessage("Comprar"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Comprar el plan premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Comprar SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calculadora:"),
        "camera": MessageLookupByLibrary.simpleMessage("Cámara"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacidad"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("Efectivo y banco"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Efectivo en mano"),
        "categories": MessageLookupByLibrary.simpleMessage("Categorías"),
        "category": MessageLookupByLibrary.simpleMessage("Categoría"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Nombre de la categoría"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Cambio"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Importe modificable"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Verificar la garantía"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("Elige un plan"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Cobrar Deuda >"),
        "color": MessageLookupByLibrary.simpleMessage("Color"),
        "comapnyName":
            MessageLookupByLibrary.simpleMessage("Nombre de la empresa"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Dirección de la empresa"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("Descripción de la empresa"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Dirección de correo electrónico de la empresa"),
        "companyName":
            MessageLookupByLibrary.simpleMessage("Nombre de la empresa"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Número de teléfono de la empresa"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "URL del sitio web de la empresa"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmar contraseña"),
        "continu": MessageLookupByLibrary.simpleMessage("Continuar"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Convertir a venta"),
        "create": MessageLookupByLibrary.simpleMessage("Crear"),
        "createPayment": MessageLookupByLibrary.simpleMessage("Crear Pago"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Creado por"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Centro creativo"),
        "currency": MessageLookupByLibrary.simpleMessage("Moneda"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Plan actual"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Marca personalizada en facturas"),
        "customer": MessageLookupByLibrary.simpleMessage("Cliente"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Cliente adeudado"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Facturas de clientes"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Lista de clientes"),
        "customerName":
            MessageLookupByLibrary.simpleMessage("Nombre del cliente"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Cliente del mes"),
        "customerType": MessageLookupByLibrary.simpleMessage("Tipo de cliente"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Cliente: Cliente que entra"),
        "customers": MessageLookupByLibrary.simpleMessage("Clientes"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("Cobro diario"),
        "dailySales": MessageLookupByLibrary.simpleMessage("Ventas diarias"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transacción diaria"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Tablero"),
        "date": MessageLookupByLibrary.simpleMessage("Fecha"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Fecha y hora"),
        "dealer": MessageLookupByLibrary.simpleMessage("Distribuidor"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Precio de distribuidor"),
        "delete": MessageLookupByLibrary.simpleMessage("Borrar"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Cargo por entrega"),
        "description": MessageLookupByLibrary.simpleMessage("Descripción"),
        "details": MessageLookupByLibrary.simpleMessage("Detalles >"),
        "discount": MessageLookupByLibrary.simpleMessage("Descuento"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Precio de descuento"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Descargar PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Debido"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Importe adeudado"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "El importe pendiente se mostrará aquí si está disponible"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("Cobro de deuda"),
        "dueList": MessageLookupByLibrary.simpleMessage("Lista de pendientes"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transacción vencida"),
        "edit": MessageLookupByLibrary.simpleMessage("Editar"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("Editar/Agregar serie:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Editar su perfil"),
        "email": MessageLookupByLibrary.simpleMessage("Correo electrónico"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("Ingrese la cantidad"),
        "enterBrandName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de la marca"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de la categoría"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Ingrese la descripción de la empresa"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Ingrese la dirección de correo electrónico de la empresa"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Ingrese el número de teléfono de la empresa"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Ingrese la URL del sitio web de la empresa"),
        "enterCustomerName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre del cliente"),
        "enterDealePrice": MessageLookupByLibrary.simpleMessage(
            "Ingrese el precio del distribuidor"),
        "enterDiscountPrice": MessageLookupByLibrary.simpleMessage(
            "Ingrese el precio de descuento"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Ingrese la categoría de gasto"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Ingrese la fecha del gasto"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Ingrese la categoría de ingresos"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Ingrese la fecha de ingreso"),
        "enterManufacturerName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre del fabricante"),
        "enterName": MessageLookupByLibrary.simpleMessage("Ingrese el nombre"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Ingrese el nombre"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Ingrese la nota"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Ingrese el saldo de apertura"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Ingrese el importe pagado"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Introducir contraseña"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Ingrese la cantidad a pagar"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Ingrese el precio"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Ingrese la capacidad del producto"),
        "enterProductCode": MessageLookupByLibrary.simpleMessage(
            "Ingrese el código de producto"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage(
            "Ingrese el color del producto"),
        "enterProductName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre del producto"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Ingrese la cantidad de producto"),
        "enterProductSize": MessageLookupByLibrary.simpleMessage(
            "Ingrese el tamaño del producto"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Ingrese el tipo de producto"),
        "enterProductUnit": MessageLookupByLibrary.simpleMessage(
            "Ingrese la unidad del producto"),
        "enterProductWeight": MessageLookupByLibrary.simpleMessage(
            "Ingrese el peso del producto"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Ingrese el precio de compra"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Ingrese el número de referencia"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Ingrese el precio de venta"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Ingrese el número de serie"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Ingrese el contenido del mensaje"),
        "enterStockAmount": MessageLookupByLibrary.simpleMessage(
            "Ingrese la cantidad de stock"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Introducir ID de transacción"),
        "enterUnitName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de la unidad"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Introducir nombre del rol de usuario"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Introducir título de usuario"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Ingrese la garantía"),
        "enterWholeSalePrice": MessageLookupByLibrary.simpleMessage(
            "Ingrese el precio al por mayor"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Ingrese su cantidad"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Ingrese su dirección"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Ingrese su dirección de la empresa"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de su empresa"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de su empresa"),
        "enterYourEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Ingrese su dirección de correo electrónico"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Ingrese su contraseña"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Ingrese su contraseña nuevamente"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Ingrese su número de teléfono"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de su tienda"),
        "entercategoryName": MessageLookupByLibrary.simpleMessage(
            "Ingrese el nombre de la categoría"),
        "expense": MessageLookupByLibrary.simpleMessage("Gasto"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Fecha del gasto"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Detalles del gasto"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Gasto para"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista de categorías de gastos"),
        "expenses": MessageLookupByLibrary.simpleMessage("Gastos"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Cinco productos más comprados del mes"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("Para usos ilimitados"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("¿Olvidaste tu contraseña?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Copia de seguridad de datos gratuita"),
        "freeLifeTimeUpdate": MessageLookupByLibrary.simpleMessage(
            "Actualización gratuita de por vida"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Paquete gratuito"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Plan gratuito"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Empezar"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("Identificación del gobierno"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Total general"),
        "hold": MessageLookupByLibrary.simpleMessage("Retener"),
        "holdNumber":
            MessageLookupByLibrary.simpleMessage("Número de retención"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Verificación de identidad"),
        "inc": MessageLookupByLibrary.simpleMessage("Ingresos"),
        "income": MessageLookupByLibrary.simpleMessage("Ingresos"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Categoría de ingresos"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage(
            "Lista de categorías de ingresos"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Fecha de ingreso"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Detalles de los ingresos"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Ingresos por"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Lista de ingresos"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Aumentar existencias"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Privacidad instantánea"),
        "invoice": MessageLookupByLibrary.simpleMessage("Factura"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Factura:"),
        "invoiceHint":
            MessageLookupByLibrary.simpleMessage("Número de factura..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Número de factura"),
        "item": MessageLookupByLibrary.simpleMessage("Artículo"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nombre del artículo"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Verificación KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Detalles del libro mayor"),
        "ledger": MessageLookupByLibrary.simpleMessage("Libro mayor"),
        "left": MessageLookupByLibrary.simpleMessage("Izquierda"),
        "loanAccounts":
            MessageLookupByLibrary.simpleMessage("Cuentas de préstamo"),
        "logOut": MessageLookupByLibrary.simpleMessage("Cerrar sesión"),
        "login": MessageLookupByLibrary.simpleMessage("Iniciar sesión"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Posición del logotipo en la factura"),
        "loss": MessageLookupByLibrary.simpleMessage("Pérdida"),
        "lossOrProfit":
            MessageLookupByLibrary.simpleMessage("Pérdida/Ganancia"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Pérdida (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Inventario Bajo"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Stock bajo"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Causa una impresión duradera en tus clientes con facturas personalizadas. Nuestra Actualización Ilimitada ofrece la ventaja única de personalizar tus facturas, agregando un toque profesional que refuerza la identidad de tu marca y fomenta la fidelidad del cliente."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Fabricante"),
        "mobiPosLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Panel de inicio de sesión de Pos Saas"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Panel de registro de Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Aplicación móvil\n+\nEscritorio"),
        "moneyReciept":
            MessageLookupByLibrary.simpleMessage("Recibo de dinero"),
        "nam": MessageLookupByLibrary.simpleMessage("Nombre*"),
        "name": MessageLookupByLibrary.simpleMessage("Nombre"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nombre, código o categoría"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("Nuevos clientes"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("Nuevos clientes"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nuevos ingresos"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noConnection": MessageLookupByLibrary.simpleMessage("Sin conexión"),
        "noCustomerFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún cliente"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna transacción pendiente"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna categoría de gasto"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna categoría de ingresos"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("No se encontraron ingresos"),
        "noInvoiceFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna factura"),
        "noProductFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún producto"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna transacción de compra"),
        "noQuotionFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna cotización"),
        "noReportFound": MessageLookupByLibrary.simpleMessage(
            "NO se encontró ningún informe"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna transacción de venta"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún número de serie"),
        "noSupplierFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún proveedor"),
        "noTransactionFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ninguna transacción"),
        "noUserFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún usuario"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún rol de usuario"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "No se encontró ningún número de serie"),
        "note": MessageLookupByLibrary.simpleMessage("Nota"),
        "ok": MessageLookupByLibrary.simpleMessage("Aceptar"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Cheques abiertos"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo de apertura"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "o arrastre y suelte PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Pedidos"),
        "other": MessageLookupByLibrary.simpleMessage("Otro"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Otros ingresos"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Características del paquete"),
        "paid": MessageLookupByLibrary.simpleMessage("Pagado"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Cantidad pagada"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nombre de la parte"),
        "partyType": MessageLookupByLibrary.simpleMessage("Tipo de parte"),
        "password": MessageLookupByLibrary.simpleMessage("Contraseña"),
        "payCash": MessageLookupByLibrary.simpleMessage("Pagar en efectivo"),
        "payable": MessageLookupByLibrary.simpleMessage("A pagar"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Importe pagado"),
        "payment": MessageLookupByLibrary.simpleMessage("Pago"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Pago entrante"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Pago saliente"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Tipo de pago"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("Tipo de pago"),
        "phone": MessageLookupByLibrary.simpleMessage("Teléfono"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Número de teléfono"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Verificación de teléfono"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Agregue una venta"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Añadir cliente"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Por favor, verifica tu conectividad a Internet"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Descargue nuestra aplicación móvil y suscríbase a un paquete para usar la versión de escritorio"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Por favor, introduce las existencias de productos"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("Ingrese datos válidos"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("Seleccione un cliente"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("Ingrese datos válidos"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Panel de registro de Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Practicar"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Soporte al cliente premium"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Plan premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Vista previa"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Deuda anterior:"),
        "price": MessageLookupByLibrary.simpleMessage("Precio"),
        "print": MessageLookupByLibrary.simpleMessage("Imprimir"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Imprimir factura"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Imprimir PDF"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Política de privacidad"),
        "product": MessageLookupByLibrary.simpleMessage("Producto"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Categoría de producto"),
        "productCod":
            MessageLookupByLibrary.simpleMessage("Código de producto*"),
        "productColor":
            MessageLookupByLibrary.simpleMessage("Color del producto"),
        "productList":
            MessageLookupByLibrary.simpleMessage("Lista de productos"),
        "productNam":
            MessageLookupByLibrary.simpleMessage("Nombre del producto*"),
        "productName":
            MessageLookupByLibrary.simpleMessage("Nombre del producto"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Tamaño del producto"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Existencias de productos"),
        "productType": MessageLookupByLibrary.simpleMessage("Tipo de producto"),
        "productUnit":
            MessageLookupByLibrary.simpleMessage("Unidad de producto"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garantía del producto"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Peso del producto"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Capacidad del producto"),
        "prof": MessageLookupByLibrary.simpleMessage("Perfil"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Editar perfil"),
        "profit": MessageLookupByLibrary.simpleMessage("Ganancia"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Pérdida(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Ganancia(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Compra"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Lista de compras"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Comprar el plan premium"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Precio de compra"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transacción de compra"),
        "quantity": MessageLookupByLibrary.simpleMessage("Cantidad"),
        "quotation": MessageLookupByLibrary.simpleMessage("Cotización"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Lista de cotizaciones"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Ventas Recientes"),
        "recivedAmount":
            MessageLookupByLibrary.simpleMessage("Cantidad recibida"),
        "referenceNo":
            MessageLookupByLibrary.simpleMessage("Número de referencia"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Número de referencia"),
        "registration": MessageLookupByLibrary.simpleMessage("Registro"),
        "remaining": MessageLookupByLibrary.simpleMessage("Restante: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Saldo restante"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Deuda restante"),
        "reports": MessageLookupByLibrary.simpleMessage("Informes"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Restablecer su contraseña"),
        "retailer": MessageLookupByLibrary.simpleMessage("Minorista"),
        "revenue": MessageLookupByLibrary.simpleMessage("Ingresos"),
        "right": MessageLookupByLibrary.simpleMessage("Derecha"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Monto de Ventas"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Protege tus datos comerciales sin esfuerzo. Nuestra Actualización Ilimitada de Pos Saas POS incluye copias de seguridad gratuitas de datos, asegurando que tu información valiosa esté protegida contra cualquier evento imprevisto. Concéntrate en lo que realmente importa: el crecimiento de tu negocio."),
        "sale": MessageLookupByLibrary.simpleMessage("Venta"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("Monto de la venta"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Detalles de la venta"),
        "saleList": MessageLookupByLibrary.simpleMessage("Lista de ventas"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Precio de venta"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Precio de venta*"),
        "saleReturn":
            MessageLookupByLibrary.simpleMessage("Devolucion de venta"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transacción de venta"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transacciones de venta (historial de cotización de venta)"),
        "sales": MessageLookupByLibrary.simpleMessage("Ventas"),
        "salesList": MessageLookupByLibrary.simpleMessage("Lista de ventas"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Guardar y publicar"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Guardar y publicar"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Guardar cambios"),
        "search": MessageLookupByLibrary.simpleMessage("Buscar ......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("Buscar cualquier cosa..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Buscar por factura..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("Buscar por factura o nombre"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Buscar por nombre"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Buscar por nombre o teléfono..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("Buscar número de serie"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Seleccionar partes"),
        "selectProductBrand": MessageLookupByLibrary.simpleMessage(
            "Seleccionar marca de producto"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("Seleccionar número de serie"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("Seleccionar variaciones:"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage(
            "Seleccionar tiempo de garantía"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Selecciona tu idioma"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Enviar mensaje"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Número de serie"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("Número de serie"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Cargo por servicio"),
        "setting": MessageLookupByLibrary.simpleMessage("Configuración"),
        "share": MessageLookupByLibrary.simpleMessage("Compartir"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("Envío/Otro"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nombre de la tienda"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Balance de apertura de la tienda"),
        "show": MessageLookupByLibrary.simpleMessage("Mostrar >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "¿Mostrar logotipo en la factura?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Envío/Servicios"),
        "size": MessageLookupByLibrary.simpleMessage("Tamaño"),
        "statistic": MessageLookupByLibrary.simpleMessage("Estadística"),
        "status": MessageLookupByLibrary.simpleMessage("Estado"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Mantente a la vanguardia de los avances tecnológicos sin costos adicionales. Nuestra Actualización Ilimitada de Pos Saas POS garantiza que siempre tengas las últimas herramientas y funciones al alcance de tu mano, asegurando que tu negocio permanezca a la vanguardia."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Mantente a la vanguardia de los avances tecnológicos sin costos adicionales. Nuestra Actualización Ilimitada de Pos Sass POS garantiza que siempre tengas las últimas herramientas y funciones al alcance de tu mano, asegurando que tu negocio permanezca a la vanguardia."),
        "stock": MessageLookupByLibrary.simpleMessage("Stock"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventario de stock"),
        "stockReport": MessageLookupByLibrary.simpleMessage("Informe de stock"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Valor del stock"),
        "stockValues":
            MessageLookupByLibrary.simpleMessage("Valor del Inventario"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
        "subciption": MessageLookupByLibrary.simpleMessage("Suscripción"),
        "submit": MessageLookupByLibrary.simpleMessage("Enviar"),
        "supplier": MessageLookupByLibrary.simpleMessage("Proveedores"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("Proveedor adeudado"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Factura de proveedor"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Lista de proveedores"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Código SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Ventas Totales"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Tome una foto de su licencia de conducir, documento de identidad nacional o pasaporte"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Términos de uso"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "El nombre lo dice todo. Con Pos Saas POS Unlimited, no hay límite en tu uso. Ya sea que estés procesando un puñado de transacciones o experimentando un flujo de clientes, puedes operar con confianza, sabiendo que no estás limitado por restricciones."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("Este cliente no tiene deuda"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "Este cliente tiene un saldo anterior"),
        "to": MessageLookupByLibrary.simpleMessage("A"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Producto más vendido"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount":
            MessageLookupByLibrary.simpleMessage("Total de la cantidad"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Descuento total"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total adeudado"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Total de deudas"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Gasto total"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Ingresos totales"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Total de artículos: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Pérdida total"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Total pagado"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total a pagar"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Pago total saliente"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Precio total"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Productos totales"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Ganancia total"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("Compra total"),
        "totalReturnAmount": MessageLookupByLibrary.simpleMessage(
            "Importe total de la devolución"),
        "totalReturns":
            MessageLookupByLibrary.simpleMessage("Devoluciones totales"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Venta total"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Ventas totales"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total de IVA"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Pago total entrante"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transacción"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("ID de transacción"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Informe de transacción"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Intentar de nuevo"),
        "type": MessageLookupByLibrary.simpleMessage("Tipo"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Sin pagar"),
        "unit": MessageLookupByLibrary.simpleMessage("Unidad"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nombre de la unidad"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Precio unitario"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Ilimitado"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Facturas ilimitadas"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage("Uso ilimitado"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Desbloquea el potencial completo de Pos Saas POS con sesiones de entrenamiento personalizadas lideradas por nuestro equipo de expertos. Desde lo básico hasta técnicas avanzadas, nos aseguramos de que estés bien versado en la utilización de cada aspecto del sistema para optimizar tus procesos comerciales."),
        "updateNow": MessageLookupByLibrary.simpleMessage("Actualizar ahora"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Actualice su plan primero\\nEl límite de ventas ha excedido."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Actualizar en la aplicación móvil"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Subir una imagen"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "Subir un logotipo de factura"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Subir documento"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Subir archivo"),
        "userName": MessageLookupByLibrary.simpleMessage("Nombre de usuario"),
        "userRole": MessageLookupByLibrary.simpleMessage("Rol de usuario"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("Nombre del rol de usuario"),
        "userTitle": MessageLookupByLibrary.simpleMessage("Título de usuario"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("IVA/GST"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Verificar número de teléfono"),
        "view": MessageLookupByLibrary.simpleMessage("Ver"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Cliente que entra"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantía"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garantías"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Necesitamos registrar su teléfono antes de comenzar!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Entendemos la importancia de operaciones sin problemas. Por eso, nuestro soporte disponible las 24 horas del día está listo para ayudarte, ya sea una consulta rápida o una preocupación más amplia. Conéctate con nosotros en cualquier momento y en cualquier lugar a través de llamadas o WhatsApp para experimentar un servicio al cliente inigualable."),
        "wholeSaleprice":
            MessageLookupByLibrary.simpleMessage("Precio al por mayor"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Mayorista"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Venta al por mayor"),
        "wight": MessageLookupByLibrary.simpleMessage("Peso"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Sí, devolver"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Debes VOLVER A INICIAR SESIÓN en tu cuenta."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Necesita verificar su identidad antes de comprar mensajes"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Su lista de todas las ventas"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Todas sus ventas"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Estás usando"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("Sus ventas pendientes"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Necesita verificar su identidad antes de comprar mensajes"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Tu paquete"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Su pago ha sido cancelado"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Su pago se ha realizado correctamente"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Su pago ha sido cancelado")
      };
}
