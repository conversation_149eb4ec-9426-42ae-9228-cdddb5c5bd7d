// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("AJOUTER UNE VENTE"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("CATÉGORIE"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("FACTURE"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale":
            MessageLookupByLibrary.simpleMessage("Vente au point de vente"),
        "PRICE": MessageLookupByLibrary.simpleMessage("PRIX"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("NOM DU PRODUIT"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Panneau de connexion Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("QTY"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Quantité *"),
        "STATUS": MessageLookupByLibrary.simpleMessage("STATUT"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("VALEUR TOTALE"),
        "UserTitle":
            MessageLookupByLibrary.simpleMessage("Titre de l\'utilisateur"),
        "aboutApp":
            MessageLookupByLibrary.simpleMessage("À propos de l\'application"),
        "accountName": MessageLookupByLibrary.simpleMessage("Nom du compte"),
        "accountNumber":
            MessageLookupByLibrary.simpleMessage("Numéro de compte"),
        "action": MessageLookupByLibrary.simpleMessage("Action"),
        "add": MessageLookupByLibrary.simpleMessage("Ajouter"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Ajouter une marque"),
        "addCategory":
            MessageLookupByLibrary.simpleMessage("Ajouter une catégorie"),
        "addCustomer":
            MessageLookupByLibrary.simpleMessage("Ajouter un client"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("Ajouter une description..."),
        "addDucument":
            MessageLookupByLibrary.simpleMessage("Ajouter des documents"),
        "addItem": MessageLookupByLibrary.simpleMessage("Ajouter un article"),
        "addItemCategory": MessageLookupByLibrary.simpleMessage(
            "Ajouter une catégorie d\'article"),
        "addNew": MessageLookupByLibrary.simpleMessage("Ajouter un nouveau"),
        "addNewUser": MessageLookupByLibrary.simpleMessage(
            "Ajouter un nouvel utilisateur"),
        "addProduct":
            MessageLookupByLibrary.simpleMessage("Ajouter un produit"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Ajouté avec succès"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("Ajouter un fournisseur"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Ajouter une unité"),
        "addUpdateExpenseList": MessageLookupByLibrary.simpleMessage(
            "Ajouter/Mettre à jour la liste des dépenses"),
        "addUpdateIncomeList": MessageLookupByLibrary.simpleMessage(
            "Ajouter/Mettre à jour la liste des revenus"),
        "addUserRole": MessageLookupByLibrary.simpleMessage(
            "Ajouter un rôle d\'utilisateur"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("Ajouter un numéro de série?"),
        "address": MessageLookupByLibrary.simpleMessage("Adresse"),
        "all": MessageLookupByLibrary.simpleMessage("Tout"),
        "allBasicFeatures": MessageLookupByLibrary.simpleMessage(
            "Toutes les fonctionnalités de base"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Vous avez déjà un compte?"),
        "amount": MessageLookupByLibrary.simpleMessage("Montant"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "Assistance pour les applications Android et iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous créer cette quotation?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous supprimer ce client?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous supprimer ce produit"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous supprimer ce devis?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous retourner cette vente?"),
        "balance": MessageLookupByLibrary.simpleMessage("Balance"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Devise du compte bancaire"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("Comptes bancaires"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Informations bancaires"),
        "bankName": MessageLookupByLibrary.simpleMessage("Nom de la banque"),
        "between": MessageLookupByLibrary.simpleMessage("Entre"),
        "billTo": MessageLookupByLibrary.simpleMessage("Facture à:"),
        "branchName":
            MessageLookupByLibrary.simpleMessage("Nom de la succursale"),
        "brand": MessageLookupByLibrary.simpleMessage("Marque"),
        "brandName": MessageLookupByLibrary.simpleMessage("Nom de la marque"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Catégorie d\'entreprise"),
        "buy": MessageLookupByLibrary.simpleMessage("Acheter"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Acheter un forfait Premium"),
        "buySms": MessageLookupByLibrary.simpleMessage("Acheter des SMS"),
        "calculator": MessageLookupByLibrary.simpleMessage("Calculatrice:"),
        "camera": MessageLookupByLibrary.simpleMessage("Caméra"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annuler"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacité"),
        "cashAndBank":
            MessageLookupByLibrary.simpleMessage("Liquide et banque"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("Liquide en main"),
        "categories": MessageLookupByLibrary.simpleMessage("Catégories"),
        "category": MessageLookupByLibrary.simpleMessage("Catégorie"),
        "categoryName":
            MessageLookupByLibrary.simpleMessage("Nom de la catégorie"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("Montant changé"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("Montant modifiable"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("Vérifier la garantie"),
        "choseAplan":
            MessageLookupByLibrary.simpleMessage("Choisissez un plan"),
        "collectDue": MessageLookupByLibrary.simpleMessage("Collecter le dû >"),
        "color": MessageLookupByLibrary.simpleMessage("Couleur"),
        "comapnyName":
            MessageLookupByLibrary.simpleMessage("Nom de l\'entreprise"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Adresse de l\'entreprise"),
        "companyDescription": MessageLookupByLibrary.simpleMessage(
            "Description de l\'entreprise"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Adresse e-mail de l\'entreprise"),
        "companyName":
            MessageLookupByLibrary.simpleMessage("Nom de l\'entreprise"),
        "companyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Numéro de téléphone de l\'entreprise"),
        "companyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "URL du site Web de l\'entreprise"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmer le mot de passe"),
        "continu": MessageLookupByLibrary.simpleMessage("Continuer"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("Convertir en vente"),
        "create": MessageLookupByLibrary.simpleMessage("Créer"),
        "createPayment":
            MessageLookupByLibrary.simpleMessage("Créer un paiement"),
        "createdBy": MessageLookupByLibrary.simpleMessage("Créé par"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("Hub créatif"),
        "currency": MessageLookupByLibrary.simpleMessage("Devise"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("Plan actuel"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "Personnalisation de la facture"),
        "customer": MessageLookupByLibrary.simpleMessage("Client"),
        "customerDue": MessageLookupByLibrary.simpleMessage("Client dû"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("Factures clients"),
        "customerList":
            MessageLookupByLibrary.simpleMessage("Liste des clients"),
        "customerName": MessageLookupByLibrary.simpleMessage("Nom du client"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("Client du mois"),
        "customerType": MessageLookupByLibrary.simpleMessage("Type de client"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("Client: Client de passage"),
        "customers": MessageLookupByLibrary.simpleMessage("Clients"),
        "dailyCollection":
            MessageLookupByLibrary.simpleMessage("Collecte quotidienne"),
        "dailySales":
            MessageLookupByLibrary.simpleMessage("Ventes quotidiennes"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("Transaction quotidienne"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Tableau de bord"),
        "date": MessageLookupByLibrary.simpleMessage("Date"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Date et heure"),
        "dealer": MessageLookupByLibrary.simpleMessage("Concessionnaire"),
        "dealerPrice":
            MessageLookupByLibrary.simpleMessage("Prix du concessionnaire"),
        "delete": MessageLookupByLibrary.simpleMessage("Supprimer"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Frais de livraison"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "details": MessageLookupByLibrary.simpleMessage("Détails >"),
        "discount": MessageLookupByLibrary.simpleMessage("Réduction"),
        "discountPrice":
            MessageLookupByLibrary.simpleMessage("Prix ​​de réduction"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("Télécharger PDF"),
        "due": MessageLookupByLibrary.simpleMessage("Dû"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Montant dû"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "Le montant dû s\'affichera ici si disponible"),
        "dueCollection":
            MessageLookupByLibrary.simpleMessage("Collecte des dues"),
        "dueList": MessageLookupByLibrary.simpleMessage("Liste des dues"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("Transaction due"),
        "edit": MessageLookupByLibrary.simpleMessage("Modifier"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "Modifier/Ajouter un numéro de série:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("Modifier votre profil"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enterAmount":
            MessageLookupByLibrary.simpleMessage("Entrez le montant"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Entrez le nom de la marque"),
        "enterCategoryName": MessageLookupByLibrary.simpleMessage(
            "Entrez le nom de la catégorie"),
        "enterCompanyDesciption": MessageLookupByLibrary.simpleMessage(
            "Entrez la description de l\'entreprise"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "Entrez l\'adresse e-mail de l\'entreprise"),
        "enterCompanyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Entrez le numéro de téléphone de l\'entreprise"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "Entrez l\'URL du site Web de l\'entreprise"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("Entrez le nom du client"),
        "enterDealePrice": MessageLookupByLibrary.simpleMessage(
            "Entrez le prix du concessionnaire"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("Entrez le prix de réduction"),
        "enterExpanseCategory": MessageLookupByLibrary.simpleMessage(
            "Entrez la catégorie de dépense"),
        "enterExpenseDate": MessageLookupByLibrary.simpleMessage(
            "Entrez la date de la dépense"),
        "enterIncomeCategory": MessageLookupByLibrary.simpleMessage(
            "Entrez la catégorie de revenu"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("Entrez la date de revenu"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("Entrez le nom du fabricant"),
        "enterName": MessageLookupByLibrary.simpleMessage("Entrez le nom"),
        "enterNames": MessageLookupByLibrary.simpleMessage("Entrez le nom"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Entrez la note"),
        "enterOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Entrez le solde d\'ouverture"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("Entrez le montant payé"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Entrer le mot de passe"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("Entrez le montant à payer"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Entrez le prix"),
        "enterProductCapacity": MessageLookupByLibrary.simpleMessage(
            "Entrez la capacité du produit"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("Entrez le code produit"),
        "enterProductColor": MessageLookupByLibrary.simpleMessage(
            "Entrez la couleur du produit"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Entrez le nom du produit"),
        "enterProductQuantity": MessageLookupByLibrary.simpleMessage(
            "Entrez la quantité de produit"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("Entrez la taille du produit"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("Entrez le type de produit"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("Entrez l\'unité du produit"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("Entrez le poids du produit"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Entrez le prix d\'achat"),
        "enterReferenceNumber": MessageLookupByLibrary.simpleMessage(
            "Entrez le numéro de référence"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("Entrez le prix de vente"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("Entrez le numéro de série"),
        "enterSmsContent": MessageLookupByLibrary.simpleMessage(
            "Entrez le contenu du message"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("Entrez le montant du stock"),
        "enterTransactionId": MessageLookupByLibrary.simpleMessage(
            "Entrer l\'identifiant de la transaction"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("Entrez le nom de l\'unité"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "Entrer le nom du rôle de l\'utilisateur"),
        "enterUserTitle": MessageLookupByLibrary.simpleMessage(
            "Entrer le titre de l\'utilisateur"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("Entrez la garantie"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Entrez le prix de gros"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("Entrez votre montant"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("Entrez votre adresse"),
        "enterYourCompanyAddress": MessageLookupByLibrary.simpleMessage(
            "Entrez votre adresse d\'entreprise"),
        "enterYourCompanyName": MessageLookupByLibrary.simpleMessage(
            "Entrez votre nom d\'entreprise"),
        "enterYourCompanyNames": MessageLookupByLibrary.simpleMessage(
            "Entrez votre nom d\'entreprise"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Entrez votre adresse email"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Entrez votre mot de passe"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "Entrez votre mot de passe à nouveau"),
        "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Entrez votre numéro de téléphone"),
        "enterYourShopName": MessageLookupByLibrary.simpleMessage(
            "Entrez le nom de votre magasin"),
        "entercategoryName": MessageLookupByLibrary.simpleMessage(
            "Entrez le nom de la catégorie"),
        "expense": MessageLookupByLibrary.simpleMessage("Dépense"),
        "expenseDate":
            MessageLookupByLibrary.simpleMessage("Date de la dépense"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("Détails de la dépense"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Dépense pour"),
        "expensecategoryList": MessageLookupByLibrary.simpleMessage(
            "Liste des catégories de dépenses"),
        "expenses": MessageLookupByLibrary.simpleMessage("Dépenses"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "Top cinq produits achetés du mois"),
        "forUnlimitedUses": MessageLookupByLibrary.simpleMessage(
            "Pour une utilisation illimitée"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Mot de passe oublié?"),
        "freeDataBackup": MessageLookupByLibrary.simpleMessage(
            "Sauvegarde des données gratuite"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Mise à jour à vie gratuite"),
        "freePackage": MessageLookupByLibrary.simpleMessage("Forfait gratuit"),
        "freePlan": MessageLookupByLibrary.simpleMessage("Forfait gratuit"),
        "getStarted": MessageLookupByLibrary.simpleMessage("Commencer"),
        "govermentId":
            MessageLookupByLibrary.simpleMessage("ID gouvernementale"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("Total général"),
        "hold": MessageLookupByLibrary.simpleMessage("Retenir"),
        "holdNumber":
            MessageLookupByLibrary.simpleMessage("Numéro de réservation"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Vérification d\'identité"),
        "inc": MessageLookupByLibrary.simpleMessage("Revenu"),
        "income": MessageLookupByLibrary.simpleMessage("Revenue"),
        "incomeCategory":
            MessageLookupByLibrary.simpleMessage("Catégorie de revenu"),
        "incomeCategoryList": MessageLookupByLibrary.simpleMessage(
            "Liste des catégories de revenu"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("Date de revenu"),
        "incomeDetails":
            MessageLookupByLibrary.simpleMessage("Détails du revenu"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("Revenu pour"),
        "incomeList": MessageLookupByLibrary.simpleMessage("Liste des revenus"),
        "increaseStock":
            MessageLookupByLibrary.simpleMessage("Augmenter le stock"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("Confidentialité instantanée"),
        "invoice": MessageLookupByLibrary.simpleMessage("Facture"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("Facture:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("N° de facture..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("Numéro de facture"),
        "item": MessageLookupByLibrary.simpleMessage("Article"),
        "itemName": MessageLookupByLibrary.simpleMessage("Nom de l\'article"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("Vérification KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("Détails du livre-journal"),
        "ledger": MessageLookupByLibrary.simpleMessage("Grand livre"),
        "left": MessageLookupByLibrary.simpleMessage("Gauche"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("Comptes de prêt"),
        "logOut": MessageLookupByLibrary.simpleMessage("Se déconnecter"),
        "login": MessageLookupByLibrary.simpleMessage("Connexion"),
        "logoPositionInInvoice": MessageLookupByLibrary.simpleMessage(
            "Position du logo sur la facture?"),
        "loss": MessageLookupByLibrary.simpleMessage("Perte"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Perte/Profit"),
        "lossminus": MessageLookupByLibrary.simpleMessage("Perte (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("Stock Faible"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("Stocks bas"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Laissez une impression durable sur vos clients avec des factures personnalisées. Notre mise à niveau illimitée offre l\'avantage unique de personnaliser vos factures, ajoutant une touche professionnelle qui renforce l\'identité de votre marque et favorise la fidélité des clients."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("Fabricant"),
        "mobiPosLoginPanel": MessageLookupByLibrary.simpleMessage(
            "Panneau de connexion Pos Saas"),
        "mobiPosSignUpPane": MessageLookupByLibrary.simpleMessage(
            "Panneau d\'inscription Pos Saas"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "Application mobile\n+\nBureau"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("Reçu d\'argent"),
        "nam": MessageLookupByLibrary.simpleMessage("Nom *"),
        "name": MessageLookupByLibrary.simpleMessage("Nom"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("Nom, code ou catégorie"),
        "newCusotmers":
            MessageLookupByLibrary.simpleMessage("Nouveaux clients"),
        "newCustomers":
            MessageLookupByLibrary.simpleMessage("Nouveaux clients"),
        "newIncome": MessageLookupByLibrary.simpleMessage("Nouveau revenu"),
        "no": MessageLookupByLibrary.simpleMessage("Non"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("Pas de connexion"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("Aucun client trouvé"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "Aucune transaction due trouvée"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Aucune catégorie de dépense trouvée"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "Aucune catégorie de revenu trouvée"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("Aucun revenu trouvé"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("Aucune facture trouvée"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("Aucun produit trouvé"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "Aucune transaction d\'achat trouvée"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("Aucun devis trouvé"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("AUCUN RAPPORT TROUVE"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "Aucune transaction de vente trouvée"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Aucun numéro de série trouvé"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("Aucun fournisseur trouvé"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("Aucune transaction trouvée"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("Aucun utilisateur trouvé"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "Aucun rôle d\'utilisateur trouvé"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "Aucun numéro de série trouvé"),
        "note": MessageLookupByLibrary.simpleMessage("Note"),
        "ok": MessageLookupByLibrary.simpleMessage("D\'accord"),
        "openCheques": MessageLookupByLibrary.simpleMessage("Chèques ouverts"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Solde d\'ouverture"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "ou faites glisser et déposez PNG, JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("Commandes"),
        "other": MessageLookupByLibrary.simpleMessage("Autre"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("Autres revenus"),
        "packageFeature":
            MessageLookupByLibrary.simpleMessage("Caractéristiques du forfait"),
        "paid": MessageLookupByLibrary.simpleMessage("Payé"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Montant payé"),
        "partyName": MessageLookupByLibrary.simpleMessage("Nom de la partie"),
        "partyType": MessageLookupByLibrary.simpleMessage("Type de partie"),
        "password": MessageLookupByLibrary.simpleMessage("Mot de passe"),
        "payCash": MessageLookupByLibrary.simpleMessage("Payer en espèces"),
        "payable": MessageLookupByLibrary.simpleMessage("Payable"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("Montant à payer"),
        "payment": MessageLookupByLibrary.simpleMessage("Paiement"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("Paiement entrant"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("Paiement sortant"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Type de paiement"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("Type de paiement"),
        "phone": MessageLookupByLibrary.simpleMessage("Téléphone"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Numéro de téléphone"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Vérification téléphonique"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("Veuillez ajouter une vente"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("Veuillez ajouter un client"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Veuillez vérifier votre connectivité Internet"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "Veuillez télécharger notre application mobile et vous abonner à un forfait pour utiliser la version de bureau"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "Veuillez entrer le stock de produit"),
        "pleaseEnterValidData": MessageLookupByLibrary.simpleMessage(
            "Veuillez entrer des données valides"),
        "pleaseSelectACustomer": MessageLookupByLibrary.simpleMessage(
            "Veuillez sélectionner un client"),
        "pleaseentervaliddata": MessageLookupByLibrary.simpleMessage(
            "Veuillez entrer des données valides"),
        "posSaasSingUpPanel": MessageLookupByLibrary.simpleMessage(
            "Panneau d\'inscription Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("Pratique"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Assistance client premium"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Forfait Premium"),
        "preview": MessageLookupByLibrary.simpleMessage("Aperçu"),
        "previousDue": MessageLookupByLibrary.simpleMessage("Due précédent:"),
        "price": MessageLookupByLibrary.simpleMessage("Prix"),
        "print": MessageLookupByLibrary.simpleMessage("Imprimer"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("Imprimer la facture"),
        "printPdf": MessageLookupByLibrary.simpleMessage("Imprimer PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage(
            "Politique de confidentialité"),
        "product": MessageLookupByLibrary.simpleMessage("Produit"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("Catégorie de produit"),
        "productCod": MessageLookupByLibrary.simpleMessage("Code produit *"),
        "productColor":
            MessageLookupByLibrary.simpleMessage("Couleur du produit"),
        "productList":
            MessageLookupByLibrary.simpleMessage("Liste des produits"),
        "productNam": MessageLookupByLibrary.simpleMessage("Nom du produit *"),
        "productName": MessageLookupByLibrary.simpleMessage("Nom du produit"),
        "productSize":
            MessageLookupByLibrary.simpleMessage("Taille du produit"),
        "productStock":
            MessageLookupByLibrary.simpleMessage("Stock de produit"),
        "productType": MessageLookupByLibrary.simpleMessage("Type de produit"),
        "productUnit": MessageLookupByLibrary.simpleMessage("Unité de produit"),
        "productWaranty":
            MessageLookupByLibrary.simpleMessage("Garantie du produit"),
        "productWeight":
            MessageLookupByLibrary.simpleMessage("Poids du produit"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("Capacité du produit"),
        "prof": MessageLookupByLibrary.simpleMessage("Profil"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("Modifier le profil"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("Profit(-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("Profit(+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("Achat"),
        "purchaseList":
            MessageLookupByLibrary.simpleMessage("Liste des achats"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Acheter un forfait Premium"),
        "purchasePrice":
            MessageLookupByLibrary.simpleMessage("Prix ​​d\'achat"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("Transaction d\'achat"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantité"),
        "quotation": MessageLookupByLibrary.simpleMessage("Devis"),
        "quotationList":
            MessageLookupByLibrary.simpleMessage("Liste des devis"),
        "recentSale": MessageLookupByLibrary.simpleMessage("Ventes Récentes"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("Montant reçu"),
        "referenceNo":
            MessageLookupByLibrary.simpleMessage("Numéro de référence"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Numéro de référence"),
        "registration": MessageLookupByLibrary.simpleMessage("Enregistrement"),
        "remaining": MessageLookupByLibrary.simpleMessage("Restant :"),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("Solde restant"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Reste dû"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapports"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage(
            "Réinitialiser votre mot de passe"),
        "retailer": MessageLookupByLibrary.simpleMessage("Détaillant"),
        "revenue": MessageLookupByLibrary.simpleMessage("Revenu"),
        "right": MessageLookupByLibrary.simpleMessage("Droite"),
        "sAmount": MessageLookupByLibrary.simpleMessage("Montant des Ventes"),
        "sale": MessageLookupByLibrary.simpleMessage("Vente"),
        "saleAmount":
            MessageLookupByLibrary.simpleMessage("Montant de la vente"),
        "saleDetails":
            MessageLookupByLibrary.simpleMessage("Détails de la vente"),
        "saleList": MessageLookupByLibrary.simpleMessage("Liste des ventes"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Prix ​​de vente"),
        "salePrices": MessageLookupByLibrary.simpleMessage("Prix de vente *"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("Retour de vente"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("Transaction de vente"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "Transactions de vente (Historique des ventes de devis)"),
        "sales": MessageLookupByLibrary.simpleMessage("Ventes"),
        "salesList": MessageLookupByLibrary.simpleMessage("Liste des ventes"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Enregistrer et publier"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("Sauvegarder et publier"),
        "saveChanges": MessageLookupByLibrary.simpleMessage(
            "Sauvegarder les modifications"),
        "search": MessageLookupByLibrary.simpleMessage("Rechercher..."),
        "searchAnyThing": MessageLookupByLibrary.simpleMessage(
            "Rechercher n\'importe quoi..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("Rechercher par facture..."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "Rechercher par facture ou nom"),
        "searchByName":
            MessageLookupByLibrary.simpleMessage("Rechercher par nom"),
        "searchByNameOrPhone": MessageLookupByLibrary.simpleMessage(
            "Rechercher par nom ou téléphone..."),
        "searchSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Rechercher le numéro de série"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("Sélectionner les parties"),
        "selectProductBrand": MessageLookupByLibrary.simpleMessage(
            "Sélectionner la marque du produit"),
        "selectSerialNumber": MessageLookupByLibrary.simpleMessage(
            "Sélectionner le numéro de série"),
        "selectVariations": MessageLookupByLibrary.simpleMessage(
            "Sélectionner les variations :"),
        "selectWarrantyTime": MessageLookupByLibrary.simpleMessage(
            "Sélectionner le délai de garantie"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("Sélectionnez votre langue"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Envoyer un message"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Numéro de série"),
        "serialNumbers":
            MessageLookupByLibrary.simpleMessage("Numéro de série"),
        "serviceCharge":
            MessageLookupByLibrary.simpleMessage("Frais de service"),
        "setting": MessageLookupByLibrary.simpleMessage("Paramètres"),
        "share": MessageLookupByLibrary.simpleMessage("Partager"),
        "shipingOrOther":
            MessageLookupByLibrary.simpleMessage("Expédition/Autre"),
        "shopName": MessageLookupByLibrary.simpleMessage("Nom de la boutique"),
        "shopOpeningBalance": MessageLookupByLibrary.simpleMessage(
            "Solde d\'ouverture du magasin"),
        "show": MessageLookupByLibrary.simpleMessage("Afficher >"),
        "showLogoInInvoice": MessageLookupByLibrary.simpleMessage(
            "Afficher le logo sur la facture?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("Expédition/Service"),
        "size": MessageLookupByLibrary.simpleMessage("Taille"),
        "statistic": MessageLookupByLibrary.simpleMessage("Statistique"),
        "status": MessageLookupByLibrary.simpleMessage("Statut"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Restez à l\'avant-garde des avancées technologiques sans coûts supplémentaires. Notre mise à niveau illimitée de Pos Saas POS garantit que vous disposez toujours des derniers outils et fonctionnalités à portée de main, garantissant que votre entreprise reste à la pointe."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "Restez à l\'avant-garde des avancées technologiques sans coûts supplémentaires. Notre mise à niveau illimitée de Pos Sass POS garantit que vous disposez toujours des derniers outils et fonctionnalités à portée de main, garantissant que votre entreprise reste à la pointe."),
        "stock": MessageLookupByLibrary.simpleMessage("Stock"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("Inventaire des stocks"),
        "stockReport":
            MessageLookupByLibrary.simpleMessage("Rapport sur les stocks"),
        "stockValue": MessageLookupByLibrary.simpleMessage("Valeur des stocks"),
        "stockValues":
            MessageLookupByLibrary.simpleMessage("Valeur des Stocks"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Sous-total"),
        "subciption": MessageLookupByLibrary.simpleMessage("Abonnement"),
        "submit": MessageLookupByLibrary.simpleMessage("Soumettre"),
        "supplier": MessageLookupByLibrary.simpleMessage("Fournisseurs"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("Fournisseur dû"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("Facture fournisseur"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("Liste des fournisseurs"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("Code SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("Ventes Totales"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "Prenez une photo de permis de conduire, de carte d\'identité nationale ou de passeport"),
        "termsOfUse":
            MessageLookupByLibrary.simpleMessage("Conditions d\'utilisation"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "Le nom dit tout. Avec Pos Saas POS Unlimited, il n\'y a pas de limite à votre utilisation. Que vous traitiez quelques transactions ou que vous soyez submergé de clients, vous pouvez opérer en toute confiance, sachant que vous n\'êtes pas limité par des contraintes."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("Ce client n\'a pas de due"),
        "thisCustomerHavepreviousDue":
            MessageLookupByLibrary.simpleMessage("Ce client a un précédent dû"),
        "to": MessageLookupByLibrary.simpleMessage("À"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("Produit le plus vendu"),
        "total": MessageLookupByLibrary.simpleMessage("total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Montant total"),
        "totalDiscount":
            MessageLookupByLibrary.simpleMessage("Réduction totale"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total dû"),
        "totalDues": MessageLookupByLibrary.simpleMessage("Total des dettes"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("Total des dépenses"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("Revenu total"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("Total des articles : 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("Total des pertes"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("Total payé"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total payable"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("Paiement total sortant"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Prix ​​total"),
        "totalProduct":
            MessageLookupByLibrary.simpleMessage("Total des produits"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("Total du profit"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("Total des achats"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("Montant total du retour"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("Retour total"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total des ventes"),
        "totalSales": MessageLookupByLibrary.simpleMessage("Total des ventes"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total TVA"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("Paiement total entrant"),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaction"),
        "transactionId":
            MessageLookupByLibrary.simpleMessage("Identifiant de transaction"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("Rapport de transaction"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Réessayer"),
        "type": MessageLookupByLibrary.simpleMessage("Type"),
        "unPaid": MessageLookupByLibrary.simpleMessage("Impayé"),
        "unit": MessageLookupByLibrary.simpleMessage("Unité"),
        "unitName": MessageLookupByLibrary.simpleMessage("Nom de l\'unité"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("Prix ​​unitaire"),
        "unlimited": MessageLookupByLibrary.simpleMessage("Illimité"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("Factures illimitées"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Utilisation illimitée"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Débloquez tout le potentiel de Pos Saas POS avec des sessions de formation personnalisées dirigées par notre équipe d\'experts. Des bases aux techniques avancées, nous nous assurons que vous maîtrisez chaque aspect du système pour optimiser vos processus commerciaux."),
        "updateNow":
            MessageLookupByLibrary.simpleMessage("Mettre à jour maintenant"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "Mettez à jour votre plan d\'abord\\nLe quota de vente est dépassé."),
        "upgradeOnMobileApp": MessageLookupByLibrary.simpleMessage(
            "Mise à niveau sur l\'application mobile"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("Télécharger une image"),
        "uploadAnInvoiceLogo": MessageLookupByLibrary.simpleMessage(
            "Télécharger un logo de facture"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Télécharger un document"),
        "uploadFile":
            MessageLookupByLibrary.simpleMessage("Télécharger un fichier"),
        "userName":
            MessageLookupByLibrary.simpleMessage("Nom de l\'utilisateur"),
        "userRole":
            MessageLookupByLibrary.simpleMessage("Rôle de l\'utilisateur"),
        "userRoleName": MessageLookupByLibrary.simpleMessage(
            "Nom du rôle de l\'utilisateur"),
        "userTitle":
            MessageLookupByLibrary.simpleMessage("Titre de l\'utilisateur"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("TVA/TPS"),
        "verifyPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Vérifier le numéro de téléphone"),
        "view": MessageLookupByLibrary.simpleMessage("Voir"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Client de passage"),
        "warranty": MessageLookupByLibrary.simpleMessage("Garantie"),
        "warrantys": MessageLookupByLibrary.simpleMessage("Garanties"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "Nous devons enregistrer votre téléphone avant de commencer!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "Nous comprenons l\'importance d\'une exploitation sans faille. C\'est pourquoi notre assistance 24 heures sur 24 est disponible pour vous aider, que ce soit pour une question rapide ou une préoccupation plus complète. Connectez-vous avec nous à tout moment, n\'importe où, par téléphone ou WhatsApp pour vivre un service client inégalé."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("Prix de gros"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("Grossiste"),
        "wholesale": MessageLookupByLibrary.simpleMessage("Vente en gros"),
        "wight": MessageLookupByLibrary.simpleMessage("Poids"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("Oui, retour"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "Vous devez vous reconnecter à votre compte."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "Vous devez vous identifier avant d\'acheter des SMS"),
        "yourAllSaleList": MessageLookupByLibrary.simpleMessage(
            "Votre liste de toutes les ventes"),
        "yourAllSales":
            MessageLookupByLibrary.simpleMessage("Toutes vos ventes"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("Vous utilisez"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("Vos ventes dues"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "Vous devez vous identifier avant d\'acheter des messages"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Votre forfait"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("Votre paiement est annulé"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Votre paiement est effectué avec succès"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("Votre paiement est annulé")
      };
}
