// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a hi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'hi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("बिक्री जोड़ें"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("श्रेणी"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("चालान"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("POS बिक्री"),
        "PRICE": MessageLookupByLibrary.simpleMessage("कीमत"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("उत्पाद का नाम"),
        "PosSaasLoginPanel":
            MessageLookupByLibrary.simpleMessage("पॉज़ सास लॉगिन पैनल"),
        "QTY": MessageLookupByLibrary.simpleMessage("मात्रा"),
        "Quantity": MessageLookupByLibrary.simpleMessage("मात्रा*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("स्थिति"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("कुल मूल्य"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता शीर्षक"),
        "aboutApp":
            MessageLookupByLibrary.simpleMessage("एप्लिकेशन के बारे में"),
        "accountName": MessageLookupByLibrary.simpleMessage("खाता नाम"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("खाता नंबर"),
        "action": MessageLookupByLibrary.simpleMessage("कार्रवाई"),
        "add": MessageLookupByLibrary.simpleMessage("जोड़ें"),
        "addBrand": MessageLookupByLibrary.simpleMessage("ब्रांड जोड़ें"),
        "addCategory": MessageLookupByLibrary.simpleMessage("श्रेणी जोड़ें"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("ग्राहक जोड़ें"),
        "addDescription":
            MessageLookupByLibrary.simpleMessage("विवरण जोड़ें..."),
        "addDucument": MessageLookupByLibrary.simpleMessage("दस्तावेज़ जोड़ें"),
        "addItem": MessageLookupByLibrary.simpleMessage("आइटम जोड़ें"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("आइटम श्रेणी जोड़ें"),
        "addNew": MessageLookupByLibrary.simpleMessage("नया जोड़ें"),
        "addNewUser":
            MessageLookupByLibrary.simpleMessage("नई उपयोगकर्ता जोड़ें"),
        "addProduct": MessageLookupByLibrary.simpleMessage("उत्पाद जोड़ें"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("सफलतापूर्वक जोड़ा गया"),
        "addSupplier":
            MessageLookupByLibrary.simpleMessage("आपूर्तिकर्ता जोड़ें"),
        "addUnit": MessageLookupByLibrary.simpleMessage("इकाई जोड़ें"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("खर्च सूची जोड़ें/अपडेट करें"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("आय सूची जोड़ें/अपडेट करें"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता भूमिका जोड़ें"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("सीरियल नंबर जोड़ रहे हैं?"),
        "address": MessageLookupByLibrary.simpleMessage("पता"),
        "all": MessageLookupByLibrary.simpleMessage("सभी"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("सभी मूल विशेषताएं"),
        "alreadyHaveAnAccounts": MessageLookupByLibrary.simpleMessage(
            "क्या आपके पास पहले से ही कोई खाता है?"),
        "amount": MessageLookupByLibrary.simpleMessage("राशि"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("एंड्रॉइड और आईओएस ऐप सपोर्ट"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "क्या आप इस कोटेशन को बनाना चाहते हैं?"),
        "areYouWantToDeleteThisCustomer": MessageLookupByLibrary.simpleMessage(
            "क्या आप इस ग्राहक को हटाना चाहते हैं?"),
        "areYouWantToDeleteThisProduct": MessageLookupByLibrary.simpleMessage(
            "क्या आप इस उत्पाद को हटाना चाहते हैं"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "क्या आप इस उद्धरण को हटाना चाहते हैं?"),
        "areYouWantToReturnThisSale": MessageLookupByLibrary.simpleMessage(
            "क्या आप इस बिक्री को वापस करना चाहते हैं?"),
        "balance": MessageLookupByLibrary.simpleMessage("शेष"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("बैंक खाता मुद्रा"),
        "bankAccounts": MessageLookupByLibrary.simpleMessage("बैंक खाते"),
        "bankInformation": MessageLookupByLibrary.simpleMessage("बैंक जानकारी"),
        "bankName": MessageLookupByLibrary.simpleMessage("बैंक का नाम"),
        "between": MessageLookupByLibrary.simpleMessage("बीच"),
        "billTo": MessageLookupByLibrary.simpleMessage("बिल के लिए:"),
        "branchName": MessageLookupByLibrary.simpleMessage("शाखा का नाम"),
        "brand": MessageLookupByLibrary.simpleMessage("ब्रांड"),
        "brandName": MessageLookupByLibrary.simpleMessage("ब्रांड का नाम"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("व्यापार श्रेणी"),
        "buy": MessageLookupByLibrary.simpleMessage("खरीदें"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("प्रीमियम प्लान खरीदें"),
        "buySms": MessageLookupByLibrary.simpleMessage("एसएमएस खरीदें"),
        "calculator": MessageLookupByLibrary.simpleMessage("कैलकुलेटर:"),
        "camera": MessageLookupByLibrary.simpleMessage("कैमरा"),
        "cancel": MessageLookupByLibrary.simpleMessage("रद्द करें"),
        "capacity": MessageLookupByLibrary.simpleMessage("क्षमता"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("नकद और बैंक"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("नकद में"),
        "categories": MessageLookupByLibrary.simpleMessage("श्रेणियाँ"),
        "category": MessageLookupByLibrary.simpleMessage("श्रेणी"),
        "categoryName": MessageLookupByLibrary.simpleMessage("श्रेणी का नाम"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("परिवर्तन राशि"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("परिवर्तनीय राशि"),
        "checkWarranty":
            MessageLookupByLibrary.simpleMessage("वारंटी की जाँच करें"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("एक योजना चुनें"),
        "collectDue":
            MessageLookupByLibrary.simpleMessage("बकाया एकत्र करें >"),
        "color": MessageLookupByLibrary.simpleMessage("रंग"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("कंपनी का नाम"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("कंपनी पता"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("कंपनी विवरण"),
        "companyEmailAddress":
            MessageLookupByLibrary.simpleMessage("कंपनी ईमेल पता"),
        "companyName": MessageLookupByLibrary.simpleMessage("कंपनी का नाम"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("कंपनी का फोन नंबर"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("कंपनी वेबसाइट यूआरएल"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड की पुष्टि करें"),
        "continu": MessageLookupByLibrary.simpleMessage("जारी रखें"),
        "convertToSale":
            MessageLookupByLibrary.simpleMessage("बिक्री में कनवर्ट करें"),
        "create": MessageLookupByLibrary.simpleMessage("बनाना"),
        "createPayment": MessageLookupByLibrary.simpleMessage("भुगतान बनाएँ"),
        "createdBy": MessageLookupByLibrary.simpleMessage("द्वारा बनाया गया"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("क्रिएटिव हब"),
        "currency": MessageLookupByLibrary.simpleMessage("मुद्रा"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("वर्तमान योजना"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("कस्टम चालान ब्रांडिंग"),
        "customer": MessageLookupByLibrary.simpleMessage("ग्राहक"),
        "customerDue": MessageLookupByLibrary.simpleMessage("ग्राहक बकाया"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("ग्राहक चालान"),
        "customerList": MessageLookupByLibrary.simpleMessage("ग्राहक सूची"),
        "customerName": MessageLookupByLibrary.simpleMessage("ग्राहक का नाम"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("महीने का ग्राहक"),
        "customerType": MessageLookupByLibrary.simpleMessage("ग्राहक प्रकार"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("ग्राहक: वॉक-इन कस्टमर"),
        "customers": MessageLookupByLibrary.simpleMessage("ग्राहक"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("दैनिक संग्रह"),
        "dailySales": MessageLookupByLibrary.simpleMessage("दैनिक बिक्री"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("दैनिक लेनदेन"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("डैशबोर्ड"),
        "date": MessageLookupByLibrary.simpleMessage("तारीख"),
        "dateTime": MessageLookupByLibrary.simpleMessage("दिनांक समय"),
        "dealer": MessageLookupByLibrary.simpleMessage("डीलर"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("डीलर मूल्य"),
        "delete": MessageLookupByLibrary.simpleMessage("हटाएँ"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("डिलीवरी शुल्क"),
        "description": MessageLookupByLibrary.simpleMessage("विवरण"),
        "details": MessageLookupByLibrary.simpleMessage("विवरण >"),
        "discount": MessageLookupByLibrary.simpleMessage("छूट"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("छूट मूल्य"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("डाउनलोड पीडीएफ"),
        "due": MessageLookupByLibrary.simpleMessage("देय"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("बकाया राशि"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "यदि उपलब्ध हो तो देय राशि यहां दिखाई देगी"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("देय संग्रह"),
        "dueList": MessageLookupByLibrary.simpleMessage("देय सूची"),
        "dueTransaction": MessageLookupByLibrary.simpleMessage("देय लेनदेन"),
        "edit": MessageLookupByLibrary.simpleMessage("संपादित करें"),
        "editOrAddSerial": MessageLookupByLibrary.simpleMessage(
            "श्रृंखला को संपादित या जोड़ें:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("अपना प्रोफ़ाइल संपादित करें"),
        "email": MessageLookupByLibrary.simpleMessage("ईमेल"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("राशि दर्ज करें"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("ब्रांड का नाम दर्ज करें"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("श्रेणी का नाम दर्ज करें"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("कंपनी विवरण दर्ज करें"),
        "enterCompanyEmailAddress":
            MessageLookupByLibrary.simpleMessage("कंपनी ईमेल पता दर्ज करें"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("कंपनी का फोन नंबर दर्ज करें"),
        "enterCompanyWebsiteUrl": MessageLookupByLibrary.simpleMessage(
            "कंपनी वेबसाइट यूआरएल दर्ज करें"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("ग्राहक का नाम दर्ज करें"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("डीलर मूल्य दर्ज करें"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("छूट मूल्य दर्ज करें"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("खर्च श्रेणी दर्ज करें"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("खर्च की तारीख दर्ज करें"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("आय श्रेणी दर्ज करें"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("आय की तारीख दर्ज करें"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("निर्माता का नाम दर्ज करें"),
        "enterName": MessageLookupByLibrary.simpleMessage("नाम दर्ज करें"),
        "enterNames": MessageLookupByLibrary.simpleMessage("नाम दर्ज करें"),
        "enterNote": MessageLookupByLibrary.simpleMessage("ध्यान दें"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("प्रारंभिक शेष दर्ज करें"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("भुगतान की गई राशि दर्ज करें"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड दर्ज करें"),
        "enterPayingAmount": MessageLookupByLibrary.simpleMessage(
            "भुगतान करने की राशि दर्ज करें"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("मूल्य दर्ज करें"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("उत्पाद की क्षमता दर्ज करें"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("उत्पाद कोड दर्ज करें"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("उत्पाद का रंग दर्ज करें"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("उत्पाद का नाम दर्ज करें"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("उत्पाद की मात्रा दर्ज करें"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("उत्पाद का आकार दर्ज करें"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("उत्पाद प्रकार दर्ज करें"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("उत्पाद इकाई दर्ज करें"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("उत्पाद का वजन दर्ज करें"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("खरीद मूल्य दर्ज करें"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("संदर्भ संख्या दर्ज करें"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("बिक्री मूल्य दर्ज करें"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("क्रमांक दर्ज करें"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("संदेश सामग्री दर्ज करें"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("स्टॉक राशि दर्ज करें"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("लेन-देन आईडी दर्ज करें"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("इकाई का नाम दर्ज करें"),
        "enterUserRoleName": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता भूमिका नाम दर्ज करें"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता शीर्षक दर्ज करें"),
        "enterWarranty":
            MessageLookupByLibrary.simpleMessage("वारंटी दर्ज करें"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("थोक मूल्य दर्ज करें"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("अपना राशि दर्ज करें"),
        "enterYourAddress":
            MessageLookupByLibrary.simpleMessage("अपना पता दर्ज करें"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("अपना कंपनी पता दर्ज करें"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("अपना कंपनी का नाम दर्ज करें"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("अपना कंपनी का नाम दर्ज करें"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("अपना ईमेल पता दर्ज करें"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("अपना पासवर्ड दर्ज करें"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "अपना पासवर्ड फिर से दर्ज करें"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("अपना फ़ोन नंबर दर्ज करें"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("अपना दुकान का नाम दर्ज करें"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("श्रेणी का नाम दर्ज करें"),
        "expense": MessageLookupByLibrary.simpleMessage("खर्च"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("खर्च की तारीख"),
        "expenseDetails": MessageLookupByLibrary.simpleMessage("खर्च विवरण"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("खर्च के लिए"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("खर्च श्रेणी सूची"),
        "expenses": MessageLookupByLibrary.simpleMessage("व्यय"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "महीने के पाँच शीर्ष खरीददार उत्पाद"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("असीमित उपयोग के लिए"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड भूल गए?"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("निःशुल्क डेटा बैकअप"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("मुफ़्त लाइफ़टाइम अपडेट"),
        "freePackage": MessageLookupByLibrary.simpleMessage("मुफ्त पैकेज"),
        "freePlan": MessageLookupByLibrary.simpleMessage("मुफ्त प्लान"),
        "getStarted": MessageLookupByLibrary.simpleMessage("शुरू करें"),
        "govermentId": MessageLookupByLibrary.simpleMessage("सरकार आईडी"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("कुल योग"),
        "hold": MessageLookupByLibrary.simpleMessage("होल्ड"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("होल्ड संख्या"),
        "identityVerify": MessageLookupByLibrary.simpleMessage("पहचान सत्यापन"),
        "inc": MessageLookupByLibrary.simpleMessage("आय"),
        "income": MessageLookupByLibrary.simpleMessage("आय"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("आय श्रेणी"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("आय श्रेणी सूची"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("आय की तारीख"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("आय विवरण"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("आय के लिए"),
        "incomeList": MessageLookupByLibrary.simpleMessage("आय सूची"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("स्टॉक बढ़ाएँ"),
        "instantPrivacy":
            MessageLookupByLibrary.simpleMessage("तुरंत गोपनीयता"),
        "invoice": MessageLookupByLibrary.simpleMessage("चालान"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("चालान:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("Invoice NO.."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("चालान संख्या"),
        "item": MessageLookupByLibrary.simpleMessage("आइटम"),
        "itemName": MessageLookupByLibrary.simpleMessage("आइटम नाम"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("केवाईसी सत्यापन"),
        "ledgeDetails": MessageLookupByLibrary.simpleMessage("लीज विवरण"),
        "ledger": MessageLookupByLibrary.simpleMessage("लेजर"),
        "left": MessageLookupByLibrary.simpleMessage("बाएं"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("ऋण खाते"),
        "logOut": MessageLookupByLibrary.simpleMessage("लॉग आउट"),
        "login": MessageLookupByLibrary.simpleMessage("लॉग इन करें"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("चालान में लोगो का स्थान?"),
        "loss": MessageLookupByLibrary.simpleMessage("हानि"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("हानि/लाभ"),
        "lossminus": MessageLookupByLibrary.simpleMessage("हानि(-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("कम स्टॉक"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("कम स्टॉक"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "ब्रांडेड चालान के साथ अपने ग्राहकों पर स्थायी प्रभाव डालें। हमारा अनलिमिटेड अपग्रेड आपके चालान को अनुकूलित करने, एक पेशेवर स्पर्श जोड़ने का अनूठा लाभ प्रदान करता है जो आपके ब्रांड की पहचान को मजबूत करता है और ग्राहक वफादारी को बढ़ावा देता है।"),
        "manufacturer": MessageLookupByLibrary.simpleMessage("निर्माता"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("Pos Saas लॉगिन पैनल"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("Pos Saas साइनअप पैनल"),
        "mobilePlusDesktop": MessageLookupByLibrary.simpleMessage(
            "मोबाइल एप्लिकेशन\n+\nडेस्कटॉप"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("रुपये की रसीद"),
        "nam": MessageLookupByLibrary.simpleMessage("नाम*"),
        "name": MessageLookupByLibrary.simpleMessage("नाम"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("नाम या कोड या श्रेणी"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("नए ग्राहक"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("नए ग्राहक"),
        "newIncome": MessageLookupByLibrary.simpleMessage("नई आय"),
        "no": MessageLookupByLibrary.simpleMessage("नहीं"),
        "noConnection":
            MessageLookupByLibrary.simpleMessage("कोई कनेक्शन नहीं"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("कोई ग्राहक नहीं मिला"),
        "noDueTransantionFound":
            MessageLookupByLibrary.simpleMessage("कोई बकाया लेनदेन नहीं मिला"),
        "noExpenseCategoryFound":
            MessageLookupByLibrary.simpleMessage("कोई खर्च श्रेणी नहीं मिली"),
        "noIncomeCategoryFound":
            MessageLookupByLibrary.simpleMessage("कोई आय श्रेणी नहीं मिली"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("कोई आय नहीं मिली"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("कोई चालान नहीं मिला"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("कोई उत्पाद नहीं मिला"),
        "noPurchaseTransactionFound":
            MessageLookupByLibrary.simpleMessage("कोई खरीद लेनदेन नहीं मिला"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("कोई उद्धरण नहीं मिला"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("कोई रिपोर्ट नहीं मिली"),
        "noSaleTransaactionFound":
            MessageLookupByLibrary.simpleMessage("कोई बिक्री लेनदेन नहीं मिला"),
        "noSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("कोई क्रमांक नहीं मिला"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("कोई आपूर्तिकर्ता नहीं मिला"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("कोई लेनदेन नहीं मिला"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("कोई उपयोगकर्ता नहीं मिला"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "कोई उपयोगकर्ता भूमिका नहीं मिली"),
        "nosSerialNumberFound":
            MessageLookupByLibrary.simpleMessage("कोई क्रमांक नहीं मिला"),
        "note": MessageLookupByLibrary.simpleMessage("ध्यान दें"),
        "ok": MessageLookupByLibrary.simpleMessage("ठीक है"),
        "openCheques": MessageLookupByLibrary.simpleMessage("खुले चेक"),
        "openingBalance": MessageLookupByLibrary.simpleMessage("प्रारंभिक शेष"),
        "orDragAndDropPng": MessageLookupByLibrary.simpleMessage(
            "या PNG, JPG को ड्रैग एंड ड्रॉप करें"),
        "orders": MessageLookupByLibrary.simpleMessage("आदेश"),
        "other": MessageLookupByLibrary.simpleMessage("अन्य"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("अन्य आय"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("पैकेज सुविधा"),
        "paid": MessageLookupByLibrary.simpleMessage("भुगतान किया"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("भुगतान की गई राशि"),
        "partyName": MessageLookupByLibrary.simpleMessage("पक्ष का नाम"),
        "partyType": MessageLookupByLibrary.simpleMessage("पक्ष का प्रकार"),
        "password": MessageLookupByLibrary.simpleMessage("पासवर्ड"),
        "payCash": MessageLookupByLibrary.simpleMessage("नकद भुगतान"),
        "payable": MessageLookupByLibrary.simpleMessage("देय"),
        "payingAmount":
            MessageLookupByLibrary.simpleMessage("भुगतान की जाने वाली राशि"),
        "payment": MessageLookupByLibrary.simpleMessage("भुगतान"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("भुगतान इन"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("भुगतान आउट"),
        "paymentType": MessageLookupByLibrary.simpleMessage("भुगतान का प्रकार"),
        "paymentTypes":
            MessageLookupByLibrary.simpleMessage("भुगतान का प्रकार"),
        "phone": MessageLookupByLibrary.simpleMessage("फ़ोन"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("फ़ोन नंबर"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("फ़ोन सत्यापन"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("कृपया एक बिक्री जोड़ें"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("कृपया ग्राहक जोड़ें"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "कृपया अपनी इंटरनेट कनेक्टिविटी जांचें"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "कृपया अपना मोबाइल ऐप डाउनलोड करें और डेस्कटॉप संस्करण का उपयोग करने के लिए एक पैकेज को सब्सक्राइब करें"),
        "pleaseEnterProductStock": MessageLookupByLibrary.simpleMessage(
            "कृपया उत्पाद स्टॉक दर्ज करें"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("कृपया मान्य डेटा दर्ज करें"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("कृपया एक ग्राहक का चयन करें"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("कृपया मान्य डेटा दर्ज करें"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("पॉज़ सास सिंगअप पैनल"),
        "practies": MessageLookupByLibrary.simpleMessage("अभ्यास"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("प्रीमियम ग्राहक सहायता"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("प्रीमियम प्लान"),
        "preview": MessageLookupByLibrary.simpleMessage("पूर्वावलोकन"),
        "previousDue": MessageLookupByLibrary.simpleMessage("पिछला देय:"),
        "price": MessageLookupByLibrary.simpleMessage("कीमत"),
        "print": MessageLookupByLibrary.simpleMessage("छापें"),
        "printInvoice":
            MessageLookupByLibrary.simpleMessage("चालान मुद्रित करें"),
        "printPdf": MessageLookupByLibrary.simpleMessage("पीडीएफ प्रिंट करें"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("गोपनीयता नीति"),
        "product": MessageLookupByLibrary.simpleMessage("उत्पाद"),
        "productCategory":
            MessageLookupByLibrary.simpleMessage("उत्पाद श्रेणी"),
        "productCod": MessageLookupByLibrary.simpleMessage("उत्पाद कोड*"),
        "productColor": MessageLookupByLibrary.simpleMessage("उत्पाद का रंग"),
        "productList": MessageLookupByLibrary.simpleMessage("उत्पाद सूची"),
        "productNam": MessageLookupByLibrary.simpleMessage("उत्पाद का नाम*"),
        "productName": MessageLookupByLibrary.simpleMessage("उत्पाद का नाम"),
        "productSize": MessageLookupByLibrary.simpleMessage("उत्पाद का आकार"),
        "productStock": MessageLookupByLibrary.simpleMessage("उत्पाद स्टॉक"),
        "productType": MessageLookupByLibrary.simpleMessage("उत्पाद प्रकार"),
        "productUnit": MessageLookupByLibrary.simpleMessage("उत्पाद इकाई"),
        "productWaranty": MessageLookupByLibrary.simpleMessage("उत्पाद वारंटी"),
        "productWeight": MessageLookupByLibrary.simpleMessage("उत्पाद का वजन"),
        "productcapacity":
            MessageLookupByLibrary.simpleMessage("उत्पाद की क्षमता"),
        "prof": MessageLookupByLibrary.simpleMessage("प्रोफाइल"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("प्रोफ़ाइल संपादन"),
        "profit": MessageLookupByLibrary.simpleMessage("लाभ"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("लाभ (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("लाभ (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("खरीद"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("खरीदारी सूची"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("प्रीमियम योजना खरीदें"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("खरीद मूल्य"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("खरीदारी लेनदेन"),
        "quantity": MessageLookupByLibrary.simpleMessage("मात्रा"),
        "quotation": MessageLookupByLibrary.simpleMessage("कोटेशन"),
        "quotationList": MessageLookupByLibrary.simpleMessage("उद्धरण सूची"),
        "recentSale": MessageLookupByLibrary.simpleMessage("हाल के बिक्री"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("प्राप्त राशि"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("संदर्भ संख्या"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("संदर्भ संख्या"),
        "registration": MessageLookupByLibrary.simpleMessage("पंजीकरण"),
        "remaining": MessageLookupByLibrary.simpleMessage("शेष: "),
        "remainingBalance": MessageLookupByLibrary.simpleMessage("शेष राशि"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("शेष बकाया"),
        "reports": MessageLookupByLibrary.simpleMessage("रिपोर्ट्स"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("अपना पासवर्ड रीसेट करें"),
        "retailer": MessageLookupByLibrary.simpleMessage("खुदरा विक्रेता"),
        "revenue": MessageLookupByLibrary.simpleMessage("राजस्व"),
        "right": MessageLookupByLibrary.simpleMessage("दाएं"),
        "sAmount": MessageLookupByLibrary.simpleMessage("बिक्री मात्रा"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "अपने व्यावसायिक डेटा को सहजता से सुरक्षित रखें। हमारे पॉस सास पीओएस अनलिमिटेड अपग्रेड में मुफ्त डेटा बैकअप शामिल है, यह सुनिश्चित करते हुए कि आपकी बहुमूल्य जानकारी किसी भी अप्रत्याशित घटना से सुरक्षित है। उस पर ध्यान केंद्रित करें जो वास्तव में मायने रखता है - आपके व्यवसाय की वृद्धि।"),
        "sale": MessageLookupByLibrary.simpleMessage("बिक्री"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("बिक्री राशि"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("बिक्री विवरण"),
        "saleList": MessageLookupByLibrary.simpleMessage("बिक्री सूची"),
        "salePrice": MessageLookupByLibrary.simpleMessage("बिक्री मूल्य"),
        "salePrices": MessageLookupByLibrary.simpleMessage("बिक्री मूल्य*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("बिक्री वापसी"),
        "saleTransaction":
            MessageLookupByLibrary.simpleMessage("बिक्री लेनदेन"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "बिक्री लेनदेन (उद्धरण बिक्री इतिहास)"),
        "sales": MessageLookupByLibrary.simpleMessage("बिक्री"),
        "salesList":
            MessageLookupByLibrary.simpleMessage("बेच दी गई वस्तुओं की सूची"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("सहेजें और प्रकाशित करें"),
        "saveAndPublished":
            MessageLookupByLibrary.simpleMessage("सहेजें और प्रकाशित करें"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("बदलाव सहेजें"),
        "search": MessageLookupByLibrary.simpleMessage("खोज ......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("कुछ भी खोजें..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("चालान द्वारा खोजें..."),
        "searchByInvoiceOrName":
            MessageLookupByLibrary.simpleMessage("चालान या नाम से खोजें"),
        "searchByName": MessageLookupByLibrary.simpleMessage("नाम से खोजें"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("नाम या फ़ोन से खोजें..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("क्रमांक खोजें"),
        "selectParties":
            MessageLookupByLibrary.simpleMessage("पक्षों का चयन करें"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("उत्पाद ब्रांड चुनें"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("क्रमांक चुनें"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("भिन्नताएँ चुनें:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("वारंटी समय चुनें"),
        "selectYourLanguage":
            MessageLookupByLibrary.simpleMessage("अपनी भाषा का चयन करें"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("संदेश भेजें"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("क्रमांक"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("क्रमांक"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("सेवा शुल्क"),
        "setting": MessageLookupByLibrary.simpleMessage("सेटिंग"),
        "share": MessageLookupByLibrary.simpleMessage("साझा करें"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("शिपिंग/अन्य"),
        "shopName": MessageLookupByLibrary.simpleMessage("दुकान का नाम"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("दुकान का प्रारंभिक शेष"),
        "show": MessageLookupByLibrary.simpleMessage("दिखाओ >"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("क्या चालान में लोगो दिखाएं?"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("शिपिंग/सेवाएं"),
        "size": MessageLookupByLibrary.simpleMessage("आकार"),
        "statistic": MessageLookupByLibrary.simpleMessage("सांख्यिकी"),
        "status": MessageLookupByLibrary.simpleMessage("स्थिति"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "बिना किसी अतिरिक्त लागत के तकनीकी प्रगति में सबसे आगे रहें। हमारा पीओ सास पीओएस अनलिमिटेड अपग्रेड यह सुनिश्चित करता है कि आपकी उंगलियों पर हमेशा नवीनतम उपकरण और सुविधाएं हों, यह गारंटी देता है कि आपका व्यवसाय अत्याधुनिक बना रहेगा।"),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "बिना किसी अतिरिक्त लागत के तकनीकी प्रगति में सबसे आगे रहें। हमारा पॉस सैस पीओएस अनलिमिटेड अपग्रेड यह सुनिश्चित करता है कि आपकी उंगलियों पर हमेशा नवीनतम उपकरण और सुविधाएं हों, यह गारंटी देता है कि आपका व्यवसाय अत्याधुनिक बना रहेगा।"),
        "stock": MessageLookupByLibrary.simpleMessage("स्टॉक"),
        "stockInventory":
            MessageLookupByLibrary.simpleMessage("स्टॉक इन्वेंट्री"),
        "stockReport": MessageLookupByLibrary.simpleMessage("स्टॉक रिपोर्ट"),
        "stockValue": MessageLookupByLibrary.simpleMessage("स्टॉक मूल्य"),
        "stockValues": MessageLookupByLibrary.simpleMessage("स्टॉक मूल्य"),
        "subTotal": MessageLookupByLibrary.simpleMessage("उप-योग"),
        "subciption": MessageLookupByLibrary.simpleMessage("सदस्यता"),
        "submit": MessageLookupByLibrary.simpleMessage("जमा करें"),
        "supplier": MessageLookupByLibrary.simpleMessage("आपूर्तिकर्ता"),
        "supplierDue":
            MessageLookupByLibrary.simpleMessage("आपूर्तिकर्ता बकाया"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("आपूर्तिकर्ता चालान"),
        "supplierList":
            MessageLookupByLibrary.simpleMessage("आपूर्तिकर्ता सूची"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT कोड"),
        "tSale": MessageLookupByLibrary.simpleMessage("कुल बिक्री"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "ड्राइविंग लाइसेंस, राष्ट्रीय पहचान पत्र या पासपोर्ट की फोटो लें"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("उपयोग की शर्तें"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "नाम से सब कुछ पता चलता है। पॉज़ सास पीओएस अनलिमिटेड के साथ, आपके उपयोग पर कोई सीमा नहीं है। चाहे आप मुट्ठी भर लेन-देन कर रहे हों या ग्राहकों की भीड़ का अनुभव कर रहे हों, आप आत्मविश्वास के साथ काम कर सकते हैं, यह जानते हुए कि आप सीमाओं से बंधे नहीं हैं"),
        "thisCustmerHasNoDue": MessageLookupByLibrary.simpleMessage(
            "इस ग्राहक का कोई देय नहीं है"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "इस ग्राहक के पास पहले से ही देय है"),
        "to": MessageLookupByLibrary.simpleMessage("से"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("शीर्ष बिक्री उत्पाद"),
        "total": MessageLookupByLibrary.simpleMessage("कुल"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("कुल राशि"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("कुल छूट"),
        "totalDue": MessageLookupByLibrary.simpleMessage("कुल बकाया"),
        "totalDues": MessageLookupByLibrary.simpleMessage("कुल बकाया"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("कुल खर्च"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("कुल आय"),
        "totalItem2": MessageLookupByLibrary.simpleMessage("कुल आइटम: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("कुल हानि"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("कुल भुगतान"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("कुल देय"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("कुल भुगतान आउट"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("कुल मूल्य"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("कुल उत्पाद"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("कुल लाभ"),
        "totalPurchase": MessageLookupByLibrary.simpleMessage("कुल खरीद"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("कुल वापसी राशि"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("कुल रिटर्न"),
        "totalSale": MessageLookupByLibrary.simpleMessage("कुल बिक्री"),
        "totalSales": MessageLookupByLibrary.simpleMessage("कुल बिक्री"),
        "totalVat": MessageLookupByLibrary.simpleMessage("कुल वैट"),
        "totalpaymentIn": MessageLookupByLibrary.simpleMessage("कुल भुगतान इन"),
        "transaction": MessageLookupByLibrary.simpleMessage("लेनदेन"),
        "transactionId": MessageLookupByLibrary.simpleMessage("लेनदेन आईडी"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("लेनदेन रिपोर्ट"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("पुनः प्रयास करें"),
        "type": MessageLookupByLibrary.simpleMessage("प्रकार"),
        "unPaid": MessageLookupByLibrary.simpleMessage("अनपेड"),
        "unit": MessageLookupByLibrary.simpleMessage("इकाई"),
        "unitName": MessageLookupByLibrary.simpleMessage("इकाई का नाम"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("इकाई मूल्य"),
        "unlimited": MessageLookupByLibrary.simpleMessage("असीमित"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("अनसीमित चालान"),
        "unlimitedUsage": MessageLookupByLibrary.simpleMessage("असीमित उपयोग"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "हमारी विशेषज्ञ टीम के नेतृत्व में व्यक्तिगत प्रशिक्षण सत्रों के साथ पॉज़ सास पीओएस की पूरी क्षमता को अनलॉक करें। बुनियादी बातों से लेकर उन्नत तकनीकों तक, हम सुनिश्चित करते हैं कि आप अपनी व्यावसायिक प्रक्रियाओं को अनुकूलित करने के लिए सिस्टम के हर पहलू का उपयोग करने में अच्छी तरह से वाकिफ हैं।"),
        "updateNow": MessageLookupByLibrary.simpleMessage("अभी अद्यतन करें"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "अपना प्लान पहले अपडेट करें\\nबिक्री सीमा खत्म हो गई है।"),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("मोबाइल ऐप पर अपग्रेड करें"),
        "uploadAImage":
            MessageLookupByLibrary.simpleMessage("एक छवि अपलोड करें"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("एक चालान लोगो अपलोड करें"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("दस्तावेज़ अपलोड करें"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("फ़ाइल अपलोड करें"),
        "userName": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता नाम"),
        "userRole": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता भूमिका"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता भूमिका नाम"),
        "userTitle": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता शीर्षक"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage("वैट/जीएसटी"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("फ़ोन नंबर सत्यापित करें"),
        "view": MessageLookupByLibrary.simpleMessage("देखें"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("वॉक इन कस्टमर"),
        "warranty": MessageLookupByLibrary.simpleMessage("वारंटी"),
        "warrantys": MessageLookupByLibrary.simpleMessage("वारंटी"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "हमें शुरू करने से पहले आपका फ़ोन पंजीकृत करना होगा!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "हम निर्बाध संचालन के महत्व को समझते हैं। इसीलिए हमारा चौबीसों घंटे समर्थन आपकी सहायता के लिए उपलब्ध है, चाहे वह त्वरित प्रश्न हो या व्यापक चिंता। बेजोड़ ग्राहक सेवा का अनुभव लेने के लिए कभी भी, कहीं भी कॉल या व्हाट्सएप के माध्यम से हमसे जुड़ें।"),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("थोक मूल्य"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("थोक व्यापारी"),
        "wholesale": MessageLookupByLibrary.simpleMessage("थोक"),
        "wight": MessageLookupByLibrary.simpleMessage("वजन"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("हां वापसी"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "आपको अपने खाते पर पुनः लॉगइन करना होगा।"),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "आपको संदेश खरीदने से पहले सत्यापन करने की आवश्यकता है"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("आपकी सभी बिक्री सूची"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("आपकी सभी बिक्री"),
        "yourAreUsing":
            MessageLookupByLibrary.simpleMessage("आप उपयोग कर रहे हैं"),
        "yourDueSales": MessageLookupByLibrary.simpleMessage("आपके देय बिक्री"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "आपको खरीदने से पहले संदेश सत्यापित करने की आवश्यकता है"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("आपका पैकेज"),
        "yourPaymentIsCancelled": MessageLookupByLibrary.simpleMessage(
            "आपका भुगतान रद्द कर दिया गया है"),
        "yourPaymentIsSuccessfully": MessageLookupByLibrary.simpleMessage(
            "आपका भुगतान सफलतापूर्वक हो गया है"),
        "yourPaymentIscancelled": MessageLookupByLibrary.simpleMessage(
            "आपका भुगतान रद्द कर दिया गया है")
      };
}
